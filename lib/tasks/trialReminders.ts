import { db } from "@/lib/db";
import { user, subscription, plan } from "@/lib/db/schema";
import { eq, and, lt, gt } from "drizzle-orm";
import { Logger } from '@/lib/utils/Logger';
import { stripe } from "@/lib/stripe";
import { EmailService } from "@/lib/email/service";

export async function sendExtendedTrialReminders() {
  try {
    const now = new Date();
    
    // Find users whose trial ends in 10 days (day 20 of 30)
    const tenDaysFromNow = new Date(now);
    tenDaysFromNow.setDate(tenDaysFromNow.getDate() + 10);
    
    const elevenDaysFromNow = new Date(now);
    elevenDaysFromNow.setDate(elevenDaysFromNow.getDate() + 11);
    
    // Get all users with extended trials ending in 10 days
    const usersToRemind = await db
      .select({
        userId: subscription.userId,
        email: user.email,
        firstname: user.firstname,
        lastname: user.lastname,
        stripeCustomerId: user.stripeCustomerId,
        subscriptionId: subscription.id,
        trialEndsAt: subscription.trialEndsAt,
      })
      .from(subscription)
      .innerJoin(user, eq(subscription.userId, user.id))
      .where(
        and(
          eq(subscription.status, "active"),
          eq(subscription.extendedTrial, true),
          gt(subscription.trialEndsAt, tenDaysFromNow),
          lt(subscription.trialEndsAt, elevenDaysFromNow)
        )
      );
    
    // Get premium plan for checkout
    const [premiumPlan] = await db
      .select()
      .from(plan)
      .where(eq(plan.name, "premium"))
      .limit(1);
    
    if (!premiumPlan || !premiumPlan.stripePriceId) {
      throw new Error("Premium plan or price ID not found");
    }
   
    const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000';
    
    // Send reminder emails
    for (const userToRemind of usersToRemind) {
      try {
        // Ensure user has a Stripe customer ID
        let stripeCustomerId = userToRemind.stripeCustomerId;
        
        if (!stripeCustomerId) {
          // Create a new Stripe customer
          const customer = await stripe.customers.create({
            email: userToRemind.email,
            name: `${userToRemind.firstname || ''} ${userToRemind.lastname || ''}`.trim() || userToRemind.email,
            metadata: { userId: userToRemind.userId, email: userToRemind.email },
          });
          
          // Update user with new Stripe customer ID
          await db
            .update(user)
            .set({ stripeCustomerId: customer.id })
            .where(eq(user.id, userToRemind.userId));
          
          stripeCustomerId = customer.id;
          Logger.info(`Created Stripe customer for user ${userToRemind.userId}: ${customer.id}`);
        }
        
        // Create a checkout session that doesn't expire for 30 days
        const session = await stripe.checkout.sessions.create({
          customer: stripeCustomerId,
          payment_method_types: ['card'],
          line_items: [
            {
              price: premiumPlan.stripePriceId,
              quantity: 1,
            },
          ],
          mode: 'subscription',
          success_url: `${baseUrl}/subscription/success?session_id={CHECKOUT_SESSION_ID}`,
          cancel_url: `${baseUrl}/`,
          expires_at: Math.floor(Date.now() / 1000) + (30 * 24 * 60 * 60), // 30 days
          metadata: { 
            userId: userToRemind.userId,
            planId: premiumPlan.id,
            subscriptionId: userToRemind.subscriptionId,
            source: 'extended_trial_reminder'
          }
        });
        
        // Generate checkout URL
        const checkoutUrl = session.url;
        
        if (!checkoutUrl) {
          throw new Error("Failed to generate checkout URL");
        }
        
        // Send email with Stripe checkout URL
        const emailResult = await EmailService.sendTrialReminderEmail(userToRemind.email, checkoutUrl);
        
        if (emailResult.success) {
          Logger.info(`Sent extended trial reminder email to ${userToRemind.email} with checkout URL: ${checkoutUrl}`);
        } else {
          Logger.error(`Failed to send trial reminder email to ${userToRemind.email}:`, emailResult.error);
        }
      } catch (error) {
        Logger.error(`Failed to process reminder for user ${userToRemind.userId}:`, error);
      }
    }
    
    return usersToRemind.length;
  } catch (error) {
    Logger.error("Failed to send extended trial reminder emails", error);
    throw error;
  }
}

