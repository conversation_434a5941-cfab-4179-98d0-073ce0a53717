/**
 * Shared event type definitions for analytics
 */

export enum SiteBehaviorEvent {
  SIGN_UP = 'User Signed Up',
  LOGIN = 'User Logged In',
  SUGGESTED_ACTION = 'User on Suggested Action',
  VIDEO_TUTORIAL_CLICK = "video_tutorial_click",
  GUIDES_PAGE_CLICK = "guides_page_click",
  AMPLIFY_BUTTON_CLICK = 'User Clicked Amplify Button',
  PLAYBOOK_FEATURE = 'Playbook Feature',
  PROMPT_ARCHITECT_FINISH = 'User Finished Prompt Architect',
  RESEARCH_MODE_CLICK = 'User Clicked Research Mode',
  ENHANCED_DOC_READER_CLICK = 'User Clicked Enhanced Doc Reader',
  PROFESSIONAL_PREFERENCES = 'Professional Preferences Feature',
  MODEL_CHANGE = 'User Changed AI Model',
  CHAT_SHARE = 'User Shared Chat',
  TRANSFER_CHAT = 'User Transferred Chat',
  PIN_CHAT = 'User Pin Chat',
  UNPIN_CHAT = 'User Unpin Chat',
  SAVE_TAG = 'User Create a Tag',
  DELETE_CHAT = 'User Delete a Chat',
  CREATE_PROMPT = "User Create a Prompt",
  TOGGLE_THEME = "Toggle Theme",
  DELETE_PROMPT = "User Delete Prompt",
  VERIFY_EMAIL = "User Verify Email"

}

export enum ServerErrorEvent {
  FILE_SIZE_EXCEEDED = 'File Size Exceeded',
  FILE_UPLOAD_FAILED = 'File Upload failed',
  FILE_DELETE_FAILED = "File Delete Failed",
  SERVER_CRASH = 'Server Crash',
  CHAT_ROUTE_ERROR = 'Chat Route Error',
  CHAT_DELETE_ERROR = 'Chat Delete Error',
  GEMINI_PALM_API = 'Error With Gemini Palm API',
  GEMINI_VERTEX_API = 'Error With Gemini Vertex API',
  TIMEOUT_ERROR = 'Request Timeout',
}

export enum ServerSuccessEvent {
  FILE_UPLOAD_SUCCESS = 'File Upload Succeeded',
  CHAT_REQUEST_RECEIVED = 'Chat Route Request Received',
  CHAT_REQUEST_SERVED = 'Chat Route Request Served',
}

export enum PerformanceEvent {
  CHAT_ROUTE = 'Chat Resp Time',
  GET_INFORMATION_FUNC = 'RAG Fn Time',
  CREATE_DOC_FUNC = 'Create Doc Fn Time',
  UPDATE_DOC_FUNC = 'Update Doc Fn Time',
  REQUEST_SUGGESTION_FUNC = 'Req Suggestion Fn Time',
  GENERAL_PERFORMANCE_FUNC = 'General Fn Perf Time'
}

export enum SubscriptionEvent {
  SUBSCRIPTION_CREATED = 'User Subscribed to Premium Plan',
  SUBSCRIPTION_CANCELLED = 'User Cancelled Subscription',
  SUBSCRIPTION_DELETED = 'Subscription Deleted'
}

// Type for all event types combined
export type EventType =
  | SiteBehaviorEvent
  | ServerErrorEvent
  | ServerSuccessEvent
  | SubscriptionEvent
  | PerformanceEvent;
