/**
 * Markdown Structure Detection Utility
 * Provides functions to detect markdown structures and safe insertion points for citations
 */

export interface MarkdownStructure {
  type: 'paragraph' | 'heading' | 'code_block' | 'table' | 'blockquote' | 'list_item' | 'other';
  start: number;
  end: number;
  level?: number; // For headings and lists
  content: string;
}

export interface SafeInsertionPoint {
  position: number;
  isAfterSentence: boolean;
  isEndOfParagraph: boolean;
  context: string;
}

export class MarkdownStructureDetector {
  /**
   * Analyzes markdown content and identifies structural elements
   */
  static analyzeStructure(content: string): MarkdownStructure[] {
    const structures: MarkdownStructure[] = [];
    const lines = content.split('\n');
    let currentPosition = 0;
    let inCodeBlock = false;
    let codeBlockStart = -1;
    let inTable = false;
    let tableStart = -1;

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      const lineStart = currentPosition;
      const lineEnd = currentPosition + line.length;

      // Check for code block boundaries
      if (line.trim().startsWith('```')) {
        if (!inCodeBlock) {
          inCodeBlock = true;
          codeBlockStart = lineStart;
        } else {
          inCodeBlock = false;
          structures.push({
            type: 'code_block',
            start: codeBlockStart,
            end: lineEnd,
            content: content.substring(codeBlockStart, lineEnd + 1)
          });
        }
      }
      // Skip analysis inside code blocks
      else if (!inCodeBlock) {
        // Check for headings
        const headingMatch = line.match(/^(#{1,6})\s+(.+)/);
        if (headingMatch) {
          structures.push({
            type: 'heading',
            start: lineStart,
            end: lineEnd,
            level: headingMatch[1].length,
            content: line
          });
        }
        // Check for table rows
        else if (line.includes('|') && line.trim().length > 0) {
          if (!inTable) {
            inTable = true;
            tableStart = lineStart;
          }
        }
        // Check for blockquotes
        else if (line.trim().startsWith('>')) {
          structures.push({
            type: 'blockquote',
            start: lineStart,
            end: lineEnd,
            content: line
          });
        }
        // Check for list items
        else if (line.match(/^\s*[-*+]\s+/) || line.match(/^\s*\d+\.\s+/)) {
          structures.push({
            type: 'list_item',
            start: lineStart,
            end: lineEnd,
            content: line
          });
        }
        // End table detection
        else if (inTable && line.trim().length === 0) {
          inTable = false;
          structures.push({
            type: 'table',
            start: tableStart,
            end: currentPosition - 1,
            content: content.substring(tableStart, currentPosition - 1)
          });
        }
      }

      currentPosition = lineEnd + 1; // +1 for newline character
    }

    // Handle unclosed table at end of content
    if (inTable) {
      structures.push({
        type: 'table',
        start: tableStart,
        end: content.length,
        content: content.substring(tableStart)
      });
    }

    return structures;
  }

  /**
   * Checks if a position is inside a structural element that should not contain citations
   */
  static isInsideRestrictedStructure(content: string, position: number): boolean {
    const structures = this.analyzeStructure(content);

    for (const structure of structures) {
      if (position >= structure.start && position <= structure.end) {
        // These structures should never contain citations
        if (['code_block', 'table', 'heading', 'blockquote'].includes(structure.type)) {
          return true;
        }
      }
    }

    return false;
  }

  /**
   * Checks if a position is inside parentheses or quotes
   */
  static isInsideParenthesesOrQuotes(content: string, position: number): boolean {
    // Look backwards and forwards to find matching parentheses or quotes
    let openParen = -1;
    let openQuote = -1;
    let quoteChar = '';

    // Search backwards for opening delimiters within the same sentence
    for (let i = position - 1; i >= 0; i--) {
      const char = content[i];

      // Stop at sentence boundaries or paragraph breaks
      if (char === '.' || char === '!' || char === '?') {
        // Check if this is actually end of sentence (not abbreviation)
        if (i > 0 && content[i-1] !== ' ') {
          // Likely an abbreviation, continue
          continue;
        }
        break;
      }
      if (char === '\n' && content[i-1] === '\n') {
        break; // Paragraph break
      }

      if (char === '(' && openParen === -1) {
        openParen = i;
      }
      if ((char === '"' || char === "'") && openQuote === -1) {
        // Make sure this isn't an escaped quote
        if (i === 0 || content[i-1] !== '\\') {
          openQuote = i;
          quoteChar = char;
        }
      }
    }

    // If we found opening delimiters, check for closing ones
    if (openParen !== -1) {
      for (let i = position; i < content.length; i++) {
        if (content[i] === ')') {
          return true; // Position is inside parentheses
        }
        // Stop searching at sentence or paragraph boundaries
        if (content[i] === '.' || content[i] === '!' || content[i] === '?') {
          if (i < content.length - 1 && content[i+1] === ' ') {
            break; // End of sentence
          }
        }
        if (content[i] === '\n' && i < content.length - 1 && content[i+1] === '\n') {
          break; // Paragraph break
        }
      }
    }

    if (openQuote !== -1) {
      for (let i = position; i < content.length; i++) {
        if (content[i] === quoteChar && (i === 0 || content[i-1] !== '\\')) {
          return true; // Position is inside quotes
        }
        // Stop searching at sentence or paragraph boundaries
        if (content[i] === '.' || content[i] === '!' || content[i] === '?') {
          if (i < content.length - 1 && content[i+1] === ' ') {
            break; // End of sentence
          }
        }
        if (content[i] === '\n' && i < content.length - 1 && content[i+1] === '\n') {
          break; // Paragraph break
        }
      }
    }

    return false;
  }

  /**
   * Finds safe insertion points for citations with different strategies for different file types
   */
  static findSafeInsertionPoints(content: string, preferInline: boolean = true): SafeInsertionPoint[] {
    const safePoints: SafeInsertionPoint[] = [];
    const sentenceEndPattern = /[.!?]/g;
    let match;

    while ((match = sentenceEndPattern.exec(content)) !== null) {
      // For inline citations (non-PDF/DOCX), insert immediately after punctuation
      // For PDF/DOCX citations, we might want to insert after whitespace
      let position = match.index + 1;

      // Skip if inside restricted structure
      if (this.isInsideRestrictedStructure(content, position)) {
        continue;
      }

      // Skip if inside parentheses or quotes
      if (this.isInsideParenthesesOrQuotes(content, position)) {
        continue;
      }

      // Additional checks to avoid false sentence endings
      const punctMark = content[match.index];
      const beforePunct = content.substring(Math.max(0, match.index - 10), match.index);
      const afterPunct = content.substring(position, Math.min(content.length, position + 10));

      // Skip periods that are likely part of file extensions or abbreviations
      if (punctMark === '.') {
        // Skip if it looks like a file extension (e.g., .pdf, .doc, .txt)
        if (/\w\.[a-zA-Z]{2,4}$/.test(beforePunct + '.')) {
          continue;
        }

        // Skip if it's likely an abbreviation (single letter followed by period)
        if (/\b[A-Za-z]\.$/.test(beforePunct + '.')) {
          continue;
        }

        // Skip if followed by lowercase letter (likely abbreviation)
        if (/^[a-z]/.test(afterPunct)) {
          continue;
        }

        // Skip if it's a decimal number
        if (/\d\.\d/.test(content.substring(match.index - 1, match.index + 2))) {
          continue;
        }
      }

      // Check what comes after the punctuation
      const nextChar = content[position];
      const nextTwoChars = content.substring(position, position + 2);

      // Skip if this would place citation at end of content
      if (position >= content.length) {
        continue;
      }

      // Only consider this a valid sentence end if followed by whitespace or end of content
      if (nextChar && !/\s/.test(nextChar)) {
        continue;
      }

      // Determine if this is end of paragraph by looking at what follows
      let isEndOfParagraph = false;

      // Check if followed by double newline (paragraph break)
      if (nextTwoChars === '\n\n') {
        isEndOfParagraph = true;
      }

      // Check if at end of content
      const remainingContent = content.substring(position).trim();
      if (remainingContent.length === 0) {
        isEndOfParagraph = true;
      }

      // Check if followed by whitespace then double newline
      const afterWhitespace = content.substring(position).match(/^\s*\n\s*\n/);
      if (afterWhitespace) {
        isEndOfParagraph = true;
      }

      // For inline citations, ensure we don't break the flow by inserting before line breaks
      if (preferInline && !isEndOfParagraph) {
        // Keep the position immediately after punctuation for inline flow
        // This ensures citations appear like: "sentence.[1] Next sentence"
      } else if (!preferInline && isEndOfParagraph) {
        // For PDF/DOCX citations at paragraph ends, we might want different positioning
        // This could be used for citations that should appear with line breaks
      }

      // Get context around the position
      const contextStart = Math.max(0, position - 50);
      const contextEnd = Math.min(content.length, position + 50);
      const context = content.substring(contextStart, contextEnd);

      safePoints.push({
        position,
        isAfterSentence: true,
        isEndOfParagraph,
        context: context.trim()
      });
    }

    return safePoints;
  }

  /**
   * Validates if a citation insertion would be safe at a given position
   */
  static isSafeInsertionPoint(content: string, position: number): boolean {
    // Check if inside restricted structures
    if (this.isInsideRestrictedStructure(content, position)) {
      return false;
    }

    // Check if inside parentheses or quotes
    if (this.isInsideParenthesesOrQuotes(content, position)) {
      return false;
    }

    // Check if position is after sentence terminator
    if (position > 0) {
      const prevChar = content[position - 1];
      if (!['.', '!', '?'].includes(prevChar)) {
        return false;
      }
    }

    return true;
  }

  /**
   * Gets the paragraph boundaries for a given position
   */
  static getParagraphBoundaries(content: string, position: number): { start: number; end: number } {
    let start = 0;
    let end = content.length;

    // Find paragraph start (double newline before position)
    for (let i = position - 1; i >= 0; i--) {
      if (content.substring(i, i + 2) === '\n\n') {
        start = i + 2;
        break;
      }
    }

    // Find paragraph end (double newline after position)
    for (let i = position; i < content.length - 1; i++) {
      if (content.substring(i, i + 2) === '\n\n') {
        end = i;
        break;
      }
    }

    return { start, end };
  }
}
