import { Logger } from "./Logger";
import { getPostgresUrl } from "./env";
import { AsyncPollingService, PollingResult } from "../services/async-polling-service";

export interface AsyncJobResponse {
  jobId: string;
  status: 'submitted' | 'processing';
  estimatedCompletion?: number;
  message?: string;
}

export async function processPdfViaApiGateway(documentId: string, url: string) {
  try {
    Logger.info("Processing PDF via API Gateway", { documentId, url });

    // The API Gateway endpoint URL
    const apiEndpoint =
      process.env.PDF_PROCESSOR_API_URL ||
      "https://ob62i6zfad.execute-api.us-east-1.amazonaws.com/prod";

    // Format the request payload
    const payload = {
      pdf_url: url,
      include_data_uri: true,
      optimize_size: true,
      target_dpi: 150,
      source_document_id: documentId,
      database_url: getPostgresUrl(),
    };

    // Call the API Gateway endpoint
    const response = await fetch(apiEndpoint, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        // Add any API key if required
        ...(process.env.PDF_PROCESSOR_API_KEY && {
          "x-api-key": process.env.PDF_PROCESSOR_API_KEY,
        }),
      },
      body: JSON.stringify(payload),
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`API Gateway error (${response.status}): ${errorText}`);
    }

    // Parse and return the response
    const result = await response.json();
    Logger.info("PDF processing via API Gateway successful", { documentId });

    return result;
  } catch (error) {
    Logger.error("Error processing PDF via API Gateway", {
      documentId,
      url,
      error: error,
    });
    throw error;
  }
}

/**
 * Processes PDF via API Gateway with extended timeout for async operations
 * This version handles the asynchronous nature of Enhanced Doc Reader services
 * by using the existing synchronous API but with longer timeouts
 * @param documentId Document ID
 * @param url PDF URL
 * @returns Promise resolving to processing result
 */
export async function processPdfViaApiGatewayAsync(
  documentId: string,
  url: string
): Promise<PollingResult> {
  try {
    Logger.info("Processing PDF via API Gateway with extended timeout", { documentId, url });

    // Use the existing synchronous method but with extended timeout
    // The Enhanced Doc Reader services are async internally but the API waits for completion
    const result = await Promise.race([
      processPdfViaApiGateway(documentId, url),
      new Promise((_, reject) =>
        setTimeout(() => reject(new Error('API Gateway timeout after 15 minutes')), 900000)
      )
    ]);

    Logger.info("API Gateway processing completed successfully", {
      documentId,
      processingTime: 'within-timeout',
    });

    return {
      success: true,
      data: result,
      metadata: {
        totalPollingTime: 0, // No polling needed
        pollAttempts: 1,
        finalStatus: 'completed',
        jobId: documentId,
      },
    };

  } catch (error) {
    Logger.error("Error in API Gateway processing with extended timeout", {
      documentId,
      url,
      error: error instanceof Error ? error.message : String(error),
    });

    return {
      success: false,
      error: error instanceof Error ? error.message : String(error),
      metadata: {
        totalPollingTime: 0,
        pollAttempts: 1,
        finalStatus: 'failed',
        jobId: documentId,
      },
    };
  }
}

// Removed submitPdfProcessingJob function - using simpler timeout approach instead
