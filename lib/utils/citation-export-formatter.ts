import { CitationMatch } from './citation-formatter';
import { CitationTextProcessor } from './citation-text-processor';

/**
 * Utility for formatting citations in exported documents (PDF/Word)
 */
export class CitationExportFormatter {
  /**
   * Formats content for export by converting interactive citations to static superscripts
   * and adding a numbered reference section at the end
   */
  static formatForExport(content: string, citations: CitationMatch[]): string {
    if (!citations || citations.length === 0) {
      return CitationTextProcessor.removeReferencesSection(content);
    }

    // Remove existing references section
    const contentWithoutReferences = CitationTextProcessor.removeReferencesSection(content);

    // Convert both old format [^1] and new format [1] citation markers to superscripts
    let exportContent = contentWithoutReferences;

    // Handle old format [^1], [^2], etc.
    const oldCitationRegex = /\[\^(\d+)\]/g;
    exportContent = exportContent.replace(oldCitationRegex, (match, citationNumber) => {
      return `<sup>${citationNumber}</sup>`;
    });

    // Handle new format [1], [2], etc. (but only if they look like citations)
    // Be careful not to replace other bracketed numbers that aren't citations
    const newCitationRegex = /\[(\d+)\]/g;
    exportContent = exportContent.replace(newCitationRegex, (match, citationNumber) => {
      const num = parseInt(citationNumber, 10);
      // Only replace if the number is within the range of available citations
      if (num > 0 && num <= citations.length) {
        return `<sup>${citationNumber}</sup>`;
      }
      return match; // Keep original if not a valid citation
    });

    // Add numbered reference section at the end
    const referencesSection = this.generateExportReferencesSection(citations);

    return exportContent + '\n\n' + referencesSection;
  }

  /**
   * Generates a clean references section for export documents
   */
  private static generateExportReferencesSection(citations: CitationMatch[]): string {
    if (!citations || citations.length === 0) {
      return '';
    }

    let referencesSection = '## References\n\n';

    citations.forEach((citation, index) => {
      const citationNumber = index + 1;
      const filename = citation.filename || 'Unknown Document';
      const pageInfo = citation.pageNumber > 1 ? `, p. ${citation.pageNumber}` : '';

      // Truncate preview for export
      const preview = this.truncateForExport(citation.chunkPreview, 150);

      referencesSection += `${citationNumber}. **${filename}**${pageInfo}\n`;
      referencesSection += `   "${preview}"\n\n`;
    });

    return referencesSection.trim();
  }

  /**
   * Formats content specifically for Word export with proper HTML superscripts
   */
  static formatForWordExport(content: string, citations: CitationMatch[]): string {
    const exportContent = this.formatForExport(content, citations);

    // Convert markdown superscripts to HTML for Word compatibility
    return exportContent.replace(/<sup>(\d+)<\/sup>/g, '<sup>$1</sup>');
  }

  /**
   * Formats content for PDF export
   */
  static formatForPdfExport(content: string, citations: CitationMatch[]): string {
    const exportContent = this.formatForExport(content, citations);

    // For PDF, we can use Unicode superscript characters
    const superscriptNumbers = ['¹', '²', '³', '⁴', '⁵', '⁶', '⁷', '⁸', '⁹', '¹⁰'];

    return exportContent.replace(/<sup>(\d+)<\/sup>/g, (match, num) => {
      const number = parseInt(num, 10);
      if (number <= 10) {
        return superscriptNumbers[number - 1];
      }
      return `^${number}`;
    });
  }

  /**
   * Truncates text for export with proper ellipsis
   */
  private static truncateForExport(text: string, maxLength: number): string {
    if (text.length <= maxLength) {
      return text;
    }

    // Find the last complete word within the limit
    const truncated = text.substring(0, maxLength - 3);
    const lastSpace = truncated.lastIndexOf(' ');

    if (lastSpace > maxLength * 0.8) {
      return truncated.substring(0, lastSpace) + '...';
    }

    return truncated + '...';
  }

  /**
   * Strips all interactive elements and returns plain text with citations
   */
  static stripInteractiveElements(content: string, citations: CitationMatch[]): string {
    const exportContent = this.formatForExport(content, citations);

    // Remove HTML tags for plain text export
    return exportContent.replace(/<[^>]*>/g, '');
  }
}
