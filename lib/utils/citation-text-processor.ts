import { CitationMatch } from './citation-formatter';

export interface ProcessedTextPart {
  type: 'text' | 'citation';
  content: string;
  citationNumber?: number;
  citation?: CitationMatch;
}

/**
 * Simple utility to process text and identify citation markers
 */
export class CitationTextProcessor {
  /**
   * Processes text content and splits it into text and citation parts
   */
  static processText(content: string, citations: CitationMatch[]): ProcessedTextPart[] {
    if (!citations || citations.length === 0) {
      return [{ type: 'text', content }];
    }

    // Create a map for quick citation lookup
    const citationMap = new Map<number, CitationMatch>();
    citations.forEach((citation, index) => {
      citationMap.set(index + 1, citation);
    });

    const parts: ProcessedTextPart[] = [];
    let lastIndex = 0;

    // Find citation markers [^1], [^2], etc.
    const citationRegex = /\[\^(\d+)\]/g;
    let match;

    while ((match = citationRegex.exec(content)) !== null) {
      const citationNumber = parseInt(match[1], 10);
      const citation = citationMap.get(citationNumber);

      // Add text before the citation marker
      if (match.index > lastIndex) {
        const textBefore = content.substring(lastIndex, match.index);
        if (textBefore) {
          parts.push({ type: 'text', content: textBefore });
        }
      }

      // Add the citation part
      if (citation) {
        parts.push({
          type: 'citation',
          content: match[0],
          citationNumber,
          citation
        });
      } else {
        // If citation not found, treat as regular text
        parts.push({ type: 'text', content: match[0] });
      }

      lastIndex = match.index + match[0].length;
    }

    // Add remaining text after the last citation
    if (lastIndex < content.length) {
      const remainingText = content.substring(lastIndex);
      if (remainingText) {
        parts.push({ type: 'text', content: remainingText });
      }
    }

    return parts.length > 0 ? parts : [{ type: 'text', content }];
  }

  /**
   * Removes the References section from content
   */
  static removeReferencesSection(content: string): string {
    // Remove the References section that starts with "## References"
    const referencesRegex = /\n\n## References\n\n[\s\S]*$/;
    return content.replace(referencesRegex, '');
  }

  /**
   * Checks if content has citation markers
   */
  static hasCitations(content: string): boolean {
    const citationRegex = /\[\^(\d+)\]/;
    return citationRegex.test(content);
  }

  /**
   * Extracts reference text from the References section for use in tooltips
   */
  static extractReferenceText(content: string, citationNumber: number): string | null {
    // Look for the References section
    const referencesMatch = content.match(/## References\n\n([\s\S]*?)$/);
    if (!referencesMatch) return null;

    const referencesSection = referencesMatch[1];

    // Look for the specific citation reference
    const citationPattern = new RegExp(`\\[\\^${citationNumber}\\]:[\\s\\S]*?(?=\\n\\[\\^\\d+\\]:|$)`, 'g');
    const citationMatch = referencesSection.match(citationPattern);

    if (!citationMatch) return null;

    // Clean up the reference text
    let referenceText = citationMatch[0];

    // Remove the citation marker
    referenceText = referenceText.replace(/^\[\^\d+\]:\s*/, '');

    // Remove markdown formatting and clean up
    referenceText = referenceText
      .replace(/\*"([^"]*?)"\*/g, '"$1"') // Remove italic formatting around quotes
      .replace(/\n\s+/g, ' ') // Replace newlines with spaces
      .trim();

    return referenceText;
  }
}
