/**
 * Citation Formatter Utility
 * Provides improved formatting for citations and reference sections
 */

export interface CitationMatch {
  chunkId: string;
  similarity: number;
  chunkText: string;
  chunkPreview: string;
  pageNumber: number;
  sourceDocumentId: string;
  filename?: string;
}

export interface FormattingOptions {
  maxFilenameLength: number;
  maxPreviewLength: number;
  maxLineLength: number;
  truncateIndicator: string;
  compactMode: boolean;
}

export const DEFAULT_FORMATTING_OPTIONS: FormattingOptions = {
  maxFilenameLength: 60,
  maxPreviewLength: 150,
  maxLineLength: 100,
  truncateIndicator: "...",
  compactMode: false
};

export class CitationFormatter {
  /**
   * Truncates filename while preserving extension
   */
  static truncateFilename(filename: string, maxLength: number, indicator: string = "..."): string {
    if (!filename || filename.length <= maxLength) {
      return filename;
    }

    // Extract extension
    const lastDotIndex = filename.lastIndexOf('.');
    const extension = lastDotIndex > 0 ? filename.substring(lastDotIndex) : '';
    const nameWithoutExt = lastDotIndex > 0 ? filename.substring(0, lastDotIndex) : filename;

    // Calculate available space for name
    const availableLength = maxLength - extension.length - indicator.length;
    
    if (availableLength <= 0) {
      return filename.substring(0, maxLength - indicator.length) + indicator;
    }

    return nameWithoutExt.substring(0, availableLength) + indicator + extension;
  }

  /**
   * Truncates preview text at word boundaries
   */
  static truncatePreview(preview: string, maxLength: number, indicator: string = "..."): string {
    if (!preview || preview.length <= maxLength) {
      return preview;
    }

    // Find last complete word within limit
    const truncated = preview.substring(0, maxLength - indicator.length);
    const lastSpaceIndex = truncated.lastIndexOf(' ');
    
    if (lastSpaceIndex > maxLength * 0.7) {
      // If we can preserve most of the text, truncate at word boundary
      return truncated.substring(0, lastSpaceIndex) + indicator;
    } else {
      // Otherwise, truncate at character limit
      return truncated + indicator;
    }
  }

  /**
   * Sanitizes filename for markdown rendering
   */
  static sanitizeFilename(filename: string): string {
    if (!filename) return "Unknown Document";
    
    // Escape markdown special characters
    return filename
      .replace(/\[/g, '\\[')
      .replace(/\]/g, '\\]')
      .replace(/\(/g, '\\(')
      .replace(/\)/g, '\\)')
      .replace(/\*/g, '\\*')
      .replace(/_/g, '\\_')
      .replace(/`/g, '\\`')
      .replace(/~/g, '\\~')
      .replace(/\|/g, '\\|');
  }

  /**
   * Sanitizes preview text for markdown rendering
   */
  static sanitizePreview(preview: string): string {
    if (!preview) return "";
    
    // Remove or escape problematic characters
    return preview
      // Remove nested quotes to prevent markdown issues
      .replace(/"/g, "'")
      // Escape markdown formatting
      .replace(/\*/g, '\\*')
      .replace(/_/g, '\\_')
      .replace(/`/g, '\\`')
      .replace(/~/g, '\\~')
      // Normalize whitespace
      .replace(/\s+/g, ' ')
      .trim();
  }

  /**
   * Formats page number with proper handling of edge cases
   */
  static formatPageNumber(pageNumber: number): string {
    if (!pageNumber || pageNumber <= 0) {
      return "";
    }
    
    // Handle very high page numbers (likely errors)
    if (pageNumber > 9999) {
      return "";
    }
    
    return `, p. ${pageNumber}`;
  }

  /**
   * Generates a properly formatted reference entry
   */
  static formatReference(
    citation: CitationMatch, 
    index: number, 
    options: FormattingOptions = DEFAULT_FORMATTING_OPTIONS
  ): string {
    const citationNumber = index + 1;
    
    // Process filename
    const rawFilename = citation.filename || "Unknown Document";
    const sanitizedFilename = this.sanitizeFilename(rawFilename);
    const truncatedFilename = this.truncateFilename(
      sanitizedFilename, 
      options.maxFilenameLength, 
      options.truncateIndicator
    );
    
    // Process page number
    const pageInfo = this.formatPageNumber(citation.pageNumber);
    
    // Process preview
    const sanitizedPreview = this.sanitizePreview(citation.chunkPreview);
    const truncatedPreview = this.truncatePreview(
      sanitizedPreview, 
      options.maxPreviewLength, 
      options.truncateIndicator
    );

    if (options.compactMode) {
      // Compact format for better readability
      return `[^${citationNumber}]: ${truncatedFilename}${pageInfo}\n    "${truncatedPreview}"`;
    } else {
      // Standard format with proper spacing
      return `[^${citationNumber}]: ${truncatedFilename}${pageInfo}  \n    *"${truncatedPreview}"*`;
    }
  }

  /**
   * Generates the complete references section with improved formatting
   */
  static generateReferencesSection(
    citations: CitationMatch[], 
    options: FormattingOptions = DEFAULT_FORMATTING_OPTIONS
  ): string {
    if (!citations || citations.length === 0) {
      return "";
    }

    let referencesSection = "## References\n\n";

    citations.forEach((citation, index) => {
      const formattedReference = this.formatReference(citation, index, options);
      referencesSection += formattedReference + "\n\n";
    });

    return referencesSection.trim();
  }

  /**
   * Validates reference section formatting
   */
  static validateReferencesSection(referencesSection: string): {
    isValid: boolean;
    issues: string[];
    suggestions: string[];
  } {
    const issues: string[] = [];
    const suggestions: string[] = [];
    
    // Check line lengths
    const lines = referencesSection.split('\n');
    const longLines = lines.filter(line => line.length > 120);
    if (longLines.length > 0) {
      issues.push(`${longLines.length} lines exceed 120 characters`);
      suggestions.push("Consider enabling compact mode or reducing max lengths");
    }
    
    // Check for unescaped special characters
    const unescapedChars = referencesSection.match(/[^\\][*_`~|[\]()]/g);
    if (unescapedChars && unescapedChars.length > 0) {
      issues.push("Unescaped markdown special characters detected");
      suggestions.push("Enable filename and preview sanitization");
    }
    
    // Check for nested quotes
    const nestedQuotes = referencesSection.match(/"[^"]*"[^"]*"/g);
    if (nestedQuotes && nestedQuotes.length > 0) {
      issues.push("Nested quotes detected in preview text");
      suggestions.push("Preview sanitization will convert quotes to single quotes");
    }
    
    return {
      isValid: issues.length === 0,
      issues,
      suggestions
    };
  }

  /**
   * Creates alternative formatting options for different use cases
   */
  static getFormattingPresets(): Record<string, FormattingOptions> {
    return {
      standard: DEFAULT_FORMATTING_OPTIONS,
      
      compact: {
        maxFilenameLength: 40,
        maxPreviewLength: 100,
        maxLineLength: 80,
        truncateIndicator: "...",
        compactMode: true
      },
      
      mobile: {
        maxFilenameLength: 30,
        maxPreviewLength: 80,
        maxLineLength: 60,
        truncateIndicator: "...",
        compactMode: true
      },
      
      detailed: {
        maxFilenameLength: 80,
        maxPreviewLength: 200,
        maxLineLength: 120,
        truncateIndicator: "...",
        compactMode: false
      }
    };
  }
}
