import type { Message as AIMessage } from "ai";

declare module "ai" {
  interface Attachment {
    document_id?: string;
    pendingConfirm?: boolean;
  }
  interface Message {
    sources: Array<any>;
  }
}

export enum AllowedTools {
  // createDocument = "createDocument",
  requestSuggestions = "requestSuggestions",
  getInformation = "getInformation",
}
export const BLOCKS_TOOLS: AllowedTools[] = [
  // AllowedTools.createDocument,
  AllowedTools.requestSuggestions,
];
export const ALL_TOOLS: AllowedTools[] = [
  AllowedTools.getInformation,
  // ...BLOCKS_TOOLS
];
// HELP DOCUMENT TYPES
export interface HelpDocument {
  id: string;
  displayName: string;
  url: string;
}

export const FAQ_DOCUMENT: HelpDocument = {
  id: "faq",
  displayName: "FAQ",
  url: process.env.DOC_FAQ || "",
};

// export const POLICY_DOCUMENTS: HelpDocument[] = [
//   // {
//   //   id: "privacy-policy",
//   //   displayName: "Privacy Policy",
//   //   url: process.env.DOC_PRIVACY_POLICY || "",
//   // },
//   // {
//   //   id: "acceptable-use-policy",
//   //   displayName: "Acceptable Use Policy",
//   //   url: process.env.DOC_ACCEPTABLE_USE || "",
//   // },
//   // {
//   //   id: "disclaimer",
//   //   displayName: "Disclaimer",
//   //   url: process.env.DOC_DISCLAIMER || "",
//   // },
//   // {
//   //   id: "terms-of-service",
//   //   displayName: "Terms of Service",
//   //   url: process.env.DOC_TERMS_OF_SERVICE || "",
//   // },
//   // {
//   //   id: "cookies-policy",
//   //   displayName: "Cookies Policy",
//   //   url: process.env.DOC_COOKIES_POLICY || "",
//   // },
//   {
//     id: "service-provider-and-subprocessor-list",
//     displayName: "Service Provider and Subprocessor List",
//     url: process.env.DOC_SERVICE_PROVIDER_SUBPROC_LIST || "",
//   },
// ] as const;

export const POLICY_PAGES: HelpDocument[] = [
  {
    id: "privacy-policy",
    displayName: "Privacy Policy",
    url: "/privacy-policy",
  },
  {
    id: "terms-of-service",
    displayName: "Terms of Service",
    url: "/terms-of-service",
  },
  {
    id: "acceptable-use-policy",
    displayName: "Acceptable Use Policy",
    url: "/acceptable-use-policy",
  },
  {
    id: "disclaimer",
    displayName: "Disclaimer",
    url: "/disclaimer",
  },
  {
    id: "cookies-policy",
    displayName: "Cookies Policy",
    url: "/cookies-policy",
  },
  {
    id: "service-provider-and-subprocessor-list",
    displayName: "Service Provider and Subprocessor List",
    url: "/service-provider-and-subprocessor-list",
  },
] as const;

export const VALID_DOCUMENTS = [FAQ_DOCUMENT] as const;
// Default visibility
export const DEFAULT_CHAT_VISIBILITY = "private" as const;

export interface ProcessedChunk {
  text: string;
  pageNumber: number;
  overlap: boolean;
  image_base64?: string;
  filename?: string;
  type?: string;
}

export interface SearchResult {
  chatId: string;
  chatTitle: string;
  messageId?: string;
  messagePreview?: string;
  matchType: "title" | "message";
  timestamp: string;
}

export interface ExtendedMessage extends AIMessage {
  metadata?: {
    internetResults?: {
      mainContent: string;
      citations: string[];
      formattedContent: string;
    };
    relevantImages?: Array<{ mime_type: string; url: string }>;
    rewrittenQuery?: string;
    chainOfThoughts?: string;
    [key: string]: any;
  };
}

export type Chat = {
  id: string;
  title: string;
  createdAt: Date;
  updatedAt?: Date;
  visibility: "public" | "private";
  userId: string;
};

export type Tag = {
  name: string;
  color: string;
};

export type ChatWithTags = Chat & {
  tags: Tag[];
};
