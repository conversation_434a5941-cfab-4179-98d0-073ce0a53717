import { Logger } from "../utils/Logger";
import { db } from "../db";
import { messageDocuments, sourceDocuments, message } from "../db/schema";
import { eq } from "drizzle-orm";

// Configuration for inline citations triggering
const TRIGGER_CONFIG = {
  ENABLE_LOGGING: true,
  REQUIRE_DOCUMENTS: true,
  REQUIRE_CHAT_INITIATION: true,
} as const;

export interface TriggerConditions {
  hasDocuments: boolean;
  isChatInitiated: boolean;
  isPreviewMode: boolean;
  isDraftMode: boolean;
  documentCount: number;
  chatExists: boolean;
}

export interface TriggerResult {
  shouldTrigger: boolean;
  conditions: TriggerConditions;
  reason: string;
  messageId: string;
  chatId: string;
}

export class InlineCitationsTrigger {
  /**
   * Main entry point to determine if inline citations pipeline should be triggered
   * @param messageId The message ID
   * @param chatId The chat ID
   * @param messages The chat messages array
   * @param chatExists Whether the chat already exists in database
   * @param isReadonly Whether the chat is in readonly mode
   * @returns Trigger result with decision and reasoning
   */
  static async shouldTriggerInlineCitations(
    messageId: string,
    chatId: string,
    messages: any[],
    chatExists: boolean,
    isReadonly: boolean = false
  ): Promise<TriggerResult> {
    try {
      Logger.info("Evaluating inline citations trigger conditions", {
        messageId,
        chatId,
        messageCount: messages.length,
        chatExists,
        isReadonly,
      });

      // Analyze trigger conditions
      const conditions = await this.analyzeTriggerConditions(
        messageId,
        chatId,
        messages,
        chatExists,
        isReadonly
      );

      // Determine if pipeline should trigger
      const shouldTrigger = this.evaluateTriggerDecision(conditions);

      // Generate reasoning
      const reason = this.generateTriggerReason(conditions, shouldTrigger);

      const result: TriggerResult = {
        shouldTrigger,
        conditions,
        reason,
        messageId,
        chatId,
      };

      // Log the decision
      this.logTriggerDecision(result);

      return result;
    } catch (error) {
      Logger.error("Error evaluating inline citations trigger", {
        messageId,
        chatId,
        error,
      });

      return {
        shouldTrigger: false,
        conditions: {
          hasDocuments: false,
          isChatInitiated: false,
          isPreviewMode: false,
          isDraftMode: false,
          documentCount: 0,
          chatExists,
        },
        reason: "Error evaluating trigger conditions",
        messageId,
        chatId,
      };
    }
  }

  /**
   * Analyzes all trigger conditions from the current chat state
   */
  private static async analyzeTriggerConditions(
    messageId: string,
    chatId: string,
    messages: any[],
    chatExists: boolean,
    isReadonly: boolean
  ): Promise<TriggerConditions> {
    // Check for document uploads in the chat (check both messages and database associations)
    const documentInfo = await this.analyzeDocumentPresence(messageId, chatId, messages);

    // Check if chat is initiated (not preview/draft)
    const chatInitiationInfo = this.analyzeChatInitiation(
      messages,
      chatExists,
      isReadonly
    );

    return {
      hasDocuments: documentInfo.hasDocuments,
      documentCount: documentInfo.documentCount,
      isChatInitiated: chatInitiationInfo.isChatInitiated,
      isPreviewMode: chatInitiationInfo.isPreviewMode,
      isDraftMode: chatInitiationInfo.isDraftMode,
      chatExists,
    };
  }

  /**
   * Analyzes document presence in the chat messages and database associations
   */
  private static async analyzeDocumentPresence(
    messageId: string,
    chatId: string,
    messages: any[]
  ): Promise<{
    hasDocuments: boolean;
    documentCount: number;
  }> {
    let totalDocuments = 0;
    let messageAttachmentCount = 0;
    let dbDocumentCount = 0;

    // First, check for documents in message attachments (for immediate uploads)
    for (const msg of messages) {
      if (msg.role === "user" && msg.experimental_attachments) {
        const attachments = Array.isArray(msg.experimental_attachments)
          ? msg.experimental_attachments
          : [];

        // Count valid document attachments
        const validAttachments = attachments.filter((attachment: any) => {
          return (
            attachment &&
            attachment.url &&
            attachment.name &&
            !attachment.pendingConfirm // Exclude pending confirmations
          );
        });

        messageAttachmentCount += validAttachments.length;
      }
    }

    totalDocuments += messageAttachmentCount;

    // Second, check for documents associated with ANY user message in this chat
    // This is crucial because documents are associated with user messages, but the
    // inline citations pipeline is triggered for assistant messages
    try {
      // Get all user messages from this chat from the database
      const chatMessages = await db
        .select({
          messageId: message.id,
        })
        .from(message)
        .where(eq(message.chatId, chatId));

      if (chatMessages.length > 0) {
        // Get all documents associated with any message in this chat
        const associatedDocuments = await db
          .select({
            documentId: messageDocuments.sourceDocumentId,
            messageId: messageDocuments.messageId,
          })
          .from(messageDocuments)
          .innerJoin(message, eq(messageDocuments.messageId, message.id))
          .where(eq(message.chatId, chatId));

        dbDocumentCount = associatedDocuments.length;
        totalDocuments += dbDocumentCount;

        Logger.debug("Document presence analysis", {
          messageAttachments: messageAttachmentCount,
          databaseAssociations: dbDocumentCount,
          totalDocuments,
          hasDocuments: totalDocuments > 0,
          messageId,
          chatId,
          chatMessageCount: chatMessages.length,
          searchScope: "chat-wide",
        });
      } else {
        Logger.debug("Document presence analysis", {
          messageAttachments: messageAttachmentCount,
          databaseAssociations: 0,
          totalDocuments,
          hasDocuments: totalDocuments > 0,
          messageId,
          chatId,
          chatMessageCount: 0,
          searchScope: "message-attachments-only",
        });
      }
    } catch (error) {
      Logger.warn("Error checking database document associations", {
        messageId,
        chatId,
        error,
      });
      // Continue with just message-based count if database check fails
    }

    return {
      hasDocuments: totalDocuments > 0,
      documentCount: totalDocuments,
    };
  }

  /**
   * Analyzes chat initiation status (not preview/draft)
   */
  private static analyzeChatInitiation(
    messages: any[],
    chatExists: boolean,
    isReadonly: boolean
  ): {
    isChatInitiated: boolean;
    isPreviewMode: boolean;
    isDraftMode: boolean;
  } {
    // Chat is considered initiated if:
    // 1. Chat exists in database (not a new chat)
    // 2. OR there are actual user messages (not just system/preview messages)
    // 3. AND it's not in readonly mode (which could indicate preview)

    const userMessages = messages.filter((msg) => msg.role === "user");
    const hasUserMessages = userMessages.length > 0;

    // Preview mode indicators
    const isPreviewMode = isReadonly && !chatExists;

    // Draft mode indicators (new chat with no committed messages)
    const isDraftMode = !chatExists && !hasUserMessages;

    // Chat is initiated if it exists OR has user messages AND is not in preview mode
    const isChatInitiated = (chatExists || hasUserMessages) && !isPreviewMode;

    Logger.debug("Chat initiation analysis", {
      chatExists,
      hasUserMessages,
      userMessageCount: userMessages.length,
      isReadonly,
      isPreviewMode,
      isDraftMode,
      isChatInitiated,
    });

    return {
      isChatInitiated,
      isPreviewMode,
      isDraftMode,
    };
  }

  /**
   * Evaluates the final trigger decision based on all conditions
   */
  private static evaluateTriggerDecision(conditions: TriggerConditions): boolean {
    // Pipeline triggers ONLY if:
    // 1. User has uploaded document(s) in the chat
    // 2. Chat is initiated (not preview or draft)

    const hasRequiredDocuments = TRIGGER_CONFIG.REQUIRE_DOCUMENTS
      ? conditions.hasDocuments
      : true;

    const isChatProperlyInitiated = TRIGGER_CONFIG.REQUIRE_CHAT_INITIATION
      ? conditions.isChatInitiated && !conditions.isPreviewMode && !conditions.isDraftMode
      : true;

    return hasRequiredDocuments && isChatProperlyInitiated;
  }

  /**
   * Generates human-readable reasoning for the trigger decision
   */
  private static generateTriggerReason(
    conditions: TriggerConditions,
    shouldTrigger: boolean
  ): string {
    if (shouldTrigger) {
      return `Inline citations triggered: ${conditions.documentCount} document(s) uploaded, chat initiated`;
    }

    const reasons: string[] = [];

    if (!conditions.hasDocuments) {
      reasons.push("no documents uploaded");
    }

    if (!conditions.isChatInitiated) {
      reasons.push("chat not initiated");
    }

    if (conditions.isPreviewMode) {
      reasons.push("chat in preview mode");
    }

    if (conditions.isDraftMode) {
      reasons.push("chat in draft mode");
    }

    return `Inline citations skipped: ${reasons.join(", ")}`;
  }

  /**
   * Logs the trigger decision with comprehensive details
   */
  private static logTriggerDecision(result: TriggerResult): void {
    if (!TRIGGER_CONFIG.ENABLE_LOGGING) return;

    const logLevel = result.shouldTrigger ? "info" : "debug";
    const logMessage = result.shouldTrigger
      ? "Inline citations pipeline TRIGGERED"
      : "Inline citations pipeline SKIPPED";

    Logger[logLevel](logMessage, {
      messageId: result.messageId,
      chatId: result.chatId,
      decision: result.shouldTrigger,
      reason: result.reason,
      conditions: {
        hasDocuments: result.conditions.hasDocuments,
        documentCount: result.conditions.documentCount,
        isChatInitiated: result.conditions.isChatInitiated,
        isPreviewMode: result.conditions.isPreviewMode,
        isDraftMode: result.conditions.isDraftMode,
        chatExists: result.conditions.chatExists,
      },
      triggerConfig: TRIGGER_CONFIG,
    });

    // Log pipeline initiation status
    if (result.shouldTrigger) {
      Logger.info("Pipeline initiation confirmed", {
        messageId: result.messageId,
        chatId: result.chatId,
        triggeringConditions: {
          documentCount: result.conditions.documentCount,
          chatInitiated: result.conditions.isChatInitiated,
        },
      });
    }
  }

  /**
   * Logs the final output after pipeline completion
   */
  static logPipelineCompletion(
    result: TriggerResult,
    pipelineSuccess: boolean,
    citationCount: number = 0,
    processingTimeMs: number = 0
  ): void {
    if (!TRIGGER_CONFIG.ENABLE_LOGGING) return;

    if (result.shouldTrigger) {
      Logger.info("Inline citations pipeline completed", {
        messageId: result.messageId,
        chatId: result.chatId,
        success: pipelineSuccess,
        citationCount,
        processingTimeMs,
        finalOutput: {
          citationsGenerated: citationCount > 0,
          pipelineTriggered: true,
          conditions: result.conditions,
        },
      });
    } else {
      Logger.debug("Inline citations pipeline not triggered", {
        messageId: result.messageId,
        chatId: result.chatId,
        reason: result.reason,
        finalOutput: {
          citationsGenerated: false,
          pipelineTriggered: false,
          conditions: result.conditions,
        },
      });
    }
  }
}
