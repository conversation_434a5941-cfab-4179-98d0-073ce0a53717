/**
 * Async Polling Service for Enhanced Doc Reader Image Extraction
 * 
 * This service provides polling mechanisms to wait for asynchronous 
 * Enhanced Doc Reader services (API Gateway/Fargate) to complete
 * image extraction before proceeding with OCR and citation insertion.
 * 
 * Key Features:
 * - Configurable polling intervals and timeouts
 * - Database status tracking using sourceDocuments.metadata
 * - Exponential backoff for failed requests
 * - Comprehensive logging and error handling
 * - No schema changes required
 */

import { Logger } from "../utils/Logger";
import { db } from "../db";
import { sourceDocuments } from "../db/schema";
import { eq } from "drizzle-orm";

export interface AsyncJobStatus {
  jobId: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  progress?: number;
  result?: any;
  error?: string;
  metadata?: {
    startTime: number;
    lastUpdated: number;
    attempts: number;
    estimatedCompletion?: number;
  };
}

export interface PollingConfig {
  initialInterval: number;      // Initial polling interval in ms (default: 2000)
  maxInterval: number;          // Maximum polling interval in ms (default: 10000)
  timeout: number;              // Maximum total polling time in ms (default: 600000 = 10 minutes)
  backoffMultiplier: number;    // Backoff multiplier for failed requests (default: 1.5)
  maxRetries: number;           // Maximum retry attempts for failed polls (default: 3)
}

export interface PollingResult<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  metadata: {
    totalPollingTime: number;
    pollAttempts: number;
    finalStatus: string;
    jobId: string;
  };
}

export class AsyncPollingService {
  private static readonly DEFAULT_CONFIG: PollingConfig = {
    initialInterval: 2000,      // 2 seconds
    maxInterval: 10000,         // 10 seconds  
    timeout: 600000,            // 10 minutes
    backoffMultiplier: 1.5,
    maxRetries: 3,
  };

  /**
   * Polls an async job until completion or timeout
   * @param jobId The job ID to poll
   * @param statusChecker Function to check job status
   * @param config Polling configuration
   * @returns Promise resolving to polling result
   */
  static async pollUntilComplete<T>(
    jobId: string,
    statusChecker: (jobId: string) => Promise<AsyncJobStatus>,
    config: Partial<PollingConfig> = {}
  ): Promise<PollingResult<T>> {
    const pollingConfig = { ...this.DEFAULT_CONFIG, ...config };
    const startTime = Date.now();
    let pollAttempts = 0;
    let currentInterval = pollingConfig.initialInterval;
    let consecutiveFailures = 0;

    Logger.info("Starting async job polling", {
      jobId,
      config: pollingConfig,
      startTime: new Date(startTime).toISOString(),
    });

    while (Date.now() - startTime < pollingConfig.timeout) {
      pollAttempts++;
      
      try {
        Logger.debug("Polling job status", {
          jobId,
          attempt: pollAttempts,
          elapsedTime: Date.now() - startTime,
          currentInterval,
        });

        const status = await statusChecker(jobId);
        consecutiveFailures = 0; // Reset failure count on success

        // Update database with current status
        await this.updateJobStatusInDatabase(jobId, status);

        // Check if job is complete
        if (status.status === 'completed') {
          const totalPollingTime = Date.now() - startTime;
          
          Logger.info("Async job completed successfully", {
            jobId,
            totalPollingTime,
            pollAttempts,
            finalStatus: status.status,
          });

          return {
            success: true,
            data: status.result,
            metadata: {
              totalPollingTime,
              pollAttempts,
              finalStatus: status.status,
              jobId,
            },
          };
        }

        // Check if job failed
        if (status.status === 'failed') {
          const totalPollingTime = Date.now() - startTime;
          
          Logger.error("Async job failed", {
            jobId,
            error: status.error,
            totalPollingTime,
            pollAttempts,
          });

          return {
            success: false,
            error: status.error || 'Job failed without specific error',
            metadata: {
              totalPollingTime,
              pollAttempts,
              finalStatus: status.status,
              jobId,
            },
          };
        }

        // Job still processing, wait before next poll
        Logger.debug("Job still processing, waiting for next poll", {
          jobId,
          status: status.status,
          progress: status.progress,
          nextPollIn: currentInterval,
        });

        await this.sleep(currentInterval);

        // Gradually increase polling interval (but don't exceed max)
        currentInterval = Math.min(
          currentInterval * 1.1, // Gentle increase
          pollingConfig.maxInterval
        );

      } catch (error) {
        consecutiveFailures++;
        
        Logger.warn("Error during job status polling", {
          jobId,
          attempt: pollAttempts,
          consecutiveFailures,
          error: error instanceof Error ? error.message : String(error),
        });

        // If we've exceeded max retries for consecutive failures, fail
        if (consecutiveFailures >= pollingConfig.maxRetries) {
          const totalPollingTime = Date.now() - startTime;
          
          Logger.error("Max consecutive polling failures reached", {
            jobId,
            consecutiveFailures,
            maxRetries: pollingConfig.maxRetries,
            totalPollingTime,
          });

          return {
            success: false,
            error: `Polling failed after ${consecutiveFailures} consecutive attempts: ${error instanceof Error ? error.message : String(error)}`,
            metadata: {
              totalPollingTime,
              pollAttempts,
              finalStatus: 'polling_failed',
              jobId,
            },
          };
        }

        // Apply exponential backoff for failed requests
        const backoffInterval = currentInterval * Math.pow(pollingConfig.backoffMultiplier, consecutiveFailures);
        await this.sleep(Math.min(backoffInterval, pollingConfig.maxInterval));
      }
    }

    // Timeout reached
    const totalPollingTime = Date.now() - startTime;
    
    Logger.error("Async job polling timeout", {
      jobId,
      timeout: pollingConfig.timeout,
      totalPollingTime,
      pollAttempts,
    });

    return {
      success: false,
      error: `Polling timeout after ${totalPollingTime}ms (${pollAttempts} attempts)`,
      metadata: {
        totalPollingTime,
        pollAttempts,
        finalStatus: 'timeout',
        jobId,
      },
    };
  }

  /**
   * Updates job status in database using sourceDocuments.metadata
   * @param jobId The job ID
   * @param status The current job status
   */
  private static async updateJobStatusInDatabase(
    jobId: string,
    status: AsyncJobStatus
  ): Promise<void> {
    try {
      // Find document by job ID stored in metadata
      const documents = await db
        .select()
        .from(sourceDocuments)
        .where(eq(sourceDocuments.id, jobId)); // Assuming jobId maps to document ID

      if (documents.length === 0) {
        Logger.warn("No document found for job ID", { jobId });
        return;
      }

      const document = documents[0];

      // Update processing status in extractedText field (since metadata field doesn't exist)
      const statusText = status.status === 'completed'
        ? 'Processing completed'
        : `Processing ${status.status}: ${status.progress || 0}%`;

      await db
        .update(sourceDocuments)
        .set({
          extractedText: statusText,
        })
        .where(eq(sourceDocuments.id, jobId));

      Logger.debug("Updated job status in database", {
        jobId,
        status: status.status,
        progress: status.progress,
      });

    } catch (error) {
      Logger.error("Error updating job status in database", {
        jobId,
        error: error instanceof Error ? error.message : String(error),
      });
      // Don't throw - this is a non-critical operation
    }
  }

  /**
   * Retrieves job status from database
   * @param jobId The job ID
   * @returns Current job status from database
   */
  static async getJobStatusFromDatabase(jobId: string): Promise<AsyncJobStatus | null> {
    try {
      const documents = await db
        .select()
        .from(sourceDocuments)
        .where(eq(sourceDocuments.id, jobId));

      if (documents.length === 0) {
        return null;
      }

      // Since metadata field doesn't exist, return basic status based on extractedText
      const extractedText = documents[0].extractedText || '';
      let status: 'pending' | 'processing' | 'completed' | 'failed' = 'pending';

      if (extractedText.includes('Processing completed')) {
        status = 'completed';
      } else if (extractedText.includes('Processing processing')) {
        status = 'processing';
      } else if (extractedText.includes('Processing failed')) {
        status = 'failed';
      }

      return {
        jobId,
        status,
        progress: undefined,
        result: status === 'completed' ? { extractedText } : undefined,
        error: status === 'failed' ? 'Processing failed' : undefined,
        metadata: {
          startTime: Date.now(),
          lastUpdated: Date.now(),
          attempts: 1,
          estimatedCompletion: undefined,
        },
      };

    } catch (error) {
      Logger.error("Error retrieving job status from database", {
        jobId,
        error: error instanceof Error ? error.message : String(error),
      });
      return null;
    }
  }

  /**
   * Sleep utility function
   * @param ms Milliseconds to sleep
   */
  private static sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Creates a status checker function for API Gateway jobs
   * @param documentId Document ID
   * @param checkEndpoint Endpoint to check job status
   * @returns Status checker function
   */
  static createApiGatewayStatusChecker(
    documentId: string,
    checkEndpoint: string
  ): (jobId: string) => Promise<AsyncJobStatus> {
    return async (jobId: string): Promise<AsyncJobStatus> => {
      try {
        const response = await fetch(`${checkEndpoint}/${jobId}`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            ...(process.env.PDF_PROCESSOR_API_KEY && {
              'x-api-key': process.env.PDF_PROCESSOR_API_KEY,
            }),
          },
        });

        if (!response.ok) {
          throw new Error(`Status check failed: ${response.status} ${response.statusText}`);
        }

        const result = await response.json();

        return {
          jobId,
          status: result.status || 'processing',
          progress: result.progress,
          result: result.data,
          error: result.error,
          metadata: {
            startTime: result.startTime || Date.now(),
            lastUpdated: Date.now(),
            attempts: 1,
            estimatedCompletion: result.estimatedCompletion,
          },
        };

      } catch (error) {
        Logger.error("Error checking API Gateway job status", {
          jobId,
          documentId,
          error: error instanceof Error ? error.message : String(error),
        });

        throw error;
      }
    };
  }

  /**
   * Creates a status checker function for Fargate jobs
   * @param documentId Document ID
   * @param checkEndpoint Endpoint to check job status
   * @returns Status checker function
   */
  static createFargateStatusChecker(
    documentId: string,
    checkEndpoint: string
  ): (jobId: string) => Promise<AsyncJobStatus> {
    return async (jobId: string): Promise<AsyncJobStatus> => {
      try {
        // Use the same endpoint pattern as existing Fargate polling: /job-status/{jobId}
        const statusEndpoint = process.env.PDF_PROCESSOR_STATUS_URL ||
          "pdf-processor-lb-1570518785.us-east-1.elb.amazonaws.com/job-status";

        const response = await fetch(`http://${statusEndpoint}/${jobId}`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
        });

        if (!response.ok) {
          throw new Error(`Fargate status check failed: ${response.status} ${response.statusText}`);
        }

        const result = await response.json();

        // Map Fargate response format to our AsyncJobStatus format
        // Fargate uses: { status: "COMPLETED", progress: "100.0%", total_pages: 5 }
        let mappedStatus: 'pending' | 'processing' | 'completed' | 'failed' = 'processing';

        if (result.status === 'COMPLETED' && result.progress === '100.0%') {
          mappedStatus = 'completed';
        } else if (result.status === 'FAILED' || result.error) {
          mappedStatus = 'failed';
        } else if (result.status === 'PROCESSING' || result.progress) {
          mappedStatus = 'processing';
        }

        return {
          jobId,
          status: mappedStatus,
          progress: result.progress ? parseFloat(result.progress.replace('%', '')) / 100 : undefined,
          result: mappedStatus === 'completed' ? result : undefined,
          error: result.error,
          metadata: {
            startTime: result.startTime || Date.now(),
            lastUpdated: Date.now(),
            attempts: 1,
            estimatedCompletion: result.estimatedCompletion,
          },
        };

      } catch (error) {
        Logger.error("Error checking Fargate job status", {
          jobId,
          documentId,
          error: error instanceof Error ? error.message : String(error),
        });

        throw error;
      }
    };
  }
}
