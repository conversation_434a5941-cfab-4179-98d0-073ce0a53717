import { Logger } from "../utils/Logger";
import { InlineCitationsPerformance, OPERATION_TYPES } from "./inline-citations-performance";

/**
 * Advanced relevance scoring system for similarity search results
 * Implements multi-factor scoring beyond basic cosine similarity
 */
export class RelevanceScorer {

  /**
   * Enhanced relevance scoring that combines multiple factors
   */
  static calculateRelevanceScore(
    cosineSimilarity: number,
    chunkContent: string,
    queryContent: string,
    metadata: {
      filename?: string;
      createdAt?: Date;
      pageNumber?: number;
      sourceDocumentId?: string;
    }
  ): RelevanceScore {
    const operationId = `relevance_scoring_${Date.now()}`;
    const timer = InlineCitationsPerformance.startTimer(
      operationId,
      OPERATION_TYPES.SIMILARITY_SEARCH,
      {
        chunkLength: chunkContent.length,
        queryLength: queryContent.length,
        filename: metadata.filename,
      }
    );

    try {
      // Base similarity score (0.0 - 1.0)
      const baseScore = Math.max(0, Math.min(1, cosineSimilarity));

      // Content quality factors
      const contentQualityScore = this.calculateContentQuality(chunkContent);
      const semanticAlignmentScore = this.calculateSemanticAlignment(chunkContent, queryContent);
      const documentTypeScore = this.calculateDocumentTypeScore(metadata.filename);
      const recencyScore = this.calculateRecencyScore(metadata.createdAt);
      const lengthScore = this.calculateLengthScore(chunkContent, queryContent);

      // Weighted combination of factors
      const weights = {
        baseSimilarity: 0.40,      // Primary factor
        contentQuality: 0.20,      // Content structure and completeness
        semanticAlignment: 0.15,   // Query-content semantic match
        documentType: 0.10,        // File type relevance
        recency: 0.10,            // Document freshness
        length: 0.05,             // Content length appropriateness
      };

      const compositeScore =
        (baseScore * weights.baseSimilarity) +
        (contentQualityScore * weights.contentQuality) +
        (semanticAlignmentScore * weights.semanticAlignment) +
        (documentTypeScore * weights.documentType) +
        (recencyScore * weights.recency) +
        (lengthScore * weights.length);

      // Ensure score is within bounds
      const finalScore = Math.max(0, Math.min(1, compositeScore));

      const performanceResult = timer.end();

      const relevanceScore: RelevanceScore = {
        finalScore,
        baseScore,
        factors: {
          contentQuality: contentQualityScore,
          semanticAlignment: semanticAlignmentScore,
          documentType: documentTypeScore,
          recency: recencyScore,
          length: lengthScore,
        },
        weights,
        metadata: {
          chunkLength: chunkContent.length,
          queryLength: queryContent.length,
          processingTime: performanceResult.duration,
        }
      };

      Logger.debug("Relevance score calculated", {
        finalScore: finalScore.toFixed(3),
        baseScore: baseScore.toFixed(3),
        factors: Object.entries(relevanceScore.factors).map(([key, value]) =>
          `${key}: ${value.toFixed(3)}`
        ).join(', '),
        processingTime: `${performanceResult.duration.toFixed(2)}ms`,
      });

      return relevanceScore;

    } catch (error) {
      timer.end();
      Logger.error("Error calculating relevance score", { error });

      // Fallback to basic similarity score
      return {
        finalScore: Math.max(0, Math.min(1, cosineSimilarity)),
        baseScore: cosineSimilarity,
        factors: {
          contentQuality: 0.5,
          semanticAlignment: 0.5,
          documentType: 0.5,
          recency: 0.5,
          length: 0.5,
        },
        weights: {
          baseSimilarity: 1.0,
          contentQuality: 0,
          semanticAlignment: 0,
          documentType: 0,
          recency: 0,
          length: 0,
        },
        metadata: {
          chunkLength: chunkContent.length,
          queryLength: queryContent.length,
          processingTime: 0,
          error: error instanceof Error ? error.message : "Unknown error",
        }
      };
    }
  }

  /**
   * Calculates content quality based on structure, completeness, and readability
   */
  private static calculateContentQuality(content: string): number {
    let score = 0.5; // Base score

    // Length factor (not too short, not too long)
    const length = content.length;
    if (length >= 50 && length <= 1000) {
      score += 0.2;
    } else if (length >= 20 && length <= 2000) {
      score += 0.1;
    }

    // Structure indicators
    const hasProperSentences = /[.!?]\s+[A-Z]/.test(content);
    const hasParagraphs = content.includes('\n\n') || content.includes('\n');
    const hasNumbers = /\d/.test(content);
    const hasProperCapitalization = /^[A-Z]/.test(content.trim());

    if (hasProperSentences) score += 0.1;
    if (hasParagraphs) score += 0.05;
    if (hasNumbers) score += 0.05;
    if (hasProperCapitalization) score += 0.05;

    // Avoid very repetitive content
    const words = content.toLowerCase().split(/\s+/);
    const uniqueWords = new Set(words);
    const uniqueRatio = uniqueWords.size / words.length;
    if (uniqueRatio > 0.7) {
      score += 0.1;
    } else if (uniqueRatio < 0.3) {
      score -= 0.1;
    }

    return Math.max(0, Math.min(1, score));
  }

  /**
   * Calculates semantic alignment between query and content
   */
  private static calculateSemanticAlignment(content: string, query: string): number {
    const contentWords = this.extractKeywords(content.toLowerCase());
    const queryWords = this.extractKeywords(query.toLowerCase());

    if (queryWords.length === 0) return 0.5;

    // Calculate keyword overlap
    const overlap = queryWords.filter(word => contentWords.includes(word));
    const keywordScore = overlap.length / queryWords.length;

    // Boost for exact phrase matches
    const queryPhrases = this.extractPhrases(query.toLowerCase());
    const contentLower = content.toLowerCase();
    const phraseMatches = queryPhrases.filter(phrase => contentLower.includes(phrase));
    const phraseScore = phraseMatches.length / Math.max(1, queryPhrases.length);

    // Combined score with phrase matches weighted higher
    const alignmentScore = (keywordScore * 0.6) + (phraseScore * 0.4);

    return Math.max(0, Math.min(1, alignmentScore));
  }

  /**
   * Calculates document type relevance score
   */
  private static calculateDocumentTypeScore(filename?: string): number {
    if (!filename) return 0.5;

    const extension = filename.toLowerCase().split('.').pop();

    // Document type preferences for legal/research content
    const typeScores: Record<string, number> = {
      'pdf': 0.9,    // High preference for PDFs (legal documents)
      'docx': 0.8,   // Word documents
      'doc': 0.8,    // Legacy Word documents
      'txt': 0.6,    // Plain text
      'md': 0.7,     // Markdown documents
      'html': 0.5,   // Web content
      'rtf': 0.6,    // Rich text format
    };

    return typeScores[extension || ''] || 0.5;
  }

  /**
   * Calculates recency score based on document age
   */
  private static calculateRecencyScore(createdAt?: Date): number {
    if (!createdAt) return 0.5;

    const now = new Date();
    const ageInDays = (now.getTime() - createdAt.getTime()) / (1000 * 60 * 60 * 24);

    // Recency scoring: newer documents get higher scores
    if (ageInDays <= 7) return 1.0;      // Last week
    if (ageInDays <= 30) return 0.9;     // Last month
    if (ageInDays <= 90) return 0.8;     // Last quarter
    if (ageInDays <= 365) return 0.6;    // Last year

    return 0.4; // Older than a year
  }

  /**
   * Calculates length appropriateness score
   */
  private static calculateLengthScore(content: string, query: string): number {
    const contentLength = content.length;
    const queryLength = query.length;

    // Ideal content length relative to query
    const idealRatio = Math.max(3, Math.min(20, queryLength / 10));
    const actualRatio = contentLength / queryLength;

    // Score based on how close to ideal ratio
    const ratioScore = 1 - Math.abs(actualRatio - idealRatio) / idealRatio;

    return Math.max(0.2, Math.min(1, ratioScore));
  }
  
  /**
   * Extracts meaningful keywords from text
   */
  private static extractKeywords(text: string): string[] {
    // Remove common stop words
    const stopWords = new Set([
      'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by',
      'is', 'are', 'was', 'were', 'be', 'been', 'have', 'has', 'had', 'do', 'does', 'did',
      'will', 'would', 'could', 'should', 'may', 'might', 'can', 'this', 'that', 'these', 'those'
    ]);

    return text
      .split(/\s+/)
      .map(word => word.replace(/[^\w]/g, ''))
      .filter(word => word.length > 2 && !stopWords.has(word))
      .slice(0, 20); // Limit to top 20 keywords
  }

  /**
   * Extracts meaningful phrases from text
   */
  private static extractPhrases(text: string): string[] {
    // Extract 2-4 word phrases
    const words = text.split(/\s+/);
    const phrases: string[] = [];

    for (let i = 0; i < words.length - 1; i++) {
      // 2-word phrases
      if (i < words.length - 1) {
        phrases.push(`${words[i]} ${words[i + 1]}`);
      }
      // 3-word phrases
      if (i < words.length - 2) {
        phrases.push(`${words[i]} ${words[i + 1]} ${words[i + 2]}`);
      }
    }

    return phrases.slice(0, 10); // Limit to top 10 phrases
  }

  /**
   * Ranks and filters results based on relevance scores
   */
  static rankResults<T extends { similarity: number }>(
    results: T[],
    queryContent: string,
    options: {
      minScore?: number;
      maxResults?: number;
      diversityThreshold?: number;
    } = {}
  ): Array<T & { relevanceScore: RelevanceScore }> {
    const {
      minScore = 0.3,
      maxResults = 20,
      diversityThreshold = 0.85
    } = options;

    // Calculate relevance scores for all results
    const scoredResults = results.map(result => ({
      ...result,
      relevanceScore: this.calculateRelevanceScore(
        result.similarity,
        (result as any).chunkText || (result as any).content || '',
        queryContent,
        {
          filename: (result as any).filename,
          createdAt: (result as any).createdAt,
          pageNumber: (result as any).pageNumber,
          sourceDocumentId: (result as any).sourceDocumentId,
        }
      )
    }));

    // Filter by minimum score
    const filteredResults = scoredResults.filter(
      result => result.relevanceScore.finalScore >= minScore
    );

    // Sort by relevance score (descending)
    const sortedResults = filteredResults.sort(
      (a, b) => b.relevanceScore.finalScore - a.relevanceScore.finalScore
    );

    // Apply diversity filtering to avoid too similar results
    const diverseResults = this.applyDiversityFilter(sortedResults, diversityThreshold);

    // Limit to max results
    return diverseResults.slice(0, maxResults);
  }

  /**
   * Applies diversity filtering to avoid too similar results
   */
  private static applyDiversityFilter<T extends { similarity: number }>(
    results: Array<T & { relevanceScore: RelevanceScore }>,
    threshold: number
  ): Array<T & { relevanceScore: RelevanceScore }> {
    if (results.length <= 1) return results;

    const diverseResults: Array<T & { relevanceScore: RelevanceScore }> = [results[0]];

    for (let i = 1; i < results.length; i++) {
      const candidate = results[i];
      let isDiverse = true;

      // Check similarity with already selected results
      for (const selected of diverseResults) {
        const contentSimilarity = this.calculateContentSimilarity(
          (candidate as any).chunkText || (candidate as any).content || '',
          (selected as any).chunkText || (selected as any).content || ''
        );

        if (contentSimilarity > threshold) {
          isDiverse = false;
          break;
        }
      }

      if (isDiverse) {
        diverseResults.push(candidate);
      }
    }

    return diverseResults;
  }

  /**
   * Calculates content similarity for diversity filtering
   */
  private static calculateContentSimilarity(content1: string, content2: string): number {
    const words1 = new Set(this.extractKeywords(content1.toLowerCase()));
    const words2 = new Set(this.extractKeywords(content2.toLowerCase()));

    const intersection = new Set([...words1].filter(word => words2.has(word)));
    const union = new Set([...words1, ...words2]);

    return union.size > 0 ? intersection.size / union.size : 0;
  }
}

// Types
export interface RelevanceScore {
  finalScore: number;
  baseScore: number;
  factors: {
    contentQuality: number;
    semanticAlignment: number;
    documentType: number;
    recency: number;
    length: number;
  };
  weights: {
    baseSimilarity: number;
    contentQuality: number;
    semanticAlignment: number;
    documentType: number;
    recency: number;
    length: number;
  };
  metadata: {
    chunkLength: number;
    queryLength: number;
    processingTime: number;
    error?: string;
  };
}
