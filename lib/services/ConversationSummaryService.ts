import { Logger } from "../utils/Logger";
import { GoogleGenerativeAI } from "@google/generative-ai";
import { getGooglePalmApiKey } from "../utils/apiKeyRotation";

export class ConversationSummaryService {
  private static readonly genAI = new GoogleGenerativeAI(
    getGooglePalmApiKey()
  );

  static async summarizeConversation(
    messages: { role: string; content: string }[]
  ): Promise<string> {
    try {
      const model = this.genAI.getGenerativeModel({
        model: "gemini-2.0-flash-lite-preview-02-05",
      });

      const conversationText = messages
        .map((msg) => `${msg.role}: ${msg.content}`)
        .join("\n");

      const prompt = `
You are an expert conversation summarizer. Provide a concise summary of the following conversation.
Focus on the key topics, questions, and information exchanged.

CONVERSATION:
${conversationText}

SUMMARY:`;

      const { response } = await model.generateContent(prompt);
      const summary = response?.candidates?.[0]?.content?.parts
        .map((p) => p.text)
        .join("\n");

      Logger.debug("Generated conversation summary:", summary);
      return summary || "";
    } catch (error) {
      Logger.error("Error summarizing conversation:", error);
      return "";
    }
  }
}