import { Logger } from "../utils/Logger";

export interface PerformanceMetrics {
  operationName: string;
  startTime: number;
  endTime: number;
  duration: number;
  metadata?: Record<string, any>;
}

export interface CitationPipelineMetrics {
  totalDuration: number;
  documentProcessing: PerformanceMetrics[];
  chunkingOperations: PerformanceMetrics[];
  embeddingOperations: PerformanceMetrics[];
  similaritySearch: PerformanceMetrics[];
  citationGeneration: PerformanceMetrics[];
  databaseOperations: PerformanceMetrics[];
  memoryUsage?: {
    heapUsed: number;
    heapTotal: number;
    external: number;
  };
}

export interface PerformanceRecommendation {
  category: "database" | "embedding" | "chunking" | "similarity" | "general";
  priority: "high" | "medium" | "low";
  issue: string;
  recommendation: string;
  estimatedImpact: string;
  complexity: "low" | "medium" | "high";
}

export class CitationPerformanceMonitor {
  private static metrics: CitationPipelineMetrics = {
    totalDuration: 0,
    documentProcessing: [],
    chunkingOperations: [],
    embeddingOperations: [],
    similaritySearch: [],
    citationGeneration: [],
    databaseOperations: []
  };

  private static activeTimers: Map<string, number> = new Map();

  /**
   * Starts timing an operation
   */
  static startTimer(operationId: string): void {
    this.activeTimers.set(operationId, Date.now());
  }

  /**
   * Ends timing an operation and records the metric
   */
  static endTimer(
    operationId: string, 
    operationName: string, 
    category: keyof Omit<CitationPipelineMetrics, 'totalDuration' | 'memoryUsage'>,
    metadata?: Record<string, any>
  ): PerformanceMetrics {
    const startTime = this.activeTimers.get(operationId);
    if (!startTime) {
      Logger.warn("Timer not found for operation", { operationId, operationName });
      return {
        operationName,
        startTime: Date.now(),
        endTime: Date.now(),
        duration: 0,
        metadata
      };
    }

    const endTime = Date.now();
    const duration = endTime - startTime;

    const metric: PerformanceMetrics = {
      operationName,
      startTime,
      endTime,
      duration,
      metadata
    };

    // Record the metric in the appropriate category
    this.metrics[category].push(metric);
    this.activeTimers.delete(operationId);

    Logger.debug("Performance metric recorded", {
      operationName,
      category,
      duration,
      metadata
    });

    return metric;
  }

  /**
   * Records memory usage at a specific point
   */
  static recordMemoryUsage(): void {
    if (typeof process !== 'undefined' && process.memoryUsage) {
      const memUsage = process.memoryUsage();
      this.metrics.memoryUsage = {
        heapUsed: memUsage.heapUsed,
        heapTotal: memUsage.heapTotal,
        external: memUsage.external
      };
    }
  }

  /**
   * Gets current performance metrics
   */
  static getMetrics(): CitationPipelineMetrics {
    return { ...this.metrics };
  }

  /**
   * Resets all metrics
   */
  static resetMetrics(): void {
    this.metrics = {
      totalDuration: 0,
      documentProcessing: [],
      chunkingOperations: [],
      embeddingOperations: [],
      similaritySearch: [],
      citationGeneration: [],
      databaseOperations: []
    };
    this.activeTimers.clear();
  }

  /**
   * Analyzes performance and provides recommendations
   */
  static analyzePerformance(): PerformanceRecommendation[] {
    const recommendations: PerformanceRecommendation[] = [];
    const metrics = this.getMetrics();

    // Analyze document processing performance
    const avgDocProcessing = this.calculateAverage(metrics.documentProcessing);
    if (avgDocProcessing > 10000) { // > 10 seconds
      recommendations.push({
        category: "general",
        priority: "high",
        issue: `Document processing is slow (avg: ${Math.round(avgDocProcessing/1000)}s)`,
        recommendation: "Consider implementing document caching or switching to faster OCR service",
        estimatedImpact: "50-70% reduction in processing time",
        complexity: "medium"
      });
    }

    // Analyze chunking performance
    const avgChunking = this.calculateAverage(metrics.chunkingOperations);
    if (avgChunking > 2000) { // > 2 seconds
      recommendations.push({
        category: "chunking",
        priority: "medium",
        issue: `Text chunking is slow (avg: ${Math.round(avgChunking/1000)}s)`,
        recommendation: "Optimize chunking algorithm or reduce chunk overlap",
        estimatedImpact: "20-30% reduction in chunking time",
        complexity: "low"
      });
    }

    // Analyze embedding performance
    const avgEmbedding = this.calculateAverage(metrics.embeddingOperations);
    if (avgEmbedding > 5000) { // > 5 seconds
      recommendations.push({
        category: "embedding",
        priority: "high",
        issue: `Embedding generation is slow (avg: ${Math.round(avgEmbedding/1000)}s)`,
        recommendation: "Implement batch embedding processing or use faster embedding model",
        estimatedImpact: "40-60% reduction in embedding time",
        complexity: "medium"
      });
    }

    // Analyze similarity search performance
    const avgSimilarity = this.calculateAverage(metrics.similaritySearch);
    if (avgSimilarity > 3000) { // > 3 seconds
      recommendations.push({
        category: "similarity",
        priority: "high",
        issue: `Similarity search is slow (avg: ${Math.round(avgSimilarity/1000)}s)`,
        recommendation: "Add database indexes on embedding columns or implement vector caching",
        estimatedImpact: "60-80% reduction in search time",
        complexity: "medium"
      });
    }

    // Analyze database performance
    const avgDatabase = this.calculateAverage(metrics.databaseOperations);
    if (avgDatabase > 1000) { // > 1 second
      recommendations.push({
        category: "database",
        priority: "medium",
        issue: `Database operations are slow (avg: ${Math.round(avgDatabase/1000)}s)`,
        recommendation: "Optimize database queries and add appropriate indexes",
        estimatedImpact: "30-50% reduction in database time",
        complexity: "low"
      });
    }

    // Analyze memory usage
    if (metrics.memoryUsage && metrics.memoryUsage.heapUsed > 500 * 1024 * 1024) { // > 500MB
      recommendations.push({
        category: "general",
        priority: "medium",
        issue: `High memory usage (${Math.round(metrics.memoryUsage.heapUsed / 1024 / 1024)}MB)`,
        recommendation: "Implement streaming processing or reduce batch sizes",
        estimatedImpact: "Reduced memory footprint and better scalability",
        complexity: "high"
      });
    }

    // Sort recommendations by priority and impact
    return recommendations.sort((a, b) => {
      const priorityOrder = { high: 3, medium: 2, low: 1 };
      return priorityOrder[b.priority] - priorityOrder[a.priority];
    });
  }

  /**
   * Generates a performance report
   */
  static generatePerformanceReport(): string {
    const metrics = this.getMetrics();
    const recommendations = this.analyzePerformance();

    let report = "# Citation Pipeline Performance Report\n\n";

    // Overall metrics
    report += "## Overall Performance\n";
    report += `- Total Pipeline Duration: ${Math.round(metrics.totalDuration / 1000)}s\n`;
    
    if (metrics.memoryUsage) {
      report += `- Memory Usage: ${Math.round(metrics.memoryUsage.heapUsed / 1024 / 1024)}MB\n`;
    }

    // Category breakdown
    const categories = [
      { name: "Document Processing", data: metrics.documentProcessing },
      { name: "Chunking Operations", data: metrics.chunkingOperations },
      { name: "Embedding Operations", data: metrics.embeddingOperations },
      { name: "Similarity Search", data: metrics.similaritySearch },
      { name: "Citation Generation", data: metrics.citationGeneration },
      { name: "Database Operations", data: metrics.databaseOperations }
    ];

    report += "\n## Performance Breakdown\n";
    categories.forEach(category => {
      if (category.data.length > 0) {
        const avg = this.calculateAverage(category.data);
        const total = category.data.reduce((sum, op) => sum + op.duration, 0);
        report += `- ${category.name}: ${category.data.length} ops, avg ${Math.round(avg)}ms, total ${Math.round(total)}ms\n`;
      }
    });

    // Recommendations
    if (recommendations.length > 0) {
      report += "\n## Performance Recommendations\n";
      recommendations.forEach((rec, index) => {
        report += `\n### ${index + 1}. ${rec.issue} (${rec.priority} priority)\n`;
        report += `**Recommendation:** ${rec.recommendation}\n`;
        report += `**Estimated Impact:** ${rec.estimatedImpact}\n`;
        report += `**Implementation Complexity:** ${rec.complexity}\n`;
      });
    } else {
      report += "\n## Performance Recommendations\n";
      report += "✅ No performance issues detected. Pipeline is performing well!\n";
    }

    return report;
  }

  /**
   * Calculates average duration for a set of metrics
   */
  private static calculateAverage(metrics: PerformanceMetrics[]): number {
    if (metrics.length === 0) return 0;
    const total = metrics.reduce((sum, metric) => sum + metric.duration, 0);
    return total / metrics.length;
  }

  /**
   * Logs performance summary
   */
  static logPerformanceSummary(messageId: string): void {
    const metrics = this.getMetrics();
    const recommendations = this.analyzePerformance();

    Logger.info("Citation pipeline performance summary", {
      messageId,
      totalDuration: metrics.totalDuration,
      operationCounts: {
        documentProcessing: metrics.documentProcessing.length,
        chunking: metrics.chunkingOperations.length,
        embedding: metrics.embeddingOperations.length,
        similarity: metrics.similaritySearch.length,
        citation: metrics.citationGeneration.length,
        database: metrics.databaseOperations.length
      },
      recommendationCount: recommendations.length,
      highPriorityIssues: recommendations.filter(r => r.priority === 'high').length,
      memoryUsage: metrics.memoryUsage
    });

    if (recommendations.length > 0) {
      Logger.warn("Performance recommendations available", {
        messageId,
        recommendations: recommendations.map(r => ({
          category: r.category,
          priority: r.priority,
          issue: r.issue
        }))
      });
    }
  }
}
