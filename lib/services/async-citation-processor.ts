import { Logger } from "../utils/Logger";
import { InlineCitationsGenerator } from "./inline-citations-generator";
import { InlineCitationsTrigger } from "./inline-citations-trigger";
import { db } from "../db";
import { message } from "../db/schema";
import { eq } from "drizzle-orm";

// Configuration for async citation processing
const ASYNC_CITATION_CONFIG = {
  ENABLE_BACKGROUND_PROCESSING: true,
  PROCESSING_DELAY_MS: 100, // Small delay to ensure message is saved
  MAX_RETRY_ATTEMPTS: 3,
  RETRY_DELAY_MS: 1000,
} as const;

export interface AsyncCitationStatus {
  status: "pending" | "processing" | "completed" | "failed";
  startedAt?: Date;
  completedAt?: Date;
  error?: string;
  citationCount?: number;
  processingTimeMs?: number;
}

export interface AsyncCitationResult {
  success: boolean;
  messageId: string;
  originalContent: string;
  citedContent?: string;
  citationStatus: AsyncCitationStatus;
  citationMetadata?: any;
}

export class AsyncCitationProcessor {
  /**
   * Triggers asynchronous citation processing for a message
   * @param messageId The message ID to process
   * @param chatId The chat ID
   * @param content The original message content
   * @param messages The chat messages for trigger evaluation
   * @param chatExists Whether the chat exists
   * @param isReadonly Whether the chat is readonly
   */
  static async triggerAsyncCitationProcessing(
    messageId: string,
    chatId: string,
    content: string,
    messages: any[],
    chatExists: boolean,
    isReadonly: boolean = false
  ): Promise<void> {
    if (!ASYNC_CITATION_CONFIG.ENABLE_BACKGROUND_PROCESSING) {
      Logger.debug("Async citation processing disabled", { messageId });
      return;
    }

    Logger.info("Triggering async citation processing", {
      messageId,
      chatId,
      contentLength: content.length,
    });

    // Process citations in the background without blocking
    setImmediate(async () => {
      try {
        // Small delay to ensure the message is saved
        await new Promise(resolve => setTimeout(resolve, ASYNC_CITATION_CONFIG.PROCESSING_DELAY_MS));

        await this.processMessageCitations(
          messageId,
          chatId,
          content,
          messages,
          chatExists,
          isReadonly
        );
      } catch (error) {
        Logger.error("Error in async citation processing", {
          messageId,
          chatId,
          error,
        });
      }
    });
  }

  /**
   * Processes citations for a message with retry logic
   */
  private static async processMessageCitations(
    messageId: string,
    chatId: string,
    content: string,
    messages: any[],
    chatExists: boolean,
    isReadonly: boolean,
    attempt: number = 1
  ): Promise<AsyncCitationResult> {
    const startTime = Date.now();

    try {
      Logger.info("Starting async citation processing", {
        messageId,
        chatId,
        attempt,
        maxAttempts: ASYNC_CITATION_CONFIG.MAX_RETRY_ATTEMPTS,
      });

      // Update message status to processing
      await this.updateMessageCitationStatus(messageId, {
        status: "processing",
        startedAt: new Date(),
      });

      // Evaluate trigger conditions
      const triggerResult = await InlineCitationsTrigger.shouldTriggerInlineCitations(
        messageId,
        chatId,
        messages,
        chatExists,
        isReadonly
      );

      if (!triggerResult.shouldTrigger) {
        Logger.info("Citation processing skipped - trigger conditions not met", {
          messageId,
          reason: triggerResult.reason,
        });

        // Update message with final trigger result and completion status
        await this.updateMessageWithTriggerResult(messageId, triggerResult, {
          status: "completed",
          completedAt: new Date(),
          processingTimeMs: Date.now() - startTime,
          citationCount: 0,
        });

        return {
          success: true,
          messageId,
          originalContent: content,
          citationStatus: {
            status: "completed",
            completedAt: new Date(),
            processingTimeMs: Date.now() - startTime,
            citationCount: 0,
          },
        };
      }

      // Generate citations
      const citationResult = await InlineCitationsGenerator.generateInlineCitations(
        messageId,
        chatId,
        content
      );

      if (citationResult.success) {
        // Update message with citations
        await this.updateMessageWithCitations(messageId, citationResult, triggerResult);

        const processingTime = Date.now() - startTime;
        Logger.info("Async citation processing completed successfully", {
          messageId,
          citationCount: citationResult.citationCount,
          processingTimeMs: processingTime,
        });

        return {
          success: true,
          messageId,
          originalContent: content,
          citedContent: citationResult.citedContent,
          citationStatus: {
            status: "completed",
            completedAt: new Date(),
            processingTimeMs: processingTime,
            citationCount: citationResult.citationCount,
          },
          citationMetadata: {
            triggered: triggerResult.shouldTrigger,
            triggerReason: triggerResult.reason,
            citations: citationResult.citations,
            insertionLocations: citationResult.insertionLocations,
          },
        };
      } else {
        throw new Error(citationResult.error || "Citation generation failed");
      }

    } catch (error) {
      Logger.error("Error in citation processing", {
        messageId,
        attempt,
        error,
      });

      // Retry logic
      if (attempt < ASYNC_CITATION_CONFIG.MAX_RETRY_ATTEMPTS) {
        Logger.info("Retrying citation processing", {
          messageId,
          nextAttempt: attempt + 1,
        });

        await new Promise(resolve => setTimeout(resolve, ASYNC_CITATION_CONFIG.RETRY_DELAY_MS));
        return this.processMessageCitations(
          messageId,
          chatId,
          content,
          messages,
          chatExists,
          isReadonly,
          attempt + 1
        );
      }

      // Final failure
      await this.updateMessageCitationStatus(messageId, {
        status: "failed",
        completedAt: new Date(),
        error: error instanceof Error ? error.message : String(error),
        processingTimeMs: Date.now() - startTime,
      });

      return {
        success: false,
        messageId,
        originalContent: content,
        citationStatus: {
          status: "failed",
          completedAt: new Date(),
          error: error instanceof Error ? error.message : String(error),
          processingTimeMs: Date.now() - startTime,
        },
      };
    }
  }

  /**
   * Updates message with trigger result and citation status
   */
  private static async updateMessageWithTriggerResult(
    messageId: string,
    triggerResult: any,
    status: Partial<AsyncCitationStatus>
  ): Promise<void> {
    try {
      // Get current message
      const [currentMessage] = await db
        .select()
        .from(message)
        .where(eq(message.id, messageId))
        .limit(1);

      if (!currentMessage) {
        Logger.error("Message not found for trigger result update", { messageId });
        return;
      }

      const currentMetadata = currentMessage.metadata as any || {};
      const updatedMetadata = {
        ...currentMetadata,
        inlineCitations: {
          ...currentMetadata.inlineCitations,
          triggered: triggerResult.shouldTrigger,
          triggerReason: triggerResult.reason,
          triggerConditions: triggerResult.conditions,
          asyncStatus: {
            ...currentMetadata.inlineCitations?.asyncStatus,
            ...status,
          },
        },
      };

      // Update message in database
      await db
        .update(message)
        .set({
          metadata: updatedMetadata,
        })
        .where(eq(message.id, messageId));

      Logger.info("Message trigger result updated", {
        messageId,
        triggered: triggerResult.shouldTrigger,
        status: status.status,
      });
    } catch (error) {
      Logger.error("Error updating message trigger result", {
        messageId,
        error,
      });
    }
  }

  /**
   * Updates message citation status in metadata
   */
  private static async updateMessageCitationStatus(
    messageId: string,
    status: Partial<AsyncCitationStatus>
  ): Promise<void> {
    try {
      // Get current message
      const [currentMessage] = await db
        .select()
        .from(message)
        .where(eq(message.id, messageId))
        .limit(1);

      if (!currentMessage) {
        Logger.error("Message not found for citation status update", { messageId });
        return;
      }

      const currentMetadata = currentMessage.metadata as any || {};
      const updatedMetadata = {
        ...currentMetadata,
        inlineCitations: {
          ...currentMetadata.inlineCitations,
          asyncStatus: {
            ...currentMetadata.inlineCitations?.asyncStatus,
            ...status,
          },
        },
      };

      await db
        .update(message)
        .set({ metadata: updatedMetadata })
        .where(eq(message.id, messageId));

      Logger.debug("Updated message citation status", {
        messageId,
        status,
      });

    } catch (error) {
      Logger.error("Error updating message citation status", {
        messageId,
        error,
      });
    }
  }

  /**
   * Updates message with citation results
   */
  private static async updateMessageWithCitations(
    messageId: string,
    citationResult: any,
    triggerResult: any
  ): Promise<void> {
    try {
      // Get current message
      const [currentMessage] = await db
        .select()
        .from(message)
        .where(eq(message.id, messageId))
        .limit(1);

      if (!currentMessage) {
        Logger.error("Message not found for citation update", { messageId });
        return;
      }

      const currentMetadata = currentMessage.metadata as any || {};
      const updatedMetadata = {
        ...currentMetadata,
        inlineCitations: {
          triggered: triggerResult.shouldTrigger,
          triggerReason: triggerResult.reason,
          triggerConditions: triggerResult.conditions,
          enabled: true,
          citationCount: citationResult.citationCount,
          citations: citationResult.citations,
          insertionLocations: citationResult.insertionLocations,
          originalContentLength: citationResult.originalContent.length,
          citedContentLength: citationResult.citedContent.length,
          asyncStatus: {
            status: "completed",
            completedAt: new Date(),
            citationCount: citationResult.citationCount,
          },
        },
      };

      // Update both content and metadata
      await db
        .update(message)
        .set({
          content: citationResult.citedContent,
          metadata: updatedMetadata
        })
        .where(eq(message.id, messageId));

      Logger.info("Updated message with citations", {
        messageId,
        citationCount: citationResult.citationCount,
        contentLengthChange: citationResult.citedContent.length - citationResult.originalContent.length,
      });

    } catch (error) {
      Logger.error("Error updating message with citations", {
        messageId,
        error,
      });
    }
  }
}
