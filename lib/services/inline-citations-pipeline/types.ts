/**
 * Common types and interfaces for the inline citations pipeline
 */

// Base result interface for all pipeline operations
export interface PipelineResult<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  metadata?: Record<string, any>;
  performance?: PerformanceMetrics;
}

// Performance metrics for pipeline operations
export interface PerformanceMetrics {
  startTime: number;
  endTime: number;
  duration: number;
  memoryUsage?: {
    heapUsed: number;
    heapTotal: number;
    external: number;
  };
  operationCounts?: Record<string, number>;
}

// Document page interface (from PDF processing)
export interface DocumentPage {
  pageNumber: number;
  imageBase64: string;
  width?: number;
  height?: number;
  metadata?: {
    scaleFactor: number;
    quality: number;
    renderingApproach: string;
    extractionMethod?: string;
    compatibilityMode?: string;
    modeAgnostic?: boolean;
    toggleStateIndependent?: boolean;
    qualityValidated?: boolean;
  };
}

// OCR processing result
export interface OcrResult {
  success: boolean;
  markdown: string;
  pagesProcessed: number;
  error?: string;
  metadata?: {
    model: string;
    processingTime: number;
    tokenCount: number;
    qualityScore?: number;
  };
}

// Markdown chunk for embedding
export interface MarkdownChunk {
  id: string;
  text: string;
  tokenCount: number;
  chunkIndex: number;
  metadata: {
    document_id: string;
    source_type: 'pdf' | 'docx' | 'txt' | 'image';
    page_numbers?: number[];
    section_title?: string;
    chunk_strategy: string;
    created_at: Date;
  };
}

// Embedding result
export interface EmbeddingResult {
  success: boolean;
  processedChunks: number;
  totalEmbeddings: number;
  averageVectorLength: number;
  error?: string;
  metadata?: {
    provider: string;
    model: string;
    batchSize: number;
    processingTime: number;
  };
}

// Citation trigger conditions
export interface TriggerConditions {
  hasDocuments: boolean;
  documentCount: number;
  hasRecentDocuments: boolean;
  messageLength: number;
  isReadonly: boolean;
  chatExists: boolean;
  performanceAllowed: boolean;
  customConditions?: Record<string, boolean>;
}

// Citation trigger result
export interface TriggerResult {
  shouldTrigger: boolean;
  conditions: TriggerConditions;
  reason: string;
  messageId: string;
  chatId: string;
  confidence?: number;
}

// Citation match from similarity search
export interface CitationMatch {
  chunkId: string;
  documentId: string;
  similarity: number;
  text: string;
  metadata: {
    page_numbers?: number[];
    section_title?: string;
    document_title?: string;
    chunk_index: number;
  };
  contextualScore?: number;
}

// Generated citation
export interface GeneratedCitation {
  id: string;
  text: string;
  sourceDocument: {
    id: string;
    title: string;
    url?: string;
  };
  pageNumbers?: number[];
  similarity: number;
  position: {
    start: number;
    end: number;
  };
  metadata?: Record<string, any>;
}

// Citation generation result
export interface CitationGenerationResult {
  success: boolean;
  originalContent: string;
  citedContent: string;
  citations: GeneratedCitation[];
  referencesSection?: string;
  error?: string;
  metadata?: {
    totalMatches: number;
    averageSimilarity: number;
    processingTime: number;
    citationCount: number;
  };
}

// Pipeline stage status
export type PipelineStageStatus = 'pending' | 'processing' | 'completed' | 'failed' | 'skipped';

// Pipeline execution context
export interface PipelineContext {
  documentId: string;
  messageId?: string;
  chatId?: string;
  userId?: string;
  sessionId?: string;
  requestId?: string;
  startTime: number;
  stages: Record<string, {
    status: PipelineStageStatus;
    startTime?: number;
    endTime?: number;
    error?: string;
    metadata?: Record<string, any>;
  }>;
  globalMetadata?: Record<string, any>;
}

// Module interface that all pipeline modules must implement
export interface PipelineModule<TInput = any, TOutput = any> {
  readonly name: string;
  readonly version: string;
  readonly dependencies?: string[];
  
  // Execute the module's main functionality
  execute(input: TInput, context: PipelineContext): Promise<PipelineResult<TOutput>>;
  
  // Validate input before execution
  validateInput?(input: TInput): boolean;
  
  // Clean up resources after execution
  cleanup?(context: PipelineContext): Promise<void>;
  
  // Get module health status
  getHealthStatus?(): Promise<{ healthy: boolean; details?: Record<string, any> }>;
}

// Pipeline orchestrator interface
export interface PipelineOrchestrator {
  // Execute the complete pipeline
  execute(input: any, context?: Partial<PipelineContext>): Promise<PipelineResult>;
  
  // Execute specific stages
  executeStage(stageName: string, input: any, context: PipelineContext): Promise<PipelineResult>;
  
  // Get pipeline status
  getStatus(contextId: string): Promise<PipelineContext | null>;
  
  // Register a new module
  registerModule(module: PipelineModule): void;
  
  // Get registered modules
  getModules(): Record<string, PipelineModule>;
}

// Error types for better error handling
export class PipelineError extends Error {
  constructor(
    message: string,
    public readonly stage: string,
    public readonly code: string,
    public readonly context?: Record<string, any>
  ) {
    super(message);
    this.name = 'PipelineError';
  }
}

export class ValidationError extends PipelineError {
  constructor(message: string, stage: string, context?: Record<string, any>) {
    super(message, stage, 'VALIDATION_ERROR', context);
    this.name = 'ValidationError';
  }
}

export class ProcessingError extends PipelineError {
  constructor(message: string, stage: string, context?: Record<string, any>) {
    super(message, stage, 'PROCESSING_ERROR', context);
    this.name = 'ProcessingError';
  }
}

export class ConfigurationError extends PipelineError {
  constructor(message: string, stage: string, context?: Record<string, any>) {
    super(message, stage, 'CONFIGURATION_ERROR', context);
    this.name = 'ConfigurationError';
  }
}



// Event types for pipeline monitoring
export interface PipelineEvent {
  type: 'stage_started' | 'stage_completed' | 'stage_failed' | 'pipeline_completed' | 'pipeline_failed' | 'pipeline_started';
  timestamp: number;
  contextId: string;
  stage?: string;
  data?: Record<string, any>;
  error?: string;
}

// Pipeline metrics for monitoring and optimization
export interface PipelineMetrics {
  contextId: string;
  totalDuration: number;
  stageMetrics: Record<string, {
    duration: number;
    memoryUsage?: number;
    success: boolean;
    retryCount?: number;
  }>;
  resourceUsage: {
    peakMemory: number;
    totalCpuTime: number;
    networkRequests: number;
    databaseQueries: number;
  };
  qualityMetrics?: {
    ocrAccuracy?: number;
    embeddingQuality?: number;
    citationRelevance?: number;
  };
}
