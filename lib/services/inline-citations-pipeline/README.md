# Inline Citations Pipeline

A modular, reusable pipeline for processing documents and generating inline citations. The pipeline is designed with clear separation of concerns, making it easy to maintain, test, and extend.

## Architecture Overview

The pipeline consists of 8 modular components that can be used independently or orchestrated together:

### Core Processing Modules

1. **PDF Processing Module** - Converts PDF documents to images using server-side PDF.js
2. **OCR Processing Module** - Converts images to markdown using Gemini OCR
3. **Markdown Storage Module** - Stores processed markdown in the database
4. **Chunking Module** - Segments markdown into chunks for embedding
5. **Embedding Module** - Generates and stores vector embeddings
6. **Citation Trigger Module** - Evaluates when to generate citations
7. **Citation Generation Module** - Performs similarity search and formats citations
8. **Performance Module** - Monitors metrics and performance

### Pipeline Orchestrator

The `InlineCitationsPipelineOrchestrator` coordinates module execution, handles data flow between stages, manages error handling, and provides comprehensive logging and monitoring.

## Quick Start

### Basic Usage

```typescript
import { InlineCitationsPipeline } from '@/lib/services/inline-citations-pipeline';

// Create pipeline instance
const pipeline = new InlineCitationsPipeline();

// Process a document
const result = await pipeline.processDocument({
  documentId: 'doc123',
  url: 'https://example.com/document.pdf',
  contentType: 'application/pdf'
});

// Generate citations
const citations = await pipeline.generateCitations({
  messageId: 'msg123',
  chatId: 'chat123',
  content: 'User message content...'
});
```

### Custom Configuration

```typescript
const pipeline = new InlineCitationsPipeline({
  pdf: {
    scaleFactor: 2.0,
    maxPages: 100,
    saveDebugImages: true
  },
  embedding: {
    provider: 'openai',
    model: 'text-embedding-3-small',
    outputDimensionality: 1024
  },
  performance: {
    enableMetrics: true,
    logLevel: 'debug'
  }
});
```

### Individual Stage Execution

```typescript
// Execute only PDF processing
const pdfResult = await pipeline.processPdf({
  documentId: 'doc123',
  url: 'https://example.com/document.pdf',
  contentType: 'application/pdf'
});

// Execute only citation trigger evaluation
const triggerResult = await pipeline.evaluateCitationTrigger({
  messageId: 'msg123',
  chatId: 'chat123',
  messages: [],
  chatExists: true,
  isReadonly: false
});
```

## Module Details

### PDF Processing Module

Handles PDF to images conversion using Enhanced Doc Reader's image extraction mechanism.

**Features:**
- Uses Enhanced Doc Reader API Gateway and Fargate services for image extraction
- Identical image extraction as Enhanced Doc Reader ("Cites to your documents")
- Consistent quality and rendering across RAG system and inline citations
- Mode-agnostic processing (works regardless of Enhanced Doc Reader UI toggle state)
- Debug image saving for troubleshooting
- No legacy fallbacks - Enhanced Doc Reader method only

**Configuration:**
```typescript
pdf: {
  scaleFactor: 1.5,        // Same as Enhanced Doc Reader
  jpegQuality: 0.9,        // Same as Enhanced Doc Reader
  targetDpi: 150,          // Same as Enhanced Doc Reader
  maxPages: 50,            // Processing limit
  saveDebugImages: false,  // Debug option
  debugImageDir: 'debug-images'
}
```

### OCR Processing Module

Converts document images to structured markdown using Gemini OCR.

**Features:**
- Gemini 2.0 Flash integration
- Batch processing with retry logic
- Quality metrics and analysis
- Debug markdown saving
- Configurable system prompts

**Configuration:**
```typescript
ocr: {
  model: 'gemini-2.0-flash-exp',
  maxRetries: 3,
  batchSize: 10,
  temperature: 0.1,
  saveDebugMarkdown: false
}
```

### Chunking Module

Segments markdown content into optimized chunks for embedding.

**Features:**
- Multiple chunking strategies (semantic, section, paragraph)
- Token-aware chunking with overlap
- Structure preservation
- Quality metrics and analysis

**Configuration:**
```typescript
chunking: {
  maxTokens: 512,
  overlapTokens: 50,
  strategy: 'semantic',
  preserveStructure: true
}
```

### Embedding Module

Generates and stores vector embeddings with support for multiple providers.

**Features:**
- OpenAI and Gemini embedding providers
- Batch processing with rate limiting
- Vector normalization verification
- Quality metrics and consistency checks

**Configuration:**
```typescript
embedding: {
  provider: 'openai',
  model: 'text-embedding-3-small',
  outputDimensionality: 1024,
  batchSize: 10,
  verifyNormalization: true
}
```

## Advanced Usage

### Event Monitoring

```typescript
pipeline.addEventListener((event) => {
  console.log(`Pipeline event: ${event.type}`, event);
});
```

### Performance Monitoring

```typescript
const result = await pipeline.processDocument(input, {
  enablePerformanceMonitoring: true
});

console.log('Performance metrics:', result.performance);
```

### Health Checks

```typescript
const health = await pipeline.getHealthStatus();
console.log('Module health:', health);
```

### Custom Execution Flow

```typescript
// Skip certain stages
const result = await pipeline.processDocument(input, {
  skipStages: ['markdown-storage']
});

// Execute only specific stages
const result = await pipeline.processDocument(input, {
  onlyStages: ['pdf-processing', 'ocr-processing']
});
```

## Configuration Reference

### Complete Configuration Object

```typescript
interface PipelineConfig {
  pdf: PdfConfig;
  ocr: OcrConfig;
  chunking: ChunkingConfig;
  embedding: EmbeddingConfig;
  trigger: TriggerConfig;
  generation: GenerationConfig;
  performance: PerformanceConfig;
  database: DatabaseConfig;
}
```

### Environment-Specific Configurations

```typescript
// Development
const devPipeline = createDevPipeline();

// Production
const prodPipeline = createProductionPipeline();
```

## Error Handling

The pipeline provides comprehensive error handling with specific error types:

- `PipelineError` - General pipeline errors
- `ValidationError` - Input validation failures
- `ProcessingError` - Stage processing failures
- `ConfigurationError` - Configuration issues

```typescript
try {
  const result = await pipeline.processDocument(input);
} catch (error) {
  if (error instanceof PipelineError) {
    console.log(`Pipeline error in stage ${error.stage}: ${error.message}`);
  }
}
```

## Testing

### Unit Testing Individual Modules

```typescript
import { PdfProcessingModule } from '@/lib/services/inline-citations-pipeline';

const module = new PdfProcessingModule(config);
const result = await module.execute(input, context);
```

### Integration Testing

```typescript
const pipeline = new InlineCitationsPipeline();
const result = await pipeline.processDocument(testInput);
expect(result.success).toBe(true);
```

## Migration Guide

### From Legacy Implementation

The modular pipeline maintains compatibility with existing services while providing enhanced functionality:

1. **Replace direct service calls** with pipeline methods
2. **Update configuration** to use the new config structure
3. **Add error handling** for the new error types
4. **Enable monitoring** for better observability

### Gradual Migration

You can migrate gradually by using individual modules:

```typescript
// Use new PDF processing module
const pdfModule = new PdfProcessingModule(config);
const result = await pdfModule.execute(input, context);

// Continue using legacy services for other stages
const ocrResult = await GeminiOCRService.processDocumentPages(result.data.pages);
```

## Performance Optimization

### Configuration Tuning

- Adjust `pdf.scaleFactor` and `pdf.targetDpi` for quality vs. speed
- Configure `embedding.batchSize` for optimal throughput
- Set `chunking.maxTokens` based on your embedding model limits

### Monitoring and Alerts

```typescript
pipeline.addEventListener((event) => {
  if (event.type === 'stage_completed' && event.data?.duration > 30000) {
    console.warn(`Slow stage detected: ${event.stage}`);
  }
});
```

## Contributing

When adding new modules:

1. Implement the `PipelineModule` interface
2. Add comprehensive input validation
3. Include proper error handling and cleanup
4. Add configuration options to the config schema
5. Include health checks and monitoring
6. Write unit tests and documentation

## Troubleshooting

### Common Issues

1. **PDF Processing Failures**: Check debug images and PDF.js configuration
2. **OCR Quality Issues**: Adjust image quality settings and OCR prompts
3. **Embedding Failures**: Verify API keys and rate limits
4. **Performance Issues**: Enable monitoring and check stage durations

### Debug Mode

```typescript
const pipeline = createDevPipeline(); // Enables debug features
```

### Logs and Metrics

All modules provide comprehensive logging. Set log level in configuration:

```typescript
performance: {
  logLevel: 'debug' // 'debug' | 'info' | 'warn' | 'error'
}
```
