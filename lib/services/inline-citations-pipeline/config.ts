/**
 * Centralized configuration for the inline citations pipeline
 * All modules use this configuration to ensure consistency
 */

// Utility type for deep partial configuration
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

export interface PipelineConfig {
  // PDF Processing Configuration - Enhanced Doc Reader Integration Only
  pdf: {
    // Core Enhanced Doc Reader settings (matches Enhanced Doc Reader API Gateway/Fargate exactly)
    scaleFactor: number;        // 1.5 - same as Enhanced Doc Reader
    jpegQuality: number;        // 0.9 - same as Enhanced Doc Reader
    targetDpi: number;          // 150 - same as Enhanced Doc Reader
    maxPages: number;           // Processing limit

    // Debug and monitoring options
    saveDebugImages: boolean;
    debugImageDir: string;
  };

  // OCR Processing Configuration
  ocr: {
    model: string;
    maxRetries: number;
    retryDelay: number;
    batchSize: number;
    systemPrompt: string;
    temperature: number;
    maxOutputTokens: number;
    saveDebugMarkdown: boolean;
    debugMarkdownDir: string;
  };

  // Chunking Configuration
  chunking: {
    maxTokens: number;
    overlapTokens: number;
    minChunkSize: number;
    strategy: 'semantic' | 'section' | 'paragraph';
    preserveStructure: boolean;
    enableOptimizations: boolean;
  };

  // Embedding Configuration
  embedding: {
    provider: 'openai' | 'gemini';
    model: string;
    outputDimensionality: number;
    batchSize: number;
    maxRetries: number;
    retryDelay: number;
    verifyNormalization: boolean;
  };

  // Citation Trigger Configuration
  trigger: {
    minDocuments: number;
    minMessageLength: number;
    maxMessagesBack: number;
    requireRecentDocuments: boolean;
    recentDocumentHours: number;
    skipReadonlyChats: boolean;
    enablePerformanceChecks: boolean;
  };

  // Citation Generation Configuration
  generation: {
    maxSimilarityResults: number;
    similarityThreshold: number;
    maxCitationsPerMessage: number;
    enableContextualRanking: boolean;
    preserveMarkdownStructure: boolean;
    citationFormat: 'superscript' | 'inline' | 'footnote';
    enableReferencesSection: boolean;
  };

  // Performance Monitoring Configuration
  performance: {
    enableMetrics: boolean;
    enableTiming: boolean;
    enableMemoryTracking: boolean;
    logLevel: 'debug' | 'info' | 'warn' | 'error';
    metricsRetentionDays: number;
  };

  // Database Configuration
  database: {
    enableTransactions: boolean;
    batchInsertSize: number;
    connectionPoolSize: number;
    queryTimeout: number;
  };
}

/**
 * Default configuration for the inline citations pipeline
 * Uses Enhanced Doc Reader API Gateway/Fargate settings for consistent image extraction
 */
export const DEFAULT_PIPELINE_CONFIG: PipelineConfig = {
  pdf: {
    // Enhanced Doc Reader API Gateway/Fargate compatible settings - DO NOT MODIFY
    scaleFactor: 1.5,           // Same as Enhanced Doc Reader
    jpegQuality: 0.9,           // Same as Enhanced Doc Reader
    targetDpi: 150,             // Same as Enhanced Doc Reader
    maxPages: 50,               // Processing limit

    // Debug options
    saveDebugImages: false,
    debugImageDir: 'debug-images',
  },

  ocr: {
    model: 'gemini-2.0-flash-exp',
    maxRetries: 3,
    retryDelay: 1000,
    batchSize: 10,
    systemPrompt: `You are an expert document processor. Convert the provided document images to clean, well-structured markdown.

CRITICAL REQUIREMENTS:
1. Extract ALL text content accurately, including headers, paragraphs, lists, and tables
2. Preserve document structure using proper markdown formatting
3. For tables: Use proper markdown table syntax with aligned columns
4. For lists: Use appropriate bullet points (-) or numbered lists (1.)
5. For headers: Use appropriate # levels based on visual hierarchy
6. Maintain paragraph breaks and spacing
7. Include any important visual elements as descriptive text in [brackets]
8. Do NOT add explanatory text or comments - only convert the document content
9. Ensure the output is valid, clean markdown that preserves the original document's information and structure

Focus on accuracy and completeness. The markdown will be used for semantic search and citation generation.`,
    temperature: 0.1,
    maxOutputTokens: 8192,
    saveDebugMarkdown: false,
    debugMarkdownDir: 'debug-markdown',
  },

  chunking: {
    maxTokens: 512,
    overlapTokens: 50,
    minChunkSize: 100,
    strategy: 'semantic',
    preserveStructure: true,
    enableOptimizations: true,
  },

  embedding: {
    provider: 'openai',
    model: 'text-embedding-3-small',
    outputDimensionality: 1024,
    batchSize: 10,
    maxRetries: 3,
    retryDelay: 1000,
    verifyNormalization: true,
  },

  trigger: {
    minDocuments: 1,
    minMessageLength: 50,
    maxMessagesBack: 10,
    requireRecentDocuments: true,
    recentDocumentHours: 24,
    skipReadonlyChats: true,
    enablePerformanceChecks: true,
  },

  generation: {
    maxSimilarityResults: 20,
    similarityThreshold: 0.7,
    maxCitationsPerMessage: 10,
    enableContextualRanking: true,
    preserveMarkdownStructure: true,
    citationFormat: 'superscript',
    enableReferencesSection: true,
  },

  performance: {
    enableMetrics: true,
    enableTiming: true,
    enableMemoryTracking: false,
    logLevel: 'info',
    metricsRetentionDays: 30,
  },

  database: {
    enableTransactions: true,
    batchInsertSize: 100,
    connectionPoolSize: 10,
    queryTimeout: 30000,
  },
};

/**
 * Environment-specific configuration overrides
 */
export function getEnvironmentConfig(): DeepPartial<PipelineConfig> {
  const env = process.env.NODE_ENV || 'development';

  switch (env) {
    case 'development':
      return {
        pdf: {
          saveDebugImages: true,
          maxPages: 10, // Limit for development
        },
        ocr: {
          saveDebugMarkdown: true,
        },
        performance: {
          logLevel: 'debug',
        },
      };

    case 'production':
      return {
        pdf: {
          saveDebugImages: false,
        },
        ocr: {
          saveDebugMarkdown: false,
        },
        performance: {
          logLevel: 'info',
          enableMemoryTracking: true,
        },
      };

    default:
      return {};
  }
}

/**
 * Deep merge utility function
 */
function deepMerge<T extends Record<string, any>>(target: T, ...sources: DeepPartial<T>[]): T {
  if (!sources.length) return target;
  const source = sources.shift();

  if (isObject(target) && isObject(source)) {
    for (const key in source) {
      if (isObject(source[key])) {
        if (!target[key]) Object.assign(target, { [key]: {} });
        deepMerge(target[key], source[key]);
      } else {
        Object.assign(target, { [key]: source[key] });
      }
    }
  }

  return deepMerge(target, ...sources);
}

function isObject(item: any): item is Record<string, any> {
  return item && typeof item === 'object' && !Array.isArray(item);
}

/**
 * Merges default config with environment-specific overrides
 */
export function createPipelineConfig(overrides?: DeepPartial<PipelineConfig>): PipelineConfig {
  const envConfig = getEnvironmentConfig();

  // Create a deep copy of the default config
  const config = JSON.parse(JSON.stringify(DEFAULT_PIPELINE_CONFIG)) as PipelineConfig;

  // Deep merge environment config and overrides
  return deepMerge(config, envConfig, overrides || {});
}
