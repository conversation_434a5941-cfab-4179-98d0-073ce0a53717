/**
 * Pipeline Orchestrator
 * Main coordinator for the inline citations pipeline modules
 */

import { Logger } from "../../utils/Logger";
import { 
  PipelineOrchestrator, 
  PipelineModule, 
  PipelineResult, 
  PipelineContext, 
  PipelineError,
  PipelineEvent,
  PipelineMetrics
} from "./types";
import { PipelineConfig, createPipelineConfig, DeepPartial } from "./config";

// Import all modules
import { PdfProcessingModule } from "./modules/pdf-processing-module";
import { OcrProcessingModule } from "./modules/ocr-processing-module";
import { MarkdownStorageModule } from "./modules/markdown-storage-module";
import { ChunkingModule } from "./modules/chunking-module";
import { EmbeddingModule } from "./modules/embedding-module";
import { CitationTriggerModule } from "./modules/citation-trigger-module";
import { CitationGenerationModule } from "./modules/citation-generation-module";
import { PerformanceModule } from "./modules/performance-module";

export interface PipelineExecutionOptions {
  skipStages?: string[];
  onlyStages?: string[];
  enablePerformanceMonitoring?: boolean;
  enableEventLogging?: boolean;
  customConfig?: DeepPartial<PipelineConfig>;
}

export class InlineCitationsPipelineOrchestrator implements PipelineOrchestrator {
  private modules = new Map<string, PipelineModule>();
  private contexts = new Map<string, PipelineContext>();
  private eventListeners: Array<(event: PipelineEvent) => void> = [];
  private config: PipelineConfig;

  constructor(configOverrides?: DeepPartial<PipelineConfig>) {
    this.config = createPipelineConfig(configOverrides);
    this.initializeModules();
  }

  private initializeModules(): void {
    // Register all pipeline modules
    this.registerModule(new PdfProcessingModule(this.config));
    this.registerModule(new OcrProcessingModule(this.config));
    this.registerModule(new MarkdownStorageModule(this.config));
    this.registerModule(new ChunkingModule(this.config));
    this.registerModule(new EmbeddingModule(this.config));
    this.registerModule(new CitationTriggerModule(this.config));
    this.registerModule(new CitationGenerationModule(this.config));
    this.registerModule(new PerformanceModule(this.config));

    Logger.info("Pipeline orchestrator initialized", {
      moduleCount: this.modules.size,
      modules: Array.from(this.modules.keys()),
    });
  }

  registerModule(pipelineModule: PipelineModule): void {
    this.modules.set(pipelineModule.name, pipelineModule);
    Logger.debug("Module registered", {
      name: pipelineModule.name,
      version: pipelineModule.version,
      dependencies: pipelineModule.dependencies,
    });
  }

  getModules(): Record<string, PipelineModule> {
    return Object.fromEntries(this.modules);
  }

  async execute(
    input: any, 
    contextOverrides?: Partial<PipelineContext>,
    options?: PipelineExecutionOptions
  ): Promise<PipelineResult> {
    const requestId = this.generateRequestId();
    const startTime = Date.now();

    // Create execution context
    const context: PipelineContext = {
      documentId: input.documentId || requestId,
      messageId: input.messageId,
      chatId: input.chatId,
      userId: input.userId,
      sessionId: input.sessionId,
      requestId,
      startTime,
      stages: {},
      globalMetadata: {},
      ...contextOverrides,
    };

    this.contexts.set(requestId, context);

    try {
      Logger.info("Starting pipeline execution", {
        requestId,
        documentId: context.documentId,
        messageId: context.messageId,
        chatId: context.chatId,
        options,
      });

      // Emit start event
      this.emitEvent({
        type: 'pipeline_started',
        timestamp: startTime,
        contextId: requestId,
        data: { input, options },
      });

      // Initialize performance monitoring if enabled
      if (options?.enablePerformanceMonitoring !== false && this.config.performance.enableMetrics) {
        await this.executeStage('performance', { contextId: requestId, operation: 'start' }, context);
      }

      // Determine execution flow based on input type
      const executionFlow = this.determineExecutionFlow(input, options);
      
      Logger.info("Determined execution flow", {
        requestId,
        flow: executionFlow,
        stageCount: executionFlow.length,
      });

      let lastResult: any = input;

      // Execute stages in sequence
      for (const stageName of executionFlow) {
        try {
          // Prepare stage input based on previous results
          const stageInput = this.prepareStageInput(stageName, lastResult, context);
          
          // Execute stage
          const stageResult = await this.executeStage(stageName, stageInput, context);
          
          if (!stageResult.success) {
            throw new PipelineError(
              stageResult.error || `Stage ${stageName} failed`,
              stageName,
              'STAGE_EXECUTION_FAILED',
              { stageResult, context: requestId }
            );
          }

          // Update performance monitoring
          if (options?.enablePerformanceMonitoring !== false && this.config.performance.enableMetrics) {
            await this.executeStage('performance', { 
              contextId: requestId, 
              operation: 'stage', 
              stageName 
            }, context);
          }

          lastResult = stageResult.data;

        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : String(error);
          
          Logger.error(`Stage ${stageName} failed`, {
            requestId,
            stageName,
            error: errorMessage,
          });

          // Emit stage failed event
          this.emitEvent({
            type: 'stage_failed',
            timestamp: Date.now(),
            contextId: requestId,
            stage: stageName,
            error: errorMessage,
          });

          throw error;
        }
      }

      // Finalize performance monitoring
      if (options?.enablePerformanceMonitoring !== false && this.config.performance.enableMetrics) {
        await this.executeStage('performance', { contextId: requestId, operation: 'end' }, context);
      }

      const endTime = Date.now();
      const totalDuration = endTime - startTime;

      // Emit completion event
      this.emitEvent({
        type: 'pipeline_completed',
        timestamp: endTime,
        contextId: requestId,
        data: { duration: totalDuration, result: lastResult },
      });

      Logger.info("Pipeline execution completed successfully", {
        requestId,
        totalDuration,
        stagesExecuted: executionFlow.length,
      });

      return {
        success: true,
        data: lastResult,
        performance: {
          startTime,
          endTime,
          duration: totalDuration,
        },
        metadata: {
          requestId,
          stagesExecuted: executionFlow,
          context: this.sanitizeContextForOutput(context),
        },
      };

    } catch (error) {
      const endTime = Date.now();
      const errorMessage = error instanceof Error ? error.message : String(error);

      // Emit failure event
      this.emitEvent({
        type: 'pipeline_failed',
        timestamp: endTime,
        contextId: requestId,
        error: errorMessage,
      });

      Logger.error("Pipeline execution failed", {
        requestId,
        error: errorMessage,
        duration: endTime - startTime,
      });

      return {
        success: false,
        error: errorMessage,
        performance: {
          startTime,
          endTime,
          duration: endTime - startTime,
        },
        metadata: {
          requestId,
          context: this.sanitizeContextForOutput(context),
        },
      };

    } finally {
      // Cleanup modules
      await this.cleanupModules(context);
      
      // Remove context after some time to prevent memory leaks
      setTimeout(() => {
        this.contexts.delete(requestId);
      }, 60000); // Keep for 1 minute for debugging
    }
  }

  async executeStage(
    stageName: string,
    input: any,
    context: PipelineContext
  ): Promise<PipelineResult> {
    const pipelineModule = this.modules.get(stageName);

    if (!pipelineModule) {
      throw new PipelineError(
        `Module '${stageName}' not found`,
        stageName,
        'MODULE_NOT_FOUND'
      );
    }

    Logger.debug(`Executing stage: ${stageName}`, {
      requestId: context.requestId,
      stageName,
      moduleName: pipelineModule.name,
      moduleVersion: pipelineModule.version,
    });

    // Emit stage started event
    this.emitEvent({
      type: 'stage_started',
      timestamp: Date.now(),
      contextId: context.requestId!,
      stage: stageName,
    });

    try {
      const result = await pipelineModule.execute(input, context);

      // Emit stage completed event
      this.emitEvent({
        type: 'stage_completed',
        timestamp: Date.now(),
        contextId: context.requestId!,
        stage: stageName,
        data: { success: result.success, duration: result.performance?.duration },
      });

      return result;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);

      // Emit stage failed event
      this.emitEvent({
        type: 'stage_failed',
        timestamp: Date.now(),
        contextId: context.requestId!,
        stage: stageName,
        error: errorMessage,
      });

      throw error;
    }
  }

  async getStatus(contextId: string): Promise<PipelineContext | null> {
    return this.contexts.get(contextId) || null;
  }

  private determineExecutionFlow(input: any, options?: PipelineExecutionOptions): string[] {
    // Default document processing flow
    let flow = ['pdf-processing', 'ocr-processing', 'markdown-storage', 'chunking', 'embedding'];
    
    // Citation generation flow
    if (input.messageId && input.chatId && input.content) {
      flow = ['citation-trigger', 'citation-generation'];
    }

    // Apply stage filtering options
    if (options?.onlyStages) {
      flow = flow.filter(stage => options.onlyStages!.includes(stage));
    }

    if (options?.skipStages) {
      flow = flow.filter(stage => !options.skipStages!.includes(stage));
    }

    return flow;
  }

  private prepareStageInput(stageName: string, previousResult: any, context: PipelineContext): any {
    // Transform data between stages as needed
    switch (stageName) {
      case 'pdf-processing':
        return {
          documentId: context.documentId,
          url: previousResult.url || previousResult,
          contentType: previousResult.contentType || 'application/pdf',
        };
      
      case 'ocr-processing':
        return {
          documentId: context.documentId,
          pages: previousResult.pages || previousResult,
        };
      
      case 'markdown-storage':
        return {
          documentId: context.documentId,
          markdown: previousResult.markdown || previousResult,
          metadata: previousResult.metadata,
        };
      
      case 'chunking':
        return {
          documentId: context.documentId,
          markdown: previousResult.markdown || previousResult,
          sourceType: 'pdf' as const,
          metadata: previousResult.metadata,
        };
      
      case 'embedding':
        return {
          documentId: context.documentId,
          chunks: previousResult.chunks || previousResult,
        };
      
      case 'citation-trigger':
        return {
          messageId: context.messageId!,
          chatId: context.chatId!,
          messages: previousResult.messages || [],
          chatExists: previousResult.chatExists || false,
          isReadonly: previousResult.isReadonly || false,
          content: previousResult.content,
        };
      
      case 'citation-generation':
        return {
          messageId: context.messageId!,
          chatId: context.chatId!,
          content: previousResult.content || previousResult,
        };
      
      default:
        return previousResult;
    }
  }

  private async cleanupModules(context: PipelineContext): Promise<void> {
    const cleanupPromises = Array.from(this.modules.values()).map(async (pipelineModule) => {
      try {
        if (pipelineModule.cleanup) {
          await pipelineModule.cleanup(context);
        }
      } catch (error) {
        Logger.warn(`Module cleanup failed: ${pipelineModule.name}`, {
          error: error instanceof Error ? error.message : String(error),
          requestId: context.requestId,
        });
      }
    });

    await Promise.allSettled(cleanupPromises);
  }

  private sanitizeContextForOutput(context: PipelineContext): Partial<PipelineContext> {
    return {
      documentId: context.documentId,
      messageId: context.messageId,
      chatId: context.chatId,
      requestId: context.requestId,
      startTime: context.startTime,
      stages: Object.fromEntries(
        Object.entries(context.stages).map(([name, stage]) => [
          name,
          {
            status: stage.status,
            startTime: stage.startTime,
            endTime: stage.endTime,
            error: stage.error,
          },
        ])
      ),
    };
  }

  private emitEvent(event: PipelineEvent): void {
    this.eventListeners.forEach(listener => {
      try {
        listener(event);
      } catch (error) {
        Logger.warn("Event listener failed", {
          error: error instanceof Error ? error.message : String(error),
          eventType: event.type,
        });
      }
    });
  }

  private generateRequestId(): string {
    return `pipeline_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Add event listener for pipeline events
   */
  addEventListener(listener: (event: PipelineEvent) => void): void {
    this.eventListeners.push(listener);
  }

  /**
   * Remove event listener
   */
  removeEventListener(listener: (event: PipelineEvent) => void): void {
    const index = this.eventListeners.indexOf(listener);
    if (index > -1) {
      this.eventListeners.splice(index, 1);
    }
  }

  /**
   * Get pipeline configuration
   */
  getConfig(): PipelineConfig {
    return { ...this.config };
  }

  /**
   * Update pipeline configuration
   */
  updateConfig(updates: DeepPartial<PipelineConfig>): void {
    this.config = createPipelineConfig({ ...this.config, ...updates });
    Logger.info("Pipeline configuration updated", { updates });
  }
}
