/**
 * Performance Module
 * Handles monitoring, metrics collection, and performance optimization
 */

import { Logger } from "../../../utils/Logger";
import { PipelineModule, PipelineResult, PipelineContext, PipelineMetrics, ProcessingError } from "../types";
import { PipelineConfig } from "../config";

export interface PerformanceInput {
  contextId: string;
  operation: 'start' | 'stage' | 'end';
  stageName?: string;
  metadata?: Record<string, any>;
}

export interface PerformanceOutput {
  metrics: PipelineMetrics;
  recommendations: string[];
  alerts: Array<{
    type: 'warning' | 'error' | 'info';
    message: string;
    threshold?: number;
    actual?: number;
  }>;
  metadata: {
    collectionTime: number;
    metricsCount: number;
    healthScore: number;
  };
}

export class PerformanceModule implements PipelineModule<PerformanceInput, PerformanceOutput> {
  readonly name = 'performance';
  readonly version = '1.0.0';
  readonly dependencies = [];

  private metricsStore = new Map<string, PipelineMetrics>();
  private performanceThresholds = {
    maxStageDuration: 30000, // 30 seconds
    maxTotalDuration: 120000, // 2 minutes
    maxMemoryUsage: 500 * 1024 * 1024, // 500MB
    maxRetryCount: 3,
  };

  constructor(private config: PipelineConfig) {}

  async execute(
    input: PerformanceInput,
    context: PipelineContext
  ): Promise<PipelineResult<PerformanceOutput>> {
    const startTime = Date.now();
    
    try {
      Logger.debug("Starting performance module", {
        contextId: input.contextId,
        operation: input.operation,
        stageName: input.stageName,
      });

      // Validate input
      if (!this.validateInput(input)) {
        throw new ProcessingError(
          "Invalid input for performance monitoring",
          this.name,
          { input }
        );
      }

      // Collect metrics based on operation type
      let metrics: PipelineMetrics;
      
      switch (input.operation) {
        case 'start':
          metrics = this.initializeMetrics(input.contextId, context);
          break;
        case 'stage':
          metrics = this.updateStageMetrics(input.contextId, input.stageName!, context);
          break;
        case 'end':
          metrics = this.finalizeMetrics(input.contextId, context);
          break;
        default:
          throw new ProcessingError(
            `Unknown performance operation: ${input.operation}`,
            this.name,
            { operation: input.operation }
          );
      }

      // Analyze performance and generate recommendations
      const analysis = this.analyzePerformance(metrics);

      const endTime = Date.now();
      const collectionTime = endTime - startTime;

      const output: PerformanceOutput = {
        metrics,
        recommendations: analysis.recommendations,
        alerts: analysis.alerts,
        metadata: {
          collectionTime,
          metricsCount: Object.keys(metrics.stageMetrics).length,
          healthScore: analysis.healthScore,
        },
      };

      Logger.debug("Performance module completed", {
        contextId: input.contextId,
        operation: input.operation,
        healthScore: analysis.healthScore,
        alertCount: analysis.alerts.length,
      });

      return {
        success: true,
        data: output,
        performance: {
          startTime,
          endTime,
          duration: collectionTime,
        },
      };

    } catch (error) {
      const endTime = Date.now();
      const errorMessage = error instanceof Error ? error.message : String(error);

      Logger.error("Performance module failed", {
        contextId: input.contextId,
        operation: input.operation,
        error: errorMessage,
        collectionTime: endTime - startTime,
      });

      return {
        success: false,
        error: errorMessage,
        performance: {
          startTime,
          endTime,
          duration: endTime - startTime,
        },
      };
    }
  }

  validateInput(input: PerformanceInput): boolean {
    if (!input.contextId || typeof input.contextId !== 'string') {
      Logger.error("Invalid contextId in performance input", { input });
      return false;
    }

    if (!['start', 'stage', 'end'].includes(input.operation)) {
      Logger.error("Invalid operation in performance input", { input });
      return false;
    }

    if (input.operation === 'stage' && (!input.stageName || typeof input.stageName !== 'string')) {
      Logger.error("Invalid stageName for stage operation in performance input", { input });
      return false;
    }

    return true;
  }

  async cleanup(context: PipelineContext): Promise<void> {
    Logger.debug("Cleaning up performance module", {
      contextId: context.requestId,
    });

    // Clean up old metrics based on retention policy
    if (this.config.performance.metricsRetentionDays > 0) {
      const cutoffTime = Date.now() - (this.config.performance.metricsRetentionDays * 24 * 60 * 60 * 1000);
      
      for (const [contextId, metrics] of this.metricsStore.entries()) {
        if (metrics.totalDuration > 0 && Date.now() - metrics.totalDuration > cutoffTime) {
          this.metricsStore.delete(contextId);
        }
      }
    }
  }

  async getHealthStatus(): Promise<{ healthy: boolean; details?: Record<string, any> }> {
    try {
      const healthDetails = {
        configValid: this.isConfigValid(),
        metricsStoreSize: this.metricsStore.size,
        memoryUsage: this.getMemoryUsage(),
        lastCheck: new Date().toISOString(),
      };

      const healthy = this.isConfigValid() && this.metricsStore.size < 10000; // Prevent memory leaks

      return {
        healthy,
        details: healthDetails,
      };
    } catch (error) {
      return {
        healthy: false,
        details: {
          error: error instanceof Error ? error.message : String(error),
          lastCheck: new Date().toISOString(),
        },
      };
    }
  }

  private isConfigValid(): boolean {
    const { performance } = this.config;
    
    return (
      typeof performance.enableMetrics === 'boolean' &&
      typeof performance.enableTiming === 'boolean' &&
      typeof performance.enableMemoryTracking === 'boolean' &&
      ['debug', 'info', 'warn', 'error'].includes(performance.logLevel) &&
      typeof performance.metricsRetentionDays === 'number' && performance.metricsRetentionDays >= 0
    );
  }

  private initializeMetrics(contextId: string, context: PipelineContext): PipelineMetrics {
    const metrics: PipelineMetrics = {
      contextId,
      totalDuration: 0,
      stageMetrics: {},
      resourceUsage: {
        peakMemory: 0,
        totalCpuTime: 0,
        networkRequests: 0,
        databaseQueries: 0,
      },
    };

    this.metricsStore.set(contextId, metrics);
    return metrics;
  }

  private updateStageMetrics(contextId: string, stageName: string, context: PipelineContext): PipelineMetrics {
    const metrics = this.metricsStore.get(contextId) || this.initializeMetrics(contextId, context);
    const stageInfo = context.stages[stageName];

    if (stageInfo && stageInfo.startTime && stageInfo.endTime) {
      const duration = stageInfo.endTime - stageInfo.startTime;
      const memoryUsage = this.config.performance.enableMemoryTracking ? this.getMemoryUsage().heapUsed : 0;

      metrics.stageMetrics[stageName] = {
        duration,
        memoryUsage,
        success: stageInfo.status === 'completed',
        retryCount: stageInfo.metadata?.retryCount || 0,
      };

      // Update peak memory
      if (memoryUsage > metrics.resourceUsage.peakMemory) {
        metrics.resourceUsage.peakMemory = memoryUsage;
      }
    }

    this.metricsStore.set(contextId, metrics);
    return metrics;
  }

  private finalizeMetrics(contextId: string, context: PipelineContext): PipelineMetrics {
    const metrics = this.metricsStore.get(contextId) || this.initializeMetrics(contextId, context);
    
    // Calculate total duration
    const startTime = Math.min(...Object.values(context.stages)
      .filter(stage => stage.startTime)
      .map(stage => stage.startTime!));
    const endTime = Math.max(...Object.values(context.stages)
      .filter(stage => stage.endTime)
      .map(stage => stage.endTime!));
    
    metrics.totalDuration = endTime - startTime;

    // Update resource usage
    metrics.resourceUsage.totalCpuTime = metrics.totalDuration;
    metrics.resourceUsage.networkRequests = this.countNetworkRequests(context);
    metrics.resourceUsage.databaseQueries = this.countDatabaseQueries(context);

    this.metricsStore.set(contextId, metrics);
    return metrics;
  }

  private analyzePerformance(metrics: PipelineMetrics) {
    const recommendations: string[] = [];
    const alerts: PerformanceOutput['alerts'] = [];
    let healthScore = 100;

    // Check total duration
    if (metrics.totalDuration > this.performanceThresholds.maxTotalDuration) {
      alerts.push({
        type: 'warning',
        message: 'Pipeline execution time exceeded threshold',
        threshold: this.performanceThresholds.maxTotalDuration,
        actual: metrics.totalDuration,
      });
      recommendations.push('Consider optimizing slow stages or increasing parallelization');
      healthScore -= 20;
    }

    // Check stage durations
    for (const [stageName, stageMetrics] of Object.entries(metrics.stageMetrics)) {
      if (stageMetrics.duration > this.performanceThresholds.maxStageDuration) {
        alerts.push({
          type: 'warning',
          message: `Stage '${stageName}' exceeded duration threshold`,
          threshold: this.performanceThresholds.maxStageDuration,
          actual: stageMetrics.duration,
        });
        recommendations.push(`Optimize '${stageName}' stage performance`);
        healthScore -= 10;
      }

      if (!stageMetrics.success) {
        alerts.push({
          type: 'error',
          message: `Stage '${stageName}' failed`,
        });
        healthScore -= 15;
      }

      if (stageMetrics.retryCount && stageMetrics.retryCount > this.performanceThresholds.maxRetryCount) {
        alerts.push({
          type: 'warning',
          message: `Stage '${stageName}' required excessive retries`,
          threshold: this.performanceThresholds.maxRetryCount,
          actual: stageMetrics.retryCount,
        });
        recommendations.push(`Investigate reliability issues in '${stageName}' stage`);
        healthScore -= 5;
      }
    }

    // Check memory usage
    if (metrics.resourceUsage.peakMemory > this.performanceThresholds.maxMemoryUsage) {
      alerts.push({
        type: 'warning',
        message: 'Peak memory usage exceeded threshold',
        threshold: this.performanceThresholds.maxMemoryUsage,
        actual: metrics.resourceUsage.peakMemory,
      });
      recommendations.push('Consider implementing memory optimization strategies');
      healthScore -= 15;
    }

    return {
      recommendations,
      alerts,
      healthScore: Math.max(healthScore, 0),
    };
  }

  private getMemoryUsage() {
    if (typeof process !== 'undefined' && process.memoryUsage) {
      return process.memoryUsage();
    }
    return { heapUsed: 0, heapTotal: 0, external: 0 };
  }

  private countNetworkRequests(context: PipelineContext): number {
    // Count network requests from stage metadata
    return Object.values(context.stages).reduce((count, stage) => {
      return count + (stage.metadata?.networkRequests || 0);
    }, 0);
  }

  private countDatabaseQueries(context: PipelineContext): number {
    // Count database queries from stage metadata
    return Object.values(context.stages).reduce((count, stage) => {
      return count + (stage.metadata?.databaseQueries || 0);
    }, 0);
  }

  /**
   * Get current metrics for a context
   */
  getMetrics(contextId: string): PipelineMetrics | null {
    return this.metricsStore.get(contextId) || null;
  }

  /**
   * Get all stored metrics
   */
  getAllMetrics(): Map<string, PipelineMetrics> {
    return new Map(this.metricsStore);
  }

  /**
   * Clear metrics for a specific context
   */
  clearMetrics(contextId: string): void {
    this.metricsStore.delete(contextId);
  }
}
