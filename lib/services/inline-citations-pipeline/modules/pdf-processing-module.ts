/**
 * PDF Processing Module
 * Handles conversion of PDF documents to images for OCR processing
 * Uses Enhanced Doc Reader image extraction for reliable document conversion
 */

import { Logger } from "../../../utils/Logger";
import { PipelineModule, PipelineResult, PipelineContext, DocumentPage, ProcessingError } from "../types";
import { PipelineConfig } from "../config";
import { EnhancedDocReaderImageIntegration } from "../../enhanced-doc-reader-integration";

export interface PdfProcessingInput {
  documentId: string;
  url: string;
  contentType: string;
}

export interface PdfProcessingOutput {
  pages: DocumentPage[];
  totalPages: number;
  processingTime: number;
  metadata: {
    scaleFactor: number;
    quality: number;
    renderingApproach: string;
    extractionMethod: string;
    compatibilityMode: string;
    debugImagesPath?: string;
  };
}

export class PdfProcessingModule implements PipelineModule<PdfProcessingInput, PdfProcessingOutput> {
  readonly name = 'pdf-processing';
  readonly version = '1.0.0';
  readonly dependencies = [];

  constructor(private config: PipelineConfig) {}

  async execute(
    input: PdfProcessingInput,
    context: PipelineContext
  ): Promise<PipelineResult<PdfProcessingOutput>> {
    const startTime = Date.now();
    
    try {
      Logger.info("Starting PDF processing module", {
        documentId: input.documentId,
        url: input.url,
        contentType: input.contentType,
        contextId: context.requestId,
      });

      // Validate input
      if (!this.validateInput(input)) {
        throw new ProcessingError(
          "Invalid input for PDF processing",
          this.name,
          { input }
        );
      }

      // Update context
      context.stages[this.name] = {
        status: 'processing',
        startTime,
      };

      // Process PDF to images using Enhanced Doc Reader
      Logger.info("Using Enhanced Doc Reader for inline citations", {
        documentId: input.documentId,
        extractionMethod: 'enhanced-doc-reader',
        targetPipeline: 'inline-citations',
        renderingApproach: 'enhanced-doc-reader-native',
        fallbackEnabled: false,
      });

      const pages = await EnhancedDocReaderImageIntegration.processPdfToImages(
        input.documentId,
        input.url,
        {
          preferredService: 'api-gateway',
          targetDpi: this.config.pdf.targetDpi,
          optimizeSize: true,
          includeDataUri: true,
        }
      );

      if (!pages || pages.length === 0) {
        Logger.warn("Enhanced Doc Reader processing returned no pages - may still be processing asynchronously", {
          documentId: input.documentId,
          renderingApproach: 'enhanced-doc-reader-native',
          extractionMethod: 'enhanced-doc-reader',
          possibleCause: 'async-processing-in-progress',
        });

        // Check if we should wait and retry or fail immediately
        // For now, we'll throw an error but with a more informative message
        throw new ProcessingError(
          `Enhanced Doc Reader processing incomplete: Document may still be processing asynchronously. Please try again in a few moments.`,
          this.name,
          {
            documentId: input.documentId,
            extractionMethod: 'enhanced-doc-reader',
            retryable: true,
            asyncProcessing: true,
          }
        );
      }

      const endTime = Date.now();
      const processingTime = endTime - startTime;

      const output: PdfProcessingOutput = {
        pages,
        totalPages: pages.length,
        processingTime,
        metadata: {
          scaleFactor: this.config.pdf.scaleFactor,
          quality: this.config.pdf.jpegQuality,
          renderingApproach: 'enhanced-doc-reader-native',
          extractionMethod: 'enhanced-doc-reader',
          compatibilityMode: 'enhanced-doc-reader-only',
          debugImagesPath: this.config.pdf.saveDebugImages ? this.config.pdf.debugImageDir : undefined,
        },
      };

      // Update context with success
      context.stages[this.name] = {
        status: 'completed',
        startTime,
        endTime,
        metadata: {
          pagesProcessed: pages.length,
          averagePageSize: pages.reduce((sum, p) => sum + p.imageBase64.length, 0) / pages.length,
        },
      };

      Logger.info("PDF processing module completed successfully", {
        documentId: input.documentId,
        pagesProcessed: pages.length,
        processingTime,
        extractionMethod: 'enhanced-doc-reader',
        renderingApproach: 'enhanced-doc-reader-native',
        compatibilityMode: 'enhanced-doc-reader-only',
        totalImageSize: Math.round(pages.reduce((sum, page) => sum + page.imageBase64.length, 0) / 1024) + 'KB',
        contextId: context.requestId,
      });

      return {
        success: true,
        data: output,
        performance: {
          startTime,
          endTime,
          duration: processingTime,
        },
      };

    } catch (error) {
      const endTime = Date.now();
      const errorMessage = error instanceof Error ? error.message : String(error);

      // Update context with failure
      context.stages[this.name] = {
        status: 'failed',
        startTime,
        endTime,
        error: errorMessage,
      };

      Logger.error("PDF processing module failed", {
        documentId: input.documentId,
        error: errorMessage,
        processingTime: endTime - startTime,
        contextId: context.requestId,
      });

      return {
        success: false,
        error: errorMessage,
        performance: {
          startTime,
          endTime,
          duration: endTime - startTime,
        },
      };
    }
  }

  validateInput(input: PdfProcessingInput): boolean {
    if (!input.documentId || typeof input.documentId !== 'string') {
      Logger.error("Invalid documentId in PDF processing input", { input });
      return false;
    }

    if (!input.url || typeof input.url !== 'string') {
      Logger.error("Invalid URL in PDF processing input", { input });
      return false;
    }

    if (!input.contentType || !input.contentType.includes('pdf')) {
      Logger.error("Invalid content type for PDF processing", { input });
      return false;
    }

    return true;
  }

  async cleanup(context: PipelineContext): Promise<void> {
    // Clean up any temporary files or resources
    Logger.debug("Cleaning up PDF processing module", {
      contextId: context.requestId,
    });

    // If debug images were saved, optionally clean them up based on config
    if (this.config.pdf.saveDebugImages && context.globalMetadata?.cleanupDebugFiles) {
      // Implementation for cleaning up debug files would go here
      Logger.debug("Debug image cleanup requested", {
        contextId: context.requestId,
        debugDir: this.config.pdf.debugImageDir,
      });
    }
  }

  async getHealthStatus(): Promise<{ healthy: boolean; details?: Record<string, any> }> {
    try {
      // Health check for Enhanced Doc Reader processing
      const healthDetails = {
        configValid: this.isConfigValid(),
        enhancedDocReaderAvailable: true,
        extractionMethod: 'enhanced-doc-reader',
        lastCheck: new Date().toISOString(),
      };

      const healthy = Object.values(healthDetails).every(v => v === true || typeof v === 'string');

      return {
        healthy,
        details: healthDetails,
      };
    } catch (error) {
      return {
        healthy: false,
        details: {
          error: error instanceof Error ? error.message : String(error),
          extractionMethod: 'enhanced-doc-reader-failed',
          lastCheck: new Date().toISOString(),
        },
      };
    }
  }

  private isConfigValid(): boolean {
    const { pdf } = this.config;

    return (
      typeof pdf.scaleFactor === 'number' && pdf.scaleFactor > 0 &&
      typeof pdf.jpegQuality === 'number' && pdf.jpegQuality > 0 && pdf.jpegQuality <= 1 &&
      typeof pdf.targetDpi === 'number' && pdf.targetDpi > 0 &&
      typeof pdf.maxPages === 'number' && pdf.maxPages > 0 &&
      typeof pdf.debugImageDir === 'string' && pdf.debugImageDir.length > 0
    );
  }

  /**
   * Get module configuration for debugging
   */
  getConfig(): PipelineConfig['pdf'] {
    return this.config.pdf;
  }

  /**
   * Update module configuration (for runtime adjustments)
   */
  updateConfig(updates: Partial<PipelineConfig['pdf']>): void {
    Object.assign(this.config.pdf, updates);
    Logger.info("PDF processing module configuration updated", {
      updates,
      newConfig: this.config.pdf,
    });
  }
}
