/**
 * OCR Processing Module
 * Handles conversion of document images to markdown using Gemini OCR
 */

import { Logger } from "../../../utils/Logger";
import { PipelineModule, PipelineResult, PipelineContext, DocumentPage, OcrResult, ProcessingError } from "../types";
import { PipelineConfig } from "../config";
import { GeminiOCRService } from "../../gemini-ocr";

export interface OcrProcessingInput {
  documentId: string;
  pages: DocumentPage[];
}

export interface OcrProcessingOutput {
  markdown: string;
  pagesProcessed: number;
  qualityMetrics: {
    averageConfidence?: number;
    blankPageCount: number;
    totalCharacters: number;
    structureScore: number;
  };
  metadata: {
    model: string;
    processingTime: number;
    tokenCount: number;
    debugMarkdownPath?: string;
  };
}

export class OcrProcessingModule implements PipelineModule<OcrProcessingInput, OcrProcessingOutput> {
  readonly name = 'ocr-processing';
  readonly version = '1.0.0';
  readonly dependencies = ['pdf-processing'];

  constructor(private config: PipelineConfig) {}

  async execute(
    input: OcrProcessingInput,
    context: PipelineContext
  ): Promise<PipelineResult<OcrProcessingOutput>> {
    const startTime = Date.now();
    
    try {
      Logger.info("Starting OCR processing module", {
        documentId: input.documentId,
        pageCount: input.pages.length,
        contextId: context.requestId,
      });

      // Validate input
      if (!this.validateInput(input)) {
        throw new ProcessingError(
          "Invalid input for OCR processing",
          this.name,
          { input: { documentId: input.documentId, pageCount: input.pages.length } }
        );
      }

      // Update context
      context.stages[this.name] = {
        status: 'processing',
        startTime,
      };

      // Process pages with Gemini OCR
      const ocrResult = await GeminiOCRService.processDocumentPages(
        input.pages,
        input.documentId
      );

      if (!ocrResult.success || !ocrResult.markdown) {
        throw new ProcessingError(
          ocrResult.error || "OCR processing failed",
          this.name,
          { documentId: input.documentId, pagesProcessed: ocrResult.pagesProcessed }
        );
      }

      const endTime = Date.now();
      const processingTime = endTime - startTime;

      // Analyze markdown quality
      const qualityMetrics = this.analyzeMarkdownQuality(ocrResult.markdown);

      // Count tokens (approximate)
      const tokenCount = this.estimateTokenCount(ocrResult.markdown);

      const output: OcrProcessingOutput = {
        markdown: ocrResult.markdown,
        pagesProcessed: ocrResult.pagesProcessed,
        qualityMetrics,
        metadata: {
          model: this.config.ocr.model,
          processingTime,
          tokenCount,
          debugMarkdownPath: this.config.ocr.saveDebugMarkdown ? this.config.ocr.debugMarkdownDir : undefined,
        },
      };

      // Update context with success
      context.stages[this.name] = {
        status: 'completed',
        startTime,
        endTime,
        metadata: {
          pagesProcessed: ocrResult.pagesProcessed,
          markdownLength: ocrResult.markdown.length,
          qualityScore: qualityMetrics.structureScore,
        },
      };

      Logger.info("OCR processing module completed successfully", {
        documentId: input.documentId,
        pagesProcessed: ocrResult.pagesProcessed,
        markdownLength: ocrResult.markdown.length,
        processingTime,
        contextId: context.requestId,
      });

      return {
        success: true,
        data: output,
        performance: {
          startTime,
          endTime,
          duration: processingTime,
        },
      };

    } catch (error) {
      const endTime = Date.now();
      const errorMessage = error instanceof Error ? error.message : String(error);

      // Update context with failure
      context.stages[this.name] = {
        status: 'failed',
        startTime,
        endTime,
        error: errorMessage,
      };

      Logger.error("OCR processing module failed", {
        documentId: input.documentId,
        error: errorMessage,
        processingTime: endTime - startTime,
        contextId: context.requestId,
      });

      return {
        success: false,
        error: errorMessage,
        performance: {
          startTime,
          endTime,
          duration: endTime - startTime,
        },
      };
    }
  }

  validateInput(input: OcrProcessingInput): boolean {
    if (!input.documentId || typeof input.documentId !== 'string') {
      Logger.error("Invalid documentId in OCR processing input", { input });
      return false;
    }

    if (!Array.isArray(input.pages) || input.pages.length === 0) {
      Logger.error("Invalid or empty pages array in OCR processing input", { input });
      return false;
    }

    // Validate each page has required properties
    for (const page of input.pages) {
      if (!page.pageNumber || !page.imageBase64) {
        Logger.error("Invalid page structure in OCR processing input", { page });
        return false;
      }
    }

    return true;
  }

  async cleanup(context: PipelineContext): Promise<void> {
    Logger.debug("Cleaning up OCR processing module", {
      contextId: context.requestId,
    });

    // Clean up debug markdown files if requested
    if (this.config.ocr.saveDebugMarkdown && context.globalMetadata?.cleanupDebugFiles) {
      Logger.debug("Debug markdown cleanup requested", {
        contextId: context.requestId,
        debugDir: this.config.ocr.debugMarkdownDir,
      });
    }
  }

  async getHealthStatus(): Promise<{ healthy: boolean; details?: Record<string, any> }> {
    try {
      const healthDetails = {
        configValid: this.isConfigValid(),
        geminiApiAvailable: true, // Would check actual API availability
        modelAccessible: true, // Would verify model access
        lastCheck: new Date().toISOString(),
      };

      const healthy = Object.values(healthDetails).every(v => v === true || typeof v === 'string');

      return {
        healthy,
        details: healthDetails,
      };
    } catch (error) {
      return {
        healthy: false,
        details: {
          error: error instanceof Error ? error.message : String(error),
          lastCheck: new Date().toISOString(),
        },
      };
    }
  }

  private isConfigValid(): boolean {
    const { ocr } = this.config;
    
    return (
      typeof ocr.model === 'string' && ocr.model.length > 0 &&
      typeof ocr.maxRetries === 'number' && ocr.maxRetries >= 0 &&
      typeof ocr.retryDelay === 'number' && ocr.retryDelay >= 0 &&
      typeof ocr.batchSize === 'number' && ocr.batchSize > 0 &&
      typeof ocr.systemPrompt === 'string' && ocr.systemPrompt.length > 0 &&
      typeof ocr.temperature === 'number' && ocr.temperature >= 0 && ocr.temperature <= 2 &&
      typeof ocr.maxOutputTokens === 'number' && ocr.maxOutputTokens > 0
    );
  }

  private analyzeMarkdownQuality(markdown: string): OcrProcessingOutput['qualityMetrics'] {
    const lines = markdown.split('\n');
    const totalCharacters = markdown.length;
    
    // Count blank/empty lines
    const blankLines = lines.filter(line => line.trim().length === 0).length;
    const blankPageCount = Math.floor(blankLines / 10); // Rough estimate
    
    // Analyze structure (headers, lists, tables, etc.)
    const headers = (markdown.match(/^#+\s/gm) || []).length;
    const lists = (markdown.match(/^[\s]*[-*+]\s/gm) || []).length;
    const tables = (markdown.match(/\|.*\|/g) || []).length;
    const codeBlocks = (markdown.match(/```/g) || []).length / 2;
    
    // Calculate structure score (0-1)
    const structureElements = headers + lists + tables + codeBlocks;
    const structureScore = Math.min(structureElements / Math.max(lines.length / 10, 1), 1);
    
    return {
      blankPageCount,
      totalCharacters,
      structureScore: Math.round(structureScore * 100) / 100,
    };
  }

  private estimateTokenCount(text: string): number {
    // Rough token estimation (actual implementation would use proper tokenizer)
    return Math.ceil(text.length / 4);
  }

  /**
   * Get module configuration for debugging
   */
  getConfig(): PipelineConfig['ocr'] {
    return this.config.ocr;
  }

  /**
   * Update module configuration (for runtime adjustments)
   */
  updateConfig(updates: Partial<PipelineConfig['ocr']>): void {
    Object.assign(this.config.ocr, updates);
    Logger.info("OCR processing module configuration updated", {
      updates,
      newConfig: this.config.ocr,
    });
  }
}
