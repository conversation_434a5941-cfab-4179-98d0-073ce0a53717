/**
 * Citation Generation Module
 * Handles similarity search and citation formatting
 */

import { Logger } from "../../../utils/Logger";
import { PipelineModule, PipelineResult, PipelineContext, CitationGenerationResult, ProcessingError } from "../types";
import { PipelineConfig } from "../config";
import { InlineCitationsGenerator } from "../../inline-citations-generator";

export interface CitationGenerationInput {
  messageId: string;
  chatId: string;
  content: string;
}

export interface CitationGenerationOutput {
  originalContent: string;
  citedContent: string;
  citations: Array<{
    id: string;
    text: string;
    sourceDocument: {
      id: string;
      title: string;
      url?: string;
    };
    pageNumbers?: number[];
    similarity: number;
    position: {
      start: number;
      end: number;
    };
  }>;
  referencesSection?: string;
  metadata: {
    processingTime: number;
    totalMatches: number;
    averageSimilarity: number;
    citationCount: number;
    qualityMetrics: {
      relevanceScore: number;
      coverageScore: number;
      diversityScore: number;
    };
  };
}

export class CitationGenerationModule implements PipelineModule<CitationGenerationInput, CitationGenerationOutput> {
  readonly name = 'citation-generation';
  readonly version = '1.0.0';
  readonly dependencies = ['embedding'];

  constructor(private config: PipelineConfig) {}

  async execute(
    input: CitationGenerationInput,
    context: PipelineContext
  ): Promise<PipelineResult<CitationGenerationOutput>> {
    const startTime = Date.now();
    
    try {
      Logger.info("Starting citation generation module", {
        messageId: input.messageId,
        chatId: input.chatId,
        contentLength: input.content.length,
        contextId: context.requestId,
      });

      // Validate input
      if (!this.validateInput(input)) {
        throw new ProcessingError(
          "Invalid input for citation generation",
          this.name,
          { input: { messageId: input.messageId, chatId: input.chatId } }
        );
      }

      // Update context
      context.stages[this.name] = {
        status: 'processing',
        startTime,
      };

      // Generate citations using existing service
      const citationResult = await InlineCitationsGenerator.generateInlineCitations(
        input.messageId,
        input.chatId,
        input.content
      );

      if (!citationResult.success) {
        throw new ProcessingError(
          citationResult.error || "Citation generation failed",
          this.name,
          { messageId: input.messageId, chatId: input.chatId }
        );
      }

      const endTime = Date.now();
      const processingTime = endTime - startTime;

      // Calculate quality metrics
      const qualityMetrics = this.calculateQualityMetrics(citationResult, input.content);

      const output: CitationGenerationOutput = {
        originalContent: citationResult.originalContent,
        citedContent: citationResult.citedContent,
        citations: citationResult.citations.map((citation, index) => ({
          id: citation.chunkId,
          text: citation.chunkText,
          sourceDocument: {
            id: citation.sourceDocumentId,
            title: citation.filename || 'Unknown Document',
            url: undefined,
          },
          pageNumbers: [citation.pageNumber],
          similarity: citation.similarity,
          position: {
            start: citationResult.insertionLocations[index] || 0,
            end: (citationResult.insertionLocations[index] || 0) + citation.chunkPreview.length,
          },
        })),
        referencesSection: undefined, // Not generated by the legacy service
        metadata: {
          processingTime,
          totalMatches: citationResult.citations.length,
          averageSimilarity: citationResult.citations.length > 0
            ? citationResult.citations.reduce((sum, c) => sum + c.similarity, 0) / citationResult.citations.length
            : 0,
          citationCount: citationResult.citations.length,
          qualityMetrics,
        },
      };

      // Update context with success
      context.stages[this.name] = {
        status: 'completed',
        startTime,
        endTime,
        metadata: {
          citationCount: citationResult.citations.length,
          averageSimilarity: citationResult.citations.length > 0
            ? citationResult.citations.reduce((sum, c) => sum + c.similarity, 0) / citationResult.citations.length
            : 0,
          processingTime,
        },
      };

      Logger.info("Citation generation module completed successfully", {
        messageId: input.messageId,
        citationCount: citationResult.citations.length,
        averageSimilarity: citationResult.citations.length > 0
          ? citationResult.citations.reduce((sum: number, c: any) => sum + c.similarity, 0) / citationResult.citations.length
          : 0,
        processingTime,
        contextId: context.requestId,
      });

      return {
        success: true,
        data: output,
        performance: {
          startTime,
          endTime,
          duration: processingTime,
        },
      };

    } catch (error) {
      const endTime = Date.now();
      const errorMessage = error instanceof Error ? error.message : String(error);

      // Update context with failure
      context.stages[this.name] = {
        status: 'failed',
        startTime,
        endTime,
        error: errorMessage,
      };

      Logger.error("Citation generation module failed", {
        messageId: input.messageId,
        chatId: input.chatId,
        error: errorMessage,
        processingTime: endTime - startTime,
        contextId: context.requestId,
      });

      return {
        success: false,
        error: errorMessage,
        performance: {
          startTime,
          endTime,
          duration: endTime - startTime,
        },
      };
    }
  }

  validateInput(input: CitationGenerationInput): boolean {
    if (!input.messageId || typeof input.messageId !== 'string') {
      Logger.error("Invalid messageId in citation generation input", { input });
      return false;
    }

    if (!input.chatId || typeof input.chatId !== 'string') {
      Logger.error("Invalid chatId in citation generation input", { input });
      return false;
    }

    if (!input.content || typeof input.content !== 'string') {
      Logger.error("Invalid content in citation generation input", { input });
      return false;
    }

    if (input.content.length === 0) {
      Logger.error("Empty content in citation generation input", { input });
      return false;
    }

    return true;
  }

  async cleanup(context: PipelineContext): Promise<void> {
    Logger.debug("Cleaning up citation generation module", {
      contextId: context.requestId,
    });

    // Clean up any temporary data or caches
  }

  async getHealthStatus(): Promise<{ healthy: boolean; details?: Record<string, any> }> {
    try {
      const healthDetails = {
        configValid: this.isConfigValid(),
        generatorServiceAvailable: true, // Would test actual generator service
        databaseConnected: true, // Would test database connectivity
        lastCheck: new Date().toISOString(),
      };

      const healthy = Object.values(healthDetails).every(v => v === true || typeof v === 'string');

      return {
        healthy,
        details: healthDetails,
      };
    } catch (error) {
      return {
        healthy: false,
        details: {
          error: error instanceof Error ? error.message : String(error),
          lastCheck: new Date().toISOString(),
        },
      };
    }
  }

  private isConfigValid(): boolean {
    const { generation } = this.config;
    
    return (
      typeof generation.maxSimilarityResults === 'number' && generation.maxSimilarityResults > 0 &&
      typeof generation.similarityThreshold === 'number' && 
      generation.similarityThreshold >= 0 && generation.similarityThreshold <= 1 &&
      typeof generation.maxCitationsPerMessage === 'number' && generation.maxCitationsPerMessage > 0 &&
      typeof generation.enableContextualRanking === 'boolean' &&
      typeof generation.preserveMarkdownStructure === 'boolean' &&
      ['superscript', 'inline', 'footnote'].includes(generation.citationFormat) &&
      typeof generation.enableReferencesSection === 'boolean'
    );
  }

  private calculateQualityMetrics(
    citationResult: any, // Using any since we're adapting from legacy CitationResult
    originalContent: string
  ) {
    const citations = citationResult.citations;
    
    // Calculate relevance score (average similarity)
    const relevanceScore = citations.length > 0
      ? citations.reduce((sum: number, c: any) => sum + c.similarity, 0) / citations.length
      : 0;

    // Calculate coverage score (how much of the content is cited)
    const citedCharacters = citations.reduce((sum: number, c: any) =>
      sum + (c.chunkPreview?.length || 0), 0
    );
    const coverageScore = originalContent.length > 0
      ? Math.min(citedCharacters / originalContent.length, 1)
      : 0;

    // Calculate diversity score (how many different sources)
    const uniqueSources = new Set(citations.map((c: any) => c.sourceDocumentId));
    const diversityScore = citations.length > 0
      ? uniqueSources.size / citations.length
      : 0;

    return {
      relevanceScore: Math.round(relevanceScore * 100) / 100,
      coverageScore: Math.round(coverageScore * 100) / 100,
      diversityScore: Math.round(diversityScore * 100) / 100,
    };
  }

  /**
   * Get module configuration for debugging
   */
  getConfig(): PipelineConfig['generation'] {
    return this.config.generation;
  }

  /**
   * Update module configuration (for runtime adjustments)
   */
  updateConfig(updates: Partial<PipelineConfig['generation']>): void {
    Object.assign(this.config.generation, updates);
    Logger.info("Citation generation module configuration updated", {
      updates,
      newConfig: this.config.generation,
    });
  }
}
