/**
 * Embedding Module
 * Handles generation and storage of vector embeddings for chunks
 */

import { Logger } from "../../../utils/Logger";
import { PipelineModule, PipelineResult, PipelineContext, MarkdownChunk, EmbeddingResult, ProcessingError } from "../types";
import { PipelineConfig } from "../config";
import { OpenAIEmbeddingService } from "../../openai-embedding";
import { GeminiEmbeddingService } from "../../gemini-embedding";

export interface EmbeddingInput {
  documentId: string;
  chunks: MarkdownChunk[];
}

export interface EmbeddingOutput {
  processedChunks: number;
  totalEmbeddings: number;
  averageVectorLength: number;
  embeddingProvider: string;
  embeddingModel: string;
  metadata: {
    processingTime: number;
    batchCount: number;
    failedChunks: number;
    qualityMetrics: {
      averageMagnitude: number;
      vectorConsistency: number;
      normalizationStatus: 'normalized' | 'not_normalized' | 'mixed';
    };
  };
}

export class EmbeddingModule implements PipelineModule<EmbeddingInput, EmbeddingOutput> {
  readonly name = 'embedding';
  readonly version = '1.0.0';
  readonly dependencies = ['chunking'];

  constructor(private config: PipelineConfig) {}

  async execute(
    input: EmbeddingInput,
    context: PipelineContext
  ): Promise<PipelineResult<EmbeddingOutput>> {
    const startTime = Date.now();
    
    try {
      Logger.info("Starting embedding module", {
        documentId: input.documentId,
        chunkCount: input.chunks.length,
        provider: this.config.embedding.provider,
        model: this.config.embedding.model,
        contextId: context.requestId,
      });

      // Validate input
      if (!this.validateInput(input)) {
        throw new ProcessingError(
          "Invalid input for embedding generation",
          this.name,
          { input: { documentId: input.documentId, chunkCount: input.chunks.length } }
        );
      }

      // Update context
      context.stages[this.name] = {
        status: 'processing',
        startTime,
      };

      // Convert chunks to the format expected by embedding services
      const embeddingChunks = this.convertToEmbeddingFormat(input.chunks);

      // Generate embeddings using configured provider
      let embeddingResult: EmbeddingResult;
      
      if (this.config.embedding.provider === 'openai') {
        embeddingResult = await OpenAIEmbeddingService.generateAndStoreEmbeddings(embeddingChunks);
      } else if (this.config.embedding.provider === 'gemini') {
        embeddingResult = await GeminiEmbeddingService.generateAndStoreEmbeddings(embeddingChunks);
      } else {
        throw new ProcessingError(
          `Unsupported embedding provider: ${this.config.embedding.provider}`,
          this.name,
          { provider: this.config.embedding.provider }
        );
      }

      if (!embeddingResult.success) {
        throw new ProcessingError(
          embeddingResult.error || "Embedding generation failed",
          this.name,
          { documentId: input.documentId, processedChunks: embeddingResult.processedChunks }
        );
      }

      const endTime = Date.now();
      const processingTime = endTime - startTime;

      // Calculate quality metrics
      const qualityMetrics = await this.assessEmbeddingQuality(input.documentId);

      const output: EmbeddingOutput = {
        processedChunks: embeddingResult.processedChunks,
        totalEmbeddings: embeddingResult.totalEmbeddings,
        averageVectorLength: embeddingResult.averageVectorLength,
        embeddingProvider: this.config.embedding.provider,
        embeddingModel: this.config.embedding.model,
        metadata: {
          processingTime,
          batchCount: Math.ceil(input.chunks.length / this.config.embedding.batchSize),
          failedChunks: input.chunks.length - embeddingResult.processedChunks,
          qualityMetrics,
        },
      };

      // Update context with success
      context.stages[this.name] = {
        status: 'completed',
        startTime,
        endTime,
        metadata: {
          processedChunks: embeddingResult.processedChunks,
          totalEmbeddings: embeddingResult.totalEmbeddings,
          provider: this.config.embedding.provider,
        },
      };

      Logger.info("Embedding module completed successfully", {
        documentId: input.documentId,
        processedChunks: embeddingResult.processedChunks,
        totalEmbeddings: embeddingResult.totalEmbeddings,
        processingTime,
        contextId: context.requestId,
      });

      return {
        success: true,
        data: output,
        performance: {
          startTime,
          endTime,
          duration: processingTime,
        },
      };

    } catch (error) {
      const endTime = Date.now();
      const errorMessage = error instanceof Error ? error.message : String(error);

      // Update context with failure
      context.stages[this.name] = {
        status: 'failed',
        startTime,
        endTime,
        error: errorMessage,
      };

      Logger.error("Embedding module failed", {
        documentId: input.documentId,
        error: errorMessage,
        processingTime: endTime - startTime,
        contextId: context.requestId,
      });

      return {
        success: false,
        error: errorMessage,
        performance: {
          startTime,
          endTime,
          duration: endTime - startTime,
        },
      };
    }
  }

  validateInput(input: EmbeddingInput): boolean {
    if (!input.documentId || typeof input.documentId !== 'string') {
      Logger.error("Invalid documentId in embedding input", { input });
      return false;
    }

    if (!Array.isArray(input.chunks) || input.chunks.length === 0) {
      Logger.error("Invalid or empty chunks array in embedding input", { input });
      return false;
    }

    // Validate each chunk has required properties
    for (const chunk of input.chunks) {
      if (!chunk.id || !chunk.text || typeof chunk.tokenCount !== 'number') {
        Logger.error("Invalid chunk structure in embedding input", { chunk });
        return false;
      }
    }

    return true;
  }

  async cleanup(context: PipelineContext): Promise<void> {
    Logger.debug("Cleaning up embedding module", {
      contextId: context.requestId,
    });

    // Clean up any cached embeddings or temporary data
    // The embedding services handle their own cleanup
  }

  async getHealthStatus(): Promise<{ healthy: boolean; details?: Record<string, any> }> {
    try {
      const healthDetails = {
        configValid: this.isConfigValid(),
        providerAvailable: await this.checkProviderHealth(),
        lastCheck: new Date().toISOString(),
      };

      const healthy = Object.values(healthDetails).every(v => v === true || typeof v === 'string');

      return {
        healthy,
        details: healthDetails,
      };
    } catch (error) {
      return {
        healthy: false,
        details: {
          error: error instanceof Error ? error.message : String(error),
          lastCheck: new Date().toISOString(),
        },
      };
    }
  }

  private isConfigValid(): boolean {
    const { embedding } = this.config;
    
    return (
      ['openai', 'gemini'].includes(embedding.provider) &&
      typeof embedding.model === 'string' && embedding.model.length > 0 &&
      typeof embedding.outputDimensionality === 'number' && embedding.outputDimensionality > 0 &&
      typeof embedding.batchSize === 'number' && embedding.batchSize > 0 &&
      typeof embedding.maxRetries === 'number' && embedding.maxRetries >= 0 &&
      typeof embedding.retryDelay === 'number' && embedding.retryDelay >= 0 &&
      typeof embedding.verifyNormalization === 'boolean'
    );
  }

  private async checkProviderHealth(): Promise<boolean> {
    try {
      // Simple health check - attempt to generate a test embedding
      if (this.config.embedding.provider === 'openai') {
        // Would check OpenAI API availability
        return true;
      } else if (this.config.embedding.provider === 'gemini') {
        // Would check Gemini API availability
        return true;
      }
      return false;
    } catch (error) {
      Logger.debug("Provider health check failed", { error });
      return false;
    }
  }

  private convertToEmbeddingFormat(chunks: MarkdownChunk[]) {
    // Convert our MarkdownChunk format to the format expected by embedding services
    return chunks.map(chunk => ({
      id: chunk.id,
      text: chunk.text,
      tokenCount: chunk.tokenCount,
      metadata: {
        chunk_id: chunk.id,
        page_number: chunk.metadata.page_numbers?.[0] || 1,
        source: chunk.metadata.source_type,
        token_count: chunk.tokenCount,
        chunk_index: chunk.chunkIndex,
        document_id: chunk.metadata.document_id,
      },
    }));
  }

  private async assessEmbeddingQuality(documentId: string) {
    try {
      // This would query the database to analyze the stored embeddings
      // For now, return default values
      return {
        averageMagnitude: 1.0,
        vectorConsistency: 0.95,
        normalizationStatus: 'normalized' as const,
      };
    } catch (error) {
      Logger.warn("Failed to assess embedding quality", { documentId, error });
      return {
        averageMagnitude: 0,
        vectorConsistency: 0,
        normalizationStatus: 'not_normalized' as const,
      };
    }
  }

  /**
   * Get module configuration for debugging
   */
  getConfig(): PipelineConfig['embedding'] {
    return this.config.embedding;
  }

  /**
   * Update module configuration (for runtime adjustments)
   */
  updateConfig(updates: Partial<PipelineConfig['embedding']>): void {
    Object.assign(this.config.embedding, updates);
    Logger.info("Embedding module configuration updated", {
      updates,
      newConfig: this.config.embedding,
    });
  }
}
