/**
 * Chunking Module
 * Handles segmentation of markdown content into chunks for embedding
 */

import { Logger } from "../../../utils/Logger";
import { PipelineModule, PipelineResult, PipelineContext, MarkdownChunk, ProcessingError } from "../types";
import { PipelineConfig } from "../config";
import { InlineCitationsChunker } from "../../inline-citations-chunker";

export interface ChunkingInput {
  documentId: string;
  markdown: string;
  sourceType: 'pdf' | 'docx' | 'txt' | 'image';
  metadata?: {
    pagesProcessed?: number;
    qualityScore?: number;
  };
}

export interface ChunkingOutput {
  chunks: MarkdownChunk[];
  totalChunks: number;
  averageChunkSize: number;
  chunkingStrategy: string;
  metadata: {
    processingTime: number;
    tokenDistribution: {
      min: number;
      max: number;
      average: number;
      median: number;
    };
    qualityMetrics: {
      structurePreservation: number;
      semanticCoherence: number;
    };
  };
}

export class ChunkingModule implements PipelineModule<ChunkingInput, ChunkingOutput> {
  readonly name = 'chunking';
  readonly version = '1.0.0';
  readonly dependencies = ['markdown-storage'];

  constructor(private config: PipelineConfig) {}

  async execute(
    input: ChunkingInput,
    context: PipelineContext
  ): Promise<PipelineResult<ChunkingOutput>> {
    const startTime = Date.now();
    
    try {
      Logger.info("Starting chunking module", {
        documentId: input.documentId,
        markdownLength: input.markdown.length,
        strategy: this.config.chunking.strategy,
        contextId: context.requestId,
      });

      // Validate input
      if (!this.validateInput(input)) {
        throw new ProcessingError(
          "Invalid input for chunking",
          this.name,
          { input: { documentId: input.documentId, markdownLength: input.markdown.length } }
        );
      }

      // Update context
      context.stages[this.name] = {
        status: 'processing',
        startTime,
      };

      // Perform chunking using existing service
      const chunkingResult = await InlineCitationsChunker.chunkMarkdownForInlineCitations(
        input.markdown,
        input.documentId
      );

      if (!chunkingResult.success) {
        throw new ProcessingError(
          chunkingResult.error || "Chunking failed",
          this.name,
          { documentId: input.documentId }
        );
      }

      // Convert to our module format and enhance with metadata
      const enhancedChunks: MarkdownChunk[] = chunkingResult.chunks.map((chunk, index) => ({
        id: chunk.id,
        text: chunk.text,
        tokenCount: chunk.metadata.token_count,
        chunkIndex: index,
        metadata: {
          document_id: input.documentId,
          source_type: input.sourceType,
          page_numbers: [chunk.metadata.page_number],
          section_title: undefined, // Not available in InlineCitationsChunk
          chunk_strategy: this.config.chunking.strategy,
          created_at: new Date(),
        },
      }));

      const endTime = Date.now();
      const processingTime = endTime - startTime;

      // Calculate metrics
      const tokenCounts = enhancedChunks.map(c => c.tokenCount);
      const averageChunkSize = tokenCounts.reduce((sum, count) => sum + count, 0) / tokenCounts.length;
      const tokenDistribution = this.calculateTokenDistribution(tokenCounts);
      const qualityMetrics = this.assessChunkingQuality(enhancedChunks, input.markdown);

      const output: ChunkingOutput = {
        chunks: enhancedChunks,
        totalChunks: enhancedChunks.length,
        averageChunkSize,
        chunkingStrategy: this.config.chunking.strategy,
        metadata: {
          processingTime,
          tokenDistribution,
          qualityMetrics,
        },
      };

      // Update context with success
      context.stages[this.name] = {
        status: 'completed',
        startTime,
        endTime,
        metadata: {
          totalChunks: enhancedChunks.length,
          averageChunkSize,
          strategy: this.config.chunking.strategy,
        },
      };

      Logger.info("Chunking module completed successfully", {
        documentId: input.documentId,
        totalChunks: enhancedChunks.length,
        averageChunkSize,
        processingTime,
        contextId: context.requestId,
      });

      return {
        success: true,
        data: output,
        performance: {
          startTime,
          endTime,
          duration: processingTime,
        },
      };

    } catch (error) {
      const endTime = Date.now();
      const errorMessage = error instanceof Error ? error.message : String(error);

      // Update context with failure
      context.stages[this.name] = {
        status: 'failed',
        startTime,
        endTime,
        error: errorMessage,
      };

      Logger.error("Chunking module failed", {
        documentId: input.documentId,
        error: errorMessage,
        processingTime: endTime - startTime,
        contextId: context.requestId,
      });

      return {
        success: false,
        error: errorMessage,
        performance: {
          startTime,
          endTime,
          duration: endTime - startTime,
        },
      };
    }
  }

  validateInput(input: ChunkingInput): boolean {
    if (!input.documentId || typeof input.documentId !== 'string') {
      Logger.error("Invalid documentId in chunking input", { input });
      return false;
    }

    if (!input.markdown || typeof input.markdown !== 'string') {
      Logger.error("Invalid markdown content in chunking input", { input });
      return false;
    }

    if (input.markdown.length === 0) {
      Logger.error("Empty markdown content in chunking input", { input });
      return false;
    }

    if (!input.sourceType || !['pdf', 'docx', 'txt', 'image'].includes(input.sourceType)) {
      Logger.error("Invalid source type in chunking input", { input });
      return false;
    }

    return true;
  }

  async cleanup(context: PipelineContext): Promise<void> {
    Logger.debug("Cleaning up chunking module", {
      contextId: context.requestId,
    });

    // Clean up any temporary data structures or caches
    // The chunking process is stateless, so minimal cleanup needed
  }

  async getHealthStatus(): Promise<{ healthy: boolean; details?: Record<string, any> }> {
    try {
      const healthDetails = {
        configValid: this.isConfigValid(),
        chunkingServiceAvailable: true, // Would test actual chunking service
        lastCheck: new Date().toISOString(),
      };

      const healthy = Object.values(healthDetails).every(v => v === true || typeof v === 'string');

      return {
        healthy,
        details: healthDetails,
      };
    } catch (error) {
      return {
        healthy: false,
        details: {
          error: error instanceof Error ? error.message : String(error),
          lastCheck: new Date().toISOString(),
        },
      };
    }
  }

  private isConfigValid(): boolean {
    const { chunking } = this.config;
    
    return (
      typeof chunking.maxTokens === 'number' && chunking.maxTokens > 0 &&
      typeof chunking.overlapTokens === 'number' && chunking.overlapTokens >= 0 &&
      typeof chunking.minChunkSize === 'number' && chunking.minChunkSize > 0 &&
      ['semantic', 'section', 'paragraph'].includes(chunking.strategy) &&
      typeof chunking.preserveStructure === 'boolean' &&
      typeof chunking.enableOptimizations === 'boolean'
    );
  }

  private calculateTokenDistribution(tokenCounts: number[]) {
    const sorted = [...tokenCounts].sort((a, b) => a - b);
    const sum = sorted.reduce((a, b) => a + b, 0);
    
    return {
      min: sorted[0] || 0,
      max: sorted[sorted.length - 1] || 0,
      average: Math.round(sum / sorted.length) || 0,
      median: sorted.length % 2 === 0 
        ? Math.round((sorted[sorted.length / 2 - 1] + sorted[sorted.length / 2]) / 2)
        : sorted[Math.floor(sorted.length / 2)] || 0,
    };
  }

  private assessChunkingQuality(chunks: MarkdownChunk[], originalMarkdown: string) {
    // Assess structure preservation
    const originalHeaders = (originalMarkdown.match(/^#+\s/gm) || []).length;
    const chunkHeaders = chunks.reduce((count, chunk) => 
      count + (chunk.text.match(/^#+\s/gm) || []).length, 0
    );
    const structurePreservation = originalHeaders > 0 ? 
      Math.min(chunkHeaders / originalHeaders, 1) : 1;

    // Assess semantic coherence (simplified)
    const averageChunkCoherence = chunks.reduce((sum, chunk) => {
      // Simple coherence metric based on sentence boundaries
      const sentences = chunk.text.split(/[.!?]+/).filter(s => s.trim().length > 0);
      const coherenceScore = sentences.length > 0 ? 
        Math.min(chunk.text.length / (sentences.length * 50), 1) : 0;
      return sum + coherenceScore;
    }, 0) / chunks.length;

    return {
      structurePreservation: Math.round(structurePreservation * 100) / 100,
      semanticCoherence: Math.round(averageChunkCoherence * 100) / 100,
    };
  }

  /**
   * Get module configuration for debugging
   */
  getConfig(): PipelineConfig['chunking'] {
    return this.config.chunking;
  }

  /**
   * Update module configuration (for runtime adjustments)
   */
  updateConfig(updates: Partial<PipelineConfig['chunking']>): void {
    Object.assign(this.config.chunking, updates);
    Logger.info("Chunking module configuration updated", {
      updates,
      newConfig: this.config.chunking,
    });
  }
}
