/**
 * Citation Trigger Module
 * Handles decision logic for when to generate inline citations
 */

import { Logger } from "../../../utils/Logger";
import { PipelineModule, PipelineResult, PipelineContext, TriggerResult, TriggerConditions, ProcessingError } from "../types";
import { PipelineConfig } from "../config";
import { InlineCitationsTrigger } from "../../inline-citations-trigger";

export interface CitationTriggerInput {
  messageId: string;
  chatId: string;
  messages: any[];
  chatExists: boolean;
  isReadonly: boolean;
  content?: string;
}

export interface CitationTriggerOutput {
  shouldTrigger: boolean;
  conditions: TriggerConditions;
  reason: string;
  confidence: number;
  metadata: {
    evaluationTime: number;
    conditionsChecked: string[];
    triggerScore: number;
    riskFactors: string[];
  };
}

export class CitationTriggerModule implements PipelineModule<CitationTriggerInput, CitationTriggerOutput> {
  readonly name = 'citation-trigger';
  readonly version = '1.0.0';
  readonly dependencies = [];

  constructor(private config: PipelineConfig) {}

  async execute(
    input: CitationTriggerInput,
    context: PipelineContext
  ): Promise<PipelineResult<CitationTriggerOutput>> {
    const startTime = Date.now();
    
    try {
      Logger.info("Starting citation trigger module", {
        messageId: input.messageId,
        chatId: input.chatId,
        messageCount: input.messages.length,
        chatExists: input.chatExists,
        isReadonly: input.isReadonly,
        contextId: context.requestId,
      });

      // Validate input
      if (!this.validateInput(input)) {
        throw new ProcessingError(
          "Invalid input for citation trigger evaluation",
          this.name,
          { input: { messageId: input.messageId, chatId: input.chatId } }
        );
      }

      // Update context
      context.stages[this.name] = {
        status: 'processing',
        startTime,
      };

      // Use existing trigger service
      const triggerResult = await InlineCitationsTrigger.shouldTriggerInlineCitations(
        input.messageId,
        input.chatId,
        input.messages,
        input.chatExists,
        input.isReadonly
      );

      const endTime = Date.now();
      const evaluationTime = endTime - startTime;

      // Convert legacy TriggerConditions to our pipeline format
      const pipelineConditions: TriggerConditions = {
        hasDocuments: triggerResult.conditions.hasDocuments,
        documentCount: triggerResult.conditions.documentCount,
        hasRecentDocuments: triggerResult.conditions.hasDocuments, // Assume recent if has documents
        messageLength: input.content?.length || 0,
        isReadonly: input.isReadonly,
        chatExists: triggerResult.conditions.chatExists,
        performanceAllowed: true, // Default to true
        customConditions: {
          isChatInitiated: triggerResult.conditions.isChatInitiated,
          isPreviewMode: triggerResult.conditions.isPreviewMode,
          isDraftMode: triggerResult.conditions.isDraftMode,
        },
      };

      // Enhance the result with additional analysis
      const enhancedAnalysis = this.enhanceTriggerAnalysis(triggerResult, input, pipelineConditions);

      const output: CitationTriggerOutput = {
        shouldTrigger: triggerResult.shouldTrigger,
        conditions: pipelineConditions,
        reason: triggerResult.reason,
        confidence: enhancedAnalysis.confidence,
        metadata: {
          evaluationTime,
          conditionsChecked: enhancedAnalysis.conditionsChecked,
          triggerScore: enhancedAnalysis.triggerScore,
          riskFactors: enhancedAnalysis.riskFactors,
        },
      };

      // Update context with success
      context.stages[this.name] = {
        status: 'completed',
        startTime,
        endTime,
        metadata: {
          shouldTrigger: triggerResult.shouldTrigger,
          reason: triggerResult.reason,
          confidence: enhancedAnalysis.confidence,
        },
      };

      Logger.info("Citation trigger module completed successfully", {
        messageId: input.messageId,
        shouldTrigger: triggerResult.shouldTrigger,
        reason: triggerResult.reason,
        confidence: enhancedAnalysis.confidence,
        evaluationTime,
        contextId: context.requestId,
      });

      return {
        success: true,
        data: output,
        performance: {
          startTime,
          endTime,
          duration: evaluationTime,
        },
      };

    } catch (error) {
      const endTime = Date.now();
      const errorMessage = error instanceof Error ? error.message : String(error);

      // Update context with failure
      context.stages[this.name] = {
        status: 'failed',
        startTime,
        endTime,
        error: errorMessage,
      };

      Logger.error("Citation trigger module failed", {
        messageId: input.messageId,
        chatId: input.chatId,
        error: errorMessage,
        evaluationTime: endTime - startTime,
        contextId: context.requestId,
      });

      return {
        success: false,
        error: errorMessage,
        performance: {
          startTime,
          endTime,
          duration: endTime - startTime,
        },
      };
    }
  }

  validateInput(input: CitationTriggerInput): boolean {
    if (!input.messageId || typeof input.messageId !== 'string') {
      Logger.error("Invalid messageId in citation trigger input", { input });
      return false;
    }

    if (!input.chatId || typeof input.chatId !== 'string') {
      Logger.error("Invalid chatId in citation trigger input", { input });
      return false;
    }

    if (!Array.isArray(input.messages)) {
      Logger.error("Invalid messages array in citation trigger input", { input });
      return false;
    }

    if (typeof input.chatExists !== 'boolean') {
      Logger.error("Invalid chatExists value in citation trigger input", { input });
      return false;
    }

    if (typeof input.isReadonly !== 'boolean') {
      Logger.error("Invalid isReadonly value in citation trigger input", { input });
      return false;
    }

    return true;
  }

  async cleanup(context: PipelineContext): Promise<void> {
    Logger.debug("Cleaning up citation trigger module", {
      contextId: context.requestId,
    });

    // Trigger evaluation is stateless, minimal cleanup needed
  }

  async getHealthStatus(): Promise<{ healthy: boolean; details?: Record<string, any> }> {
    try {
      const healthDetails = {
        configValid: this.isConfigValid(),
        triggerServiceAvailable: true, // Would test actual trigger service
        lastCheck: new Date().toISOString(),
      };

      const healthy = Object.values(healthDetails).every(v => v === true || typeof v === 'string');

      return {
        healthy,
        details: healthDetails,
      };
    } catch (error) {
      return {
        healthy: false,
        details: {
          error: error instanceof Error ? error.message : String(error),
          lastCheck: new Date().toISOString(),
        },
      };
    }
  }

  private isConfigValid(): boolean {
    const { trigger } = this.config;
    
    return (
      typeof trigger.minDocuments === 'number' && trigger.minDocuments >= 0 &&
      typeof trigger.minMessageLength === 'number' && trigger.minMessageLength >= 0 &&
      typeof trigger.maxMessagesBack === 'number' && trigger.maxMessagesBack > 0 &&
      typeof trigger.requireRecentDocuments === 'boolean' &&
      typeof trigger.recentDocumentHours === 'number' && trigger.recentDocumentHours > 0 &&
      typeof trigger.skipReadonlyChats === 'boolean' &&
      typeof trigger.enablePerformanceChecks === 'boolean'
    );
  }

  private enhanceTriggerAnalysis(
    triggerResult: any, // Legacy TriggerResult
    input: CitationTriggerInput,
    conditions: TriggerConditions
  ) {
    const conditionsChecked: string[] = [];
    const riskFactors: string[] = [];
    let triggerScore = 0;

    // Analyze each condition
    if (conditions.hasDocuments) {
      conditionsChecked.push('hasDocuments');
      triggerScore += 20;
    } else {
      riskFactors.push('no_documents');
    }

    if (conditions.hasRecentDocuments) {
      conditionsChecked.push('hasRecentDocuments');
      triggerScore += 15;
    } else if (conditions.hasDocuments) {
      riskFactors.push('old_documents');
    }

    if (conditions.messageLength >= this.config.trigger.minMessageLength) {
      conditionsChecked.push('messageLength');
      triggerScore += 10;
    } else {
      riskFactors.push('short_message');
    }

    if (!conditions.isReadonly) {
      conditionsChecked.push('notReadonly');
      triggerScore += 10;
    } else {
      riskFactors.push('readonly_chat');
    }

    if (conditions.chatExists) {
      conditionsChecked.push('chatExists');
      triggerScore += 5;
    }

    if (conditions.performanceAllowed) {
      conditionsChecked.push('performanceAllowed');
      triggerScore += 10;
    } else {
      riskFactors.push('performance_constraints');
    }

    // Document count bonus
    if (conditions.documentCount > 1) {
      triggerScore += Math.min(conditions.documentCount * 5, 20);
    }

    // Calculate confidence based on trigger score and risk factors
    const maxScore = 90; // Maximum possible score
    const baseConfidence = Math.min(triggerScore / maxScore, 1);
    const riskPenalty = riskFactors.length * 0.1;
    const confidence = Math.max(baseConfidence - riskPenalty, 0);

    return {
      conditionsChecked,
      riskFactors,
      triggerScore,
      confidence: Math.round(confidence * 100) / 100,
    };
  }

  /**
   * Get module configuration for debugging
   */
  getConfig(): PipelineConfig['trigger'] {
    return this.config.trigger;
  }

  /**
   * Update module configuration (for runtime adjustments)
   */
  updateConfig(updates: Partial<PipelineConfig['trigger']>): void {
    Object.assign(this.config.trigger, updates);
    Logger.info("Citation trigger module configuration updated", {
      updates,
      newConfig: this.config.trigger,
    });
  }
}
