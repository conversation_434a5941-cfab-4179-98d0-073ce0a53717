/**
 * Markdown Storage Module
 * Handles database storage of processed markdown content
 */

import { Logger } from "../../../utils/Logger";
import { PipelineModule, PipelineResult, PipelineContext, ProcessingError } from "../types";
import { PipelineConfig } from "../config";
import { db } from "../../../db";
import { resources } from "../../../db/schema";
import { eq } from "drizzle-orm";

export interface MarkdownStorageInput {
  documentId: string;
  markdown: string;
  metadata?: {
    pagesProcessed: number;
    processingTime: number;
    qualityScore?: number;
    ocrModel?: string;
  };
}

export interface MarkdownStorageOutput {
  stored: boolean;
  resourceId: string;
  markdownLength: number;
  storageTime: number;
  metadata: {
    databaseOperation: 'insert' | 'update';
    transactionId?: string;
    compressionRatio?: number;
  };
}

export class MarkdownStorageModule implements PipelineModule<MarkdownStorageInput, MarkdownStorageOutput> {
  readonly name = 'markdown-storage';
  readonly version = '1.0.0';
  readonly dependencies = ['ocr-processing'];

  constructor(private config: PipelineConfig) {}

  async execute(
    input: MarkdownStorageInput,
    context: PipelineContext
  ): Promise<PipelineResult<MarkdownStorageOutput>> {
    const startTime = Date.now();
    
    try {
      Logger.info("Starting markdown storage module", {
        documentId: input.documentId,
        markdownLength: input.markdown.length,
        contextId: context.requestId,
      });

      // Validate input
      if (!this.validateInput(input)) {
        throw new ProcessingError(
          "Invalid input for markdown storage",
          this.name,
          { input: { documentId: input.documentId, markdownLength: input.markdown.length } }
        );
      }

      // Update context
      context.stages[this.name] = {
        status: 'processing',
        startTime,
      };

      // Check if resource already exists
      const existingResource = await this.findExistingResource(input.documentId);
      
      let resourceId: string;
      let databaseOperation: 'insert' | 'update';

      if (existingResource) {
        // Update existing resource
        await this.updateResource(existingResource.id, input.markdown, input.metadata);
        resourceId = existingResource.id;
        databaseOperation = 'update';
        
        Logger.debug("Updated existing markdown resource", {
          documentId: input.documentId,
          resourceId,
          contextId: context.requestId,
        });
      } else {
        // Create new resource
        resourceId = await this.createResource(input.documentId, input.markdown, input.metadata);
        databaseOperation = 'insert';
        
        Logger.debug("Created new markdown resource", {
          documentId: input.documentId,
          resourceId,
          contextId: context.requestId,
        });
      }

      const endTime = Date.now();
      const storageTime = endTime - startTime;

      const output: MarkdownStorageOutput = {
        stored: true,
        resourceId,
        markdownLength: input.markdown.length,
        storageTime,
        metadata: {
          databaseOperation,
          compressionRatio: this.calculateCompressionRatio(input.markdown),
        },
      };

      // Update context with success
      context.stages[this.name] = {
        status: 'completed',
        startTime,
        endTime,
        metadata: {
          resourceId,
          databaseOperation,
          markdownLength: input.markdown.length,
        },
      };

      Logger.info("Markdown storage module completed successfully", {
        documentId: input.documentId,
        resourceId,
        databaseOperation,
        storageTime,
        contextId: context.requestId,
      });

      return {
        success: true,
        data: output,
        performance: {
          startTime,
          endTime,
          duration: storageTime,
        },
      };

    } catch (error) {
      const endTime = Date.now();
      const errorMessage = error instanceof Error ? error.message : String(error);

      // Update context with failure
      context.stages[this.name] = {
        status: 'failed',
        startTime,
        endTime,
        error: errorMessage,
      };

      Logger.error("Markdown storage module failed", {
        documentId: input.documentId,
        error: errorMessage,
        storageTime: endTime - startTime,
        contextId: context.requestId,
      });

      return {
        success: false,
        error: errorMessage,
        performance: {
          startTime,
          endTime,
          duration: endTime - startTime,
        },
      };
    }
  }

  validateInput(input: MarkdownStorageInput): boolean {
    if (!input.documentId || typeof input.documentId !== 'string') {
      Logger.error("Invalid documentId in markdown storage input", { input });
      return false;
    }

    if (!input.markdown || typeof input.markdown !== 'string') {
      Logger.error("Invalid markdown content in storage input", { input });
      return false;
    }

    if (input.markdown.length === 0) {
      Logger.error("Empty markdown content in storage input", { input });
      return false;
    }

    return true;
  }

  async cleanup(context: PipelineContext): Promise<void> {
    Logger.debug("Cleaning up markdown storage module", {
      contextId: context.requestId,
    });

    // Close any open database connections if needed
    // This is handled by the connection pool, but we could add specific cleanup here
  }

  async getHealthStatus(): Promise<{ healthy: boolean; details?: Record<string, any> }> {
    try {
      // Test database connectivity
      const testQuery = await db.select().from(resources).limit(1);
      
      const healthDetails = {
        databaseConnected: true,
        configValid: this.isConfigValid(),
        lastCheck: new Date().toISOString(),
        testQuerySuccessful: Array.isArray(testQuery),
      };

      const healthy = Object.values(healthDetails).every(v => v === true || typeof v === 'string');

      return {
        healthy,
        details: healthDetails,
      };
    } catch (error) {
      return {
        healthy: false,
        details: {
          error: error instanceof Error ? error.message : String(error),
          lastCheck: new Date().toISOString(),
        },
      };
    }
  }

  private isConfigValid(): boolean {
    const { database } = this.config;
    
    return (
      typeof database.enableTransactions === 'boolean' &&
      typeof database.batchInsertSize === 'number' && database.batchInsertSize > 0 &&
      typeof database.connectionPoolSize === 'number' && database.connectionPoolSize > 0 &&
      typeof database.queryTimeout === 'number' && database.queryTimeout > 0
    );
  }

  private async findExistingResource(documentId: string) {
    try {
      const results = await db
        .select()
        .from(resources)
        .where(eq(resources.sourceDocumentId, documentId))
        .limit(1);

      return results.length > 0 ? results[0] : null;
    } catch (error) {
      Logger.error("Error finding existing resource", {
        documentId,
        error: error instanceof Error ? error.message : String(error),
      });
      throw error;
    }
  }

  private async createResource(
    documentId: string,
    markdown: string,
    metadata?: MarkdownStorageInput['metadata']
  ): Promise<string> {
    try {
      const resourceData = {
        id: this.generateResourceId(),
        sourceDocumentId: documentId,
        content: markdown,
        contentType: 'text/markdown' as const,
        metadata: metadata ? JSON.stringify(metadata) : null,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      await db.insert(resources).values(resourceData);
      
      return resourceData.id;
    } catch (error) {
      Logger.error("Error creating resource", {
        documentId,
        error: error instanceof Error ? error.message : String(error),
      });
      throw error;
    }
  }

  private async updateResource(
    resourceId: string,
    markdown: string,
    metadata?: MarkdownStorageInput['metadata']
  ): Promise<void> {
    try {
      const updateData = {
        content: markdown,
        metadata: metadata ? JSON.stringify(metadata) : null,
        updatedAt: new Date(),
      };

      await db
        .update(resources)
        .set(updateData)
        .where(eq(resources.id, resourceId));
        
    } catch (error) {
      Logger.error("Error updating resource", {
        resourceId,
        error: error instanceof Error ? error.message : String(error),
      });
      throw error;
    }
  }

  private generateResourceId(): string {
    return `resource_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private calculateCompressionRatio(markdown: string): number {
    // Simple compression ratio calculation
    // In a real implementation, you might use actual compression
    const originalSize = Buffer.byteLength(markdown, 'utf8');
    const compressedSize = Buffer.byteLength(markdown.replace(/\s+/g, ' '), 'utf8');
    return Math.round((1 - compressedSize / originalSize) * 100) / 100;
  }

  /**
   * Get module configuration for debugging
   */
  getConfig(): PipelineConfig['database'] {
    return this.config.database;
  }

  /**
   * Update module configuration (for runtime adjustments)
   */
  updateConfig(updates: Partial<PipelineConfig['database']>): void {
    Object.assign(this.config.database, updates);
    Logger.info("Markdown storage module configuration updated", {
      updates,
      newConfig: this.config.database,
    });
  }
}
