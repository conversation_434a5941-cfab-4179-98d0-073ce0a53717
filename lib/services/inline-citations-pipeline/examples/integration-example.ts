/**
 * Integration Example - How to use the modular pipeline in existing code
 * 
 * This example shows how to replace existing inline citations code
 * with the new modular pipeline while preserving functionality.
 */

import { Logger } from "../../../utils/Logger";
import { InlineCitationsPipeline, createPipeline, createDevPipeline } from "../index";

/**
 * Example 1: Document Processing Integration
 * Replace existing DocumentMarkdownProcessor usage
 */
export class ModularDocumentProcessor {
  private pipeline: InlineCitationsPipeline;

  constructor() {
    // Create pipeline with custom configuration
    this.pipeline = createPipeline({
      pdf: {
        scaleFactor: 1.5,
        maxPages: 50,
        saveDebugImages: process.env.NODE_ENV === 'development',
      },
      ocr: {
        model: 'gemini-2.0-flash-exp',
        saveDebugMarkdown: process.env.NODE_ENV === 'development',
      },
      embedding: {
        provider: 'openai',
        model: 'text-embedding-3-small',
        outputDimensionality: 1024,
      },
    });

    // Add event monitoring
    this.pipeline.addEventListener((event) => {
      Logger.info(`Pipeline event: ${event.type}`, {
        stage: event.stage,
        contextId: event.contextId,
        timestamp: event.timestamp,
      });
    });
  }

  /**
   * Process document - replaces DocumentMarkdownProcessor.processDocument
   */
  async processDocument(
    documentId: string,
    url: string,
    contentType: string
  ) {
    try {
      Logger.info("Starting modular document processing", {
        documentId,
        url,
        contentType,
      });

      const result = await this.pipeline.processDocument({
        documentId,
        url,
        contentType,
        userId: 'system', // Could be passed from context
        sessionId: `session_${Date.now()}`,
      });

      if (result.success) {
        Logger.info("Document processing completed successfully", {
          documentId,
          processingTime: result.performance?.duration,
          stagesExecuted: result.metadata?.stagesExecuted,
        });

        return {
          success: true,
          documentId,
          markdown: result.data.markdown,
          pagesProcessed: result.data.pagesProcessed || result.data.totalPages,
          processingTime: result.performance?.duration,
        };
      } else {
        throw new Error(result.error || "Document processing failed");
      }

    } catch (error) {
      Logger.error("Modular document processing failed", {
        documentId,
        error: error instanceof Error ? error.message : String(error),
      });
      throw error;
    }
  }

  /**
   * Process only PDF to images - for cases where you only need image extraction
   */
  async processPdfToImages(documentId: string, url: string) {
    const result = await this.pipeline.processPdf({
      documentId,
      url,
      contentType: 'application/pdf',
    });

    if (result.success) {
      return {
        success: true,
        pages: result.data.pages,
        totalPages: result.data.totalPages,
      };
    } else {
      throw new Error(result.error || "PDF processing failed");
    }
  }

  /**
   * Get processing status - useful for long-running operations
   */
  async getProcessingStatus(requestId: string) {
    return await this.pipeline.getExecutionStatus(requestId);
  }
}

/**
 * Example 2: Citation Generation Integration
 * Replace existing InlineCitationsGenerator usage
 */
export class ModularCitationGenerator {
  private pipeline: InlineCitationsPipeline;

  constructor() {
    this.pipeline = createPipeline({
      trigger: {
        minDocuments: 1,
        minMessageLength: 50,
        requireRecentDocuments: true,
        recentDocumentHours: 24,
      },
      generation: {
        maxSimilarityResults: 20,
        similarityThreshold: 0.7,
        maxCitationsPerMessage: 10,
        enableContextualRanking: true,
        citationFormat: 'superscript',
      },
    });
  }

  /**
   * Generate citations - replaces InlineCitationsGenerator.generateInlineCitations
   */
  async generateInlineCitations(
    messageId: string,
    chatId: string,
    content: string,
    messages: any[] = [],
    chatExists: boolean = true,
    isReadonly: boolean = false
  ) {
    try {
      Logger.info("Starting modular citation generation", {
        messageId,
        chatId,
        contentLength: content.length,
      });

      const result = await this.pipeline.generateCitations({
        messageId,
        chatId,
        content,
        messages,
        chatExists,
        isReadonly,
      });

      if (result.success) {
        Logger.info("Citation generation completed successfully", {
          messageId,
          citationCount: result.data.citations.length,
          processingTime: result.performance?.duration,
        });

        return {
          success: true,
          originalContent: result.data.originalContent,
          citedContent: result.data.citedContent,
          citations: result.data.citations,
          referencesSection: result.data.referencesSection,
          metadata: result.data.metadata,
        };
      } else {
        return {
          success: false,
          error: result.error,
          originalContent: content,
          citedContent: content,
          citations: [],
        };
      }

    } catch (error) {
      Logger.error("Modular citation generation failed", {
        messageId,
        chatId,
        error: error instanceof Error ? error.message : String(error),
      });

      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
        originalContent: content,
        citedContent: content,
        citations: [],
      };
    }
  }

  /**
   * Evaluate citation trigger - useful for pre-checking before generation
   */
  async shouldGenerateCitations(
    messageId: string,
    chatId: string,
    messages: any[],
    chatExists: boolean,
    isReadonly: boolean,
    content?: string
  ) {
    const result = await this.pipeline.evaluateCitationTrigger({
      messageId,
      chatId,
      messages,
      chatExists,
      isReadonly,
      content,
    });

    if (result.success) {
      return {
        shouldTrigger: result.data.shouldTrigger,
        reason: result.data.reason,
        confidence: result.data.confidence,
        conditions: result.data.conditions,
      };
    } else {
      return {
        shouldTrigger: false,
        reason: result.error || "Trigger evaluation failed",
        confidence: 0,
        conditions: {},
      };
    }
  }
}

/**
 * Example 3: Development and Testing Setup
 */
export class PipelineTester {
  private devPipeline: InlineCitationsPipeline;

  constructor() {
    // Use development-optimized pipeline
    this.devPipeline = createDevPipeline();

    // Add comprehensive event logging for testing
    this.devPipeline.addEventListener((event) => {
      console.log(`[${event.type}] ${event.stage || 'pipeline'}`, {
        contextId: event.contextId,
        timestamp: new Date(event.timestamp).toISOString(),
        data: event.data,
        error: event.error,
      });
    });
  }

  /**
   * Test document processing with detailed logging
   */
  async testDocumentProcessing(documentId: string, url: string) {
    console.log("🧪 Testing document processing...");
    
    const startTime = Date.now();
    const result = await this.devPipeline.processDocument({
      documentId,
      url,
      contentType: 'application/pdf',
    });

    const duration = Date.now() - startTime;
    
    console.log("📊 Test Results:", {
      success: result.success,
      duration: `${duration}ms`,
      stages: result.metadata?.stagesExecuted,
      error: result.error,
    });

    if (result.success) {
      console.log("✅ Document processing test passed");
      console.log("📄 Processed data:", {
        pagesProcessed: result.data.totalPages,
        markdownLength: result.data.markdown?.length,
        chunksGenerated: result.data.chunks?.length,
        embeddingsCreated: result.data.totalEmbeddings,
      });
    } else {
      console.log("❌ Document processing test failed:", result.error);
    }

    return result;
  }

  /**
   * Test citation generation with mock data
   */
  async testCitationGeneration() {
    console.log("🧪 Testing citation generation...");

    const mockInput = {
      messageId: `test_msg_${Date.now()}`,
      chatId: `test_chat_${Date.now()}`,
      content: "This is a test message that should trigger citations based on uploaded documents.",
      messages: [
        { role: 'user', content: 'Previous message' },
        { role: 'assistant', content: 'Previous response' },
      ],
      chatExists: true,
      isReadonly: false,
    };

    const result = await this.devPipeline.generateCitations(mockInput);

    console.log("📊 Citation Test Results:", {
      success: result.success,
      shouldTrigger: result.data?.shouldTrigger,
      citationCount: result.data?.citations?.length || 0,
      error: result.error,
    });

    return result;
  }

  /**
   * Test individual pipeline stages
   */
  async testIndividualStages() {
    console.log("🧪 Testing individual pipeline stages...");

    // Test health status
    const health = await this.devPipeline.getHealthStatus();
    console.log("🏥 Health Status:", health);

    // Test configuration
    const config = this.devPipeline.getConfig();
    console.log("⚙️ Configuration:", {
      pdfProvider: "server-side-pdfjs",
      ocrModel: config.ocr.model,
      embeddingProvider: config.embedding.provider,
      embeddingModel: config.embedding.model,
    });

    // Test pipeline info
    const info = this.devPipeline.getInfo();
    console.log("ℹ️ Pipeline Info:", {
      version: info.version,
      moduleCount: info.modules.length,
      modules: info.modules.map(m => `${m.name}@${m.version}`),
    });
  }
}

/**
 * Example 4: Enhanced Doc Reader Integration Helper
 * Demonstrates the Enhanced Doc Reader-only approach without legacy fallbacks
 */
export class EnhancedDocReaderIntegrationHelper {
  private pipeline: InlineCitationsPipeline;

  constructor() {
    this.pipeline = createPipeline();
  }

  /**
   * Process document using Enhanced Doc Reader image extraction only
   * No fallbacks - relies entirely on Enhanced Doc Reader mechanism
   */
  async processDocumentWithEnhancedDocReader(
    documentId: string,
    url: string,
    contentType: string
  ) {
    Logger.info("Processing document with Enhanced Doc Reader integration only", {
      documentId,
      extractionMethod: 'enhanced-doc-reader-only',
      fallbackEnabled: false,
    });

    const result = await this.pipeline.processDocument({
      documentId,
      url,
      contentType,
    });

    if (result.success) {
      Logger.info("Enhanced Doc Reader pipeline succeeded", {
        documentId,
        extractionMethod: 'enhanced-doc-reader-compatible',
        pagesProcessed: result.data.pages?.length || 0,
      });
      return result.data;
    } else {
      Logger.error("Enhanced Doc Reader pipeline failed - no fallback available", {
        documentId,
        error: result.error,
        extractionMethod: 'enhanced-doc-reader-only',
      });
      throw new Error(`Enhanced Doc Reader processing failed: ${result.error}`);
    }
  }

  /**
   * Generate citations using Enhanced Doc Reader processed documents only
   */
  async generateCitationsWithEnhancedDocReader(
    messageId: string,
    chatId: string,
    content: string
  ) {
    Logger.info("Generating citations with Enhanced Doc Reader processed documents", {
      messageId,
      extractionMethod: 'enhanced-doc-reader-only',
    });

    const result = await this.pipeline.generateCitations({
      messageId,
      chatId,
      content,
    });

    if (result.success) {
      Logger.info("Enhanced Doc Reader citation generation succeeded", {
        messageId,
        citationCount: result.data.citations?.length || 0,
      });
      return result.data;
    } else {
      Logger.error("Enhanced Doc Reader citation generation failed - no fallback available", {
        messageId,
        error: result.error,
      });
      throw new Error(`Enhanced Doc Reader citation generation failed: ${result.error}`);
    }
  }

  /**
   * Get processing status and verify Enhanced Doc Reader integration
   */
  async getProcessingStatusWithVerification(requestId: string) {
    const status = await this.pipeline.getExecutionStatus(requestId);

    if (!status) {
      Logger.warn("No processing status found for request", {
        requestId,
        extractionMethod: 'enhanced-doc-reader-verified',
      });

      return {
        requestId,
        extractionMethod: 'enhanced-doc-reader-only',
        fallbackEnabled: false,
        compatibilityMode: 'enhanced-doc-reader-identical',
        status: null,
      };
    }

    Logger.info("Processing status with Enhanced Doc Reader verification", {
      requestId,
      status: status.stages,
      extractionMethod: 'enhanced-doc-reader-verified',
    });

    return {
      ...status,
      extractionMethod: 'enhanced-doc-reader-only',
      fallbackEnabled: false,
      compatibilityMode: 'enhanced-doc-reader-identical',
    };
  }
}
