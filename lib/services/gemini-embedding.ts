import { GoogleGenerativeA<PERSON> } from "@google/generative-ai";
import { Logger } from "../utils/Logger";
import { db } from "../db";
import { resources, embeddings } from "../db/schema";
import { InlineCitationsChunk } from "./inline-citations-chunker";
import { getGooglePalmApiKey } from "../utils/apiKeyRotation";

// Configuration for Gemini embedding
const GEMINI_EMBEDDING_CONFIG = {
  MODEL: "gemini-embedding-exp-03-07",
  OUTPUT_DIMENSIONALITY: 1024,
  BATCH_SIZE: 10, // Process chunks in batches to avoid rate limits
  MAX_RETRIES: 3,
  RETRY_DELAY: 1000, // 1 second
} as const;

export interface EmbeddingResult {
  success: boolean;
  processedChunks: number;
  totalEmbeddings: number;
  averageVectorLength: number;
  error?: string;
}

export class GeminiEmbeddingService {
  /**
   * Gets a fresh Google Generative AI client with a rotated API key
   */
  private static getClient(): GoogleGenerativeAI {
    return new GoogleGenerativeAI(getGooglePalmApiKey());
  }

  /**
   * Main entry point for generating embeddings and storing chunks
   * @param chunks Array of inline citations chunks to process
   * @returns Embedding result with statistics
   */
  static async generateAndStoreEmbeddings(
    chunks: InlineCitationsChunk[]
  ): Promise<EmbeddingResult> {
    try {
      Logger.info("Starting Gemini embedding generation for inline citations", {
        totalChunks: chunks.length,
        model: GEMINI_EMBEDDING_CONFIG.MODEL,
        outputDimensionality: GEMINI_EMBEDDING_CONFIG.OUTPUT_DIMENSIONALITY,
      });

      if (chunks.length === 0) {
        return {
          success: true,
          processedChunks: 0,
          totalEmbeddings: 0,
          averageVectorLength: 0,
        };
      }

      // Process chunks in batches
      const batches = this.createBatches(chunks);
      let totalProcessed = 0;
      let totalVectorLength = 0;
      let totalEmbeddings = 0;

      for (let batchIndex = 0; batchIndex < batches.length; batchIndex++) {
        const batch = batches[batchIndex];

        Logger.debug("Processing embedding batch", {
          batchIndex: batchIndex + 1,
          totalBatches: batches.length,
          batchSize: batch.length,
        });

        const batchResult = await this.processBatch(batch, batchIndex);

        totalProcessed += batchResult.processedChunks;
        totalVectorLength += batchResult.totalVectorLength;
        totalEmbeddings += batchResult.totalEmbeddings;
      }

      const averageVectorLength = totalEmbeddings > 0 ? totalVectorLength / totalEmbeddings : 0;

      Logger.info("Gemini embedding generation completed", {
        processedChunks: totalProcessed,
        totalEmbeddings,
        averageVectorLength: Math.round(averageVectorLength),
        expectedDimensionality: GEMINI_EMBEDDING_CONFIG.OUTPUT_DIMENSIONALITY,
      });

      return {
        success: true,
        processedChunks: totalProcessed,
        totalEmbeddings,
        averageVectorLength: Math.round(averageVectorLength),
      };
    } catch (error) {
      Logger.error("Error in Gemini embedding generation", { error });
      return {
        success: false,
        processedChunks: 0,
        totalEmbeddings: 0,
        averageVectorLength: 0,
        error: error instanceof Error ? error.message : "Unknown error",
      };
    }
  }

  /**
   * Creates batches of chunks for processing
   */
  private static createBatches(chunks: InlineCitationsChunk[]): InlineCitationsChunk[][] {
    const batches: InlineCitationsChunk[][] = [];
    for (let i = 0; i < chunks.length; i += GEMINI_EMBEDDING_CONFIG.BATCH_SIZE) {
      batches.push(chunks.slice(i, i + GEMINI_EMBEDDING_CONFIG.BATCH_SIZE));
    }
    return batches;
  }

  /**
   * Processes a batch of chunks with retry logic
   */
  private static async processBatch(
    batch: InlineCitationsChunk[],
    batchIndex: number
  ): Promise<{
    processedChunks: number;
    totalVectorLength: number;
    totalEmbeddings: number;
  }> {
    let retries = 0;
    let lastError: Error | null = null;

    while (retries < GEMINI_EMBEDDING_CONFIG.MAX_RETRIES) {
      try {
        return await this.processBatchInternal(batch, batchIndex);
      } catch (error) {
        lastError = error instanceof Error ? error : new Error("Unknown error");
        retries++;

        if (retries < GEMINI_EMBEDDING_CONFIG.MAX_RETRIES) {
          Logger.warn("Batch processing failed, retrying", {
            batchIndex,
            retries,
            error: lastError.message,
          });
          await new Promise(resolve =>
            setTimeout(resolve, GEMINI_EMBEDDING_CONFIG.RETRY_DELAY * retries)
          );
        }
      }
    }

    Logger.error("Batch processing failed after all retries", {
      batchIndex,
      retries,
      error: lastError?.message,
    });
    throw lastError || new Error("Batch processing failed");
  }

  /**
   * Internal method to process a batch of chunks
   */
  private static async processBatchInternal(
    batch: InlineCitationsChunk[],
    batchIndex: number
  ): Promise<{
    processedChunks: number;
    totalVectorLength: number;
    totalEmbeddings: number;
  }> {
    let totalVectorLength = 0;
    let totalEmbeddings = 0;

    // Process each chunk in the batch
    for (let i = 0; i < batch.length; i++) {
      const chunk = batch[i];

      try {
        // Generate embedding using Gemini REST API with proper output dimensionality
        const embeddingResult = await this.generateEmbeddingWithDimensionality(chunk.text);
        const embedding = embeddingResult.embedding.values;

        if (!embedding || !Array.isArray(embedding)) {
          throw new Error("Invalid embedding response from Gemini");
        }

        // Validate embedding dimensions
        if (embedding.length !== GEMINI_EMBEDDING_CONFIG.OUTPUT_DIMENSIONALITY) {
          Logger.warn("Unexpected embedding dimension", {
            expected: GEMINI_EMBEDDING_CONFIG.OUTPUT_DIMENSIONALITY,
            actual: embedding.length,
            chunkId: chunk.id,
          });
        }

        // Store in database
        await this.storeChunkWithEmbedding(chunk, embedding);

        totalVectorLength += embedding.length;
        totalEmbeddings++;

        Logger.debug("Chunk embedding generated and stored", {
          chunkId: chunk.id,
          vectorLength: embedding.length,
          tokenCount: chunk.metadata.token_count,
          batchIndex,
          chunkIndex: i,
        });

      } catch (error) {
        Logger.error("Error processing chunk in batch", {
          chunkId: chunk.id,
          batchIndex,
          chunkIndex: i,
          error,
        });
        throw error;
      }
    }

    return {
      processedChunks: batch.length,
      totalVectorLength,
      totalEmbeddings,
    };
  }

  /**
   * Stores a chunk with its embedding in the database
   */
  private static async storeChunkWithEmbedding(
    chunk: InlineCitationsChunk,
    embedding: number[]
  ): Promise<void> {
    try {
      await db.transaction(async (tx) => {
        // Insert the resource (chunk content) first
        const [resourceRow] = await tx
          .insert(resources)
          .values({
            content: chunk.text,
            sourceDocumentId: chunk.metadata.document_id,
          })
          .returning();

        // Then insert the embedding
        await tx.insert(embeddings).values({
          resourceId: resourceRow.id,
          content: chunk.text,
          embedding: embedding,
        });

        Logger.debug("Chunk and embedding stored successfully", {
          chunkId: chunk.id,
          resourceId: resourceRow.id,
          embeddingLength: embedding.length,
          metadata: chunk.metadata,
        });
      });
    } catch (error) {
      Logger.error("Error storing chunk with embedding", {
        chunkId: chunk.id,
        error,
      });
      throw error;
    }
  }

  /**
   * Generates embedding with proper dimensionality using REST API
   */
  private static async generateEmbeddingWithDimensionality(text: string): Promise<{ embedding: { values: number[] } }> {
    try {
      const apiKey = getGooglePalmApiKey();
      const url = `https://generativelanguage.googleapis.com/v1beta/models/${GEMINI_EMBEDDING_CONFIG.MODEL}:embedContent?key=${apiKey}`;

      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          content: {
            parts: [{ text }]
          },
          taskType: "RETRIEVAL_DOCUMENT",
          outputDimensionality: GEMINI_EMBEDDING_CONFIG.OUTPUT_DIMENSIONALITY
        })
      });

      if (!response.ok) {
        throw new Error(`Gemini API error: ${response.status} ${response.statusText}`);
      }

      const result = await response.json();

      if (!result.embedding || !result.embedding.values) {
        throw new Error("Invalid embedding response from Gemini API");
      }

      return result;
    } catch (error) {
      Logger.error("Error generating embedding with dimensionality", { error, textLength: text.length });
      throw error;
    }
  }

  /**
   * Generates a single embedding for a query (for search purposes)
   */
  static async generateQueryEmbedding(query: string): Promise<number[]> {
    try {
      // Use the same method with proper dimensionality
      const embeddingResult = await this.generateEmbeddingWithDimensionality(query);
      const embedding = embeddingResult.embedding.values;

      if (!embedding || !Array.isArray(embedding)) {
        throw new Error("Invalid embedding response from Gemini");
      }

      Logger.debug("Query embedding generated", {
        queryLength: query.length,
        embeddingLength: embedding.length,
      });

      return embedding;
    } catch (error) {
      Logger.error("Error generating query embedding", { error, query: query.substring(0, 100) });
      throw error;
    }
  }
}
