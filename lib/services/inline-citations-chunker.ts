import { Logger } from "../utils/Logger";
import { countTokens } from "./llmService";
import { InlineCitationsPerformance, OPERATION_TYPES } from "./inline-citations-performance";

// Configuration for inline citations chunking - Less aggressive approach
// Configuration for inline citations chunking - Less aggressive strategy
const INLINE_CITATIONS_CONFIG = {
  MAX_TOKENS: 2048, // Increased from 1024 for larger, less aggressive chunks
  OVERLAP_TOKENS: 128,
  MIN_CHUNK_SIZE: 200, // Increased from 100 to avoid overly small chunks
  MAX_CHUNK_SIZE: 2048, // Keep as maximum limit
  SOURCE: "ocr_markdown",
  // Semantic chunking improvements - reduced paragraph focus
  SENTENCE_BOUNDARY_WEIGHT: 0.8, // Prefer breaking at sentence boundaries
  MIN_OVERLAP_SENTENCES: 1, // Minimum sentences to include in overlap
} as const;

export interface InlineCitationsChunk {
  id: string;
  text: string;
  metadata: {
    chunk_id: string;
    page_number: number;
    source: string;
    token_count: number;
    chunk_index: number;
    document_id: string;
  };
}

export interface ChunkingResult {
  success: boolean;
  chunks: InlineCitationsChunk[];
  totalChunks: number;
  totalTokens: number;
  averageChunkSize: number;
  error?: string;
}

export class InlineCitationsChunker {
  /**
   * Main entry point for chunking markdown content for inline citations
   * @param markdown The markdown content to chunk
   * @param documentId The source document ID
   * @returns Chunking result with chunks and metadata
   */
  static async chunkMarkdownForInlineCitations(
    markdown: string,
    documentId: string
  ): Promise<ChunkingResult> {
    const operationId = `chunking_${documentId}_${Date.now()}`;
    const timer = InlineCitationsPerformance.startTimer(
      operationId,
      OPERATION_TYPES.CHUNKING,
      {
        documentId,
        markdownLength: markdown.length,
        estimatedTokens: countTokens(markdown),
      }
    );

    try {
      Logger.info("DEBUG: Starting inline citations markdown chunking", {
        documentId,
        markdownLength: markdown.length,
        estimatedTokens: countTokens(markdown),
        markdownPreview: markdown.substring(0, 200),
      });

      // Check for empty or invalid markdown
      if (!markdown || markdown.trim().length === 0) {
        Logger.error("DEBUG: Empty markdown content provided for chunking", {
          documentId,
          markdownLength: markdown?.length || 0,
        });
        timer.end();
        return {
          success: false,
          chunks: [],
          totalChunks: 0,
          totalTokens: 0,
          averageChunkSize: 0,
          error: "Empty markdown content",
        };
      }

      // Use optimized semantic chunking for better performance and quality
      Logger.info("DEBUG: Attempting semantic chunking", { documentId });
      let chunks = this.chunkBySemanticsOptimized(markdown, documentId);
      Logger.info("DEBUG: Semantic chunking completed", {
        documentId,
        chunkCount: chunks.length,
        usable: this.areChunksUsable(chunks),
      });

      // If semantic chunking fails, fall back to existing approach only
      if (!this.areChunksUsable(chunks)) {
        Logger.info("DEBUG: Semantic chunking not usable, trying existing approach", {
          documentId,
          semanticChunkCount: chunks.length,
          sampleChunks: chunks.slice(0, 2).map(c => ({
            id: c.id,
            textLength: c.text.length,
            tokenCount: c.metadata.token_count,
            textPreview: c.text.substring(0, 100),
          })),
        });
        chunks = this.chunkBySections(markdown, documentId);
        Logger.info("DEBUG: Existing approach completed", {
          documentId,
          chunkCount: chunks.length,
          usable: this.areChunksUsable(chunks),
        });

        // If existing approach also fails, use the best chunks we have or create minimal chunks
        if (!this.areChunksUsable(chunks)) {
          Logger.warn("DEBUG: Both semantic and existing chunking approaches failed, creating minimal chunks", {
            documentId,
            chunkCount: chunks.length,
          });
          chunks = this.createMinimalChunks(markdown, documentId);
        }
      }

      // Filter chunks by size
      const filteredChunks = this.filterChunksBySize(chunks);

      // Calculate statistics
      const stats = this.calculateChunkingStats(filteredChunks);

      // Log results
      this.logChunkingResults(documentId, filteredChunks, stats);

      const performanceResult = timer.end();

      Logger.info("Chunking completed", {
        documentId,
        chunkCount: filteredChunks.length,
        processingTime: `${performanceResult.duration.toFixed(2)}ms`,
      });

      return {
        success: true,
        chunks: filteredChunks,
        totalChunks: filteredChunks.length,
        totalTokens: stats.totalTokens,
        averageChunkSize: stats.averageChunkSize,
      };
    } catch (error) {
      timer.end();
      Logger.error("Error in inline citations markdown chunking", {
        documentId,
        error,
      });

      return {
        success: false,
        chunks: [],
        totalChunks: 0,
        totalTokens: 0,
        averageChunkSize: 0,
        error: error instanceof Error ? error.message : "Unknown error",
      };
    }
  }

  /**
   * Less aggressive semantic chunking that creates larger, more coherent chunks
   */
  private static chunkBySemanticsOptimized(
    markdown: string,
    documentId: string
  ): InlineCitationsChunk[] {
    const chunks: InlineCitationsChunk[] = [];
    let chunkIndex = 0;

    // Pre-process markdown to identify semantic boundaries (less aggressive)
    const sections = this.identifySemanticSections(markdown);

    let currentChunk = "";
    let currentTokens = 0;

    for (const section of sections) {
      const sectionTokens = countTokens(section.content);

      // Try to fit more content in each chunk - be less aggressive about splitting
      if (currentTokens + sectionTokens <= INLINE_CITATIONS_CONFIG.MAX_TOKENS) {
        // Use single newline for better content flow, double newline only for major breaks
        const separator = section.type === "heading_section" ? "\n\n" : "\n";
        currentChunk += (currentChunk ? separator : "") + section.content;
        currentTokens += sectionTokens;
      } else {
        // Finalize current chunk if it has content
        if (currentChunk.trim()) {
          this.addOptimizedChunk(chunks, currentChunk, documentId, chunkIndex);
          chunkIndex++;
        }

        // Start new chunk with current section
        if (sectionTokens <= INLINE_CITATIONS_CONFIG.MAX_TOKENS) {
          currentChunk = section.content;
          currentTokens = sectionTokens;
        } else {
          // Section is too large, split it more conservatively
          const subChunks = this.splitLargeSectionConservatively(section.content, documentId, chunkIndex);
          chunks.push(...subChunks);
          chunkIndex += subChunks.length;
          currentChunk = "";
          currentTokens = 0;
        }
      }
    }

    // Add final chunk
    if (currentChunk.trim()) {
      this.addOptimizedChunk(chunks, currentChunk, documentId, chunkIndex);
    }

    return chunks;
  }

  /**
   * Identifies semantic sections in markdown content with less aggressive paragraph splitting
   */
  private static identifySemanticSections(markdown: string): Array<{ content: string; type: string }> {
    const sections: Array<{ content: string; type: string }> = [];

    // Use less aggressive splitting - look for major structural boundaries
    // Split by headings first, then by multiple newlines for larger sections
    const majorSections = markdown.split(/(?=^#{1,6}\s)/m).filter(s => s.trim());

    for (const section of majorSections) {
      const trimmed = section.trim();
      if (!trimmed) continue;

      // For sections with headings, keep them as larger units
      if (trimmed.startsWith("#")) {
        sections.push({ content: trimmed, type: "heading_section" });
      } else {
        // For non-heading sections, split more conservatively by triple newlines or more
        const subsections = trimmed.split(/\n\s*\n\s*\n/).filter(s => s.trim());

        for (const subsection of subsections) {
          const subTrimmed = subsection.trim();
          if (!subTrimmed) continue;

          // Identify section type for better chunking decisions
          let type = "content";
          if (subTrimmed.startsWith("```") || subTrimmed.includes("```")) type = "code";
          else if (subTrimmed.startsWith("- ") || subTrimmed.startsWith("* ") || /^\d+\./.test(subTrimmed)) type = "list";
          else if (subTrimmed.startsWith(">")) type = "quote";

          sections.push({ content: subTrimmed, type });
        }
      }
    }

    return sections;
  }

  /**
   * Splits large sections more conservatively, trying to maintain larger coherent chunks
   */
  private static splitLargeSectionConservatively(
    content: string,
    documentId: string,
    startIndex: number
  ): InlineCitationsChunk[] {
    const chunks: InlineCitationsChunk[] = [];

    // First try to split by double newlines (paragraph breaks) for larger sections
    let sections = content.split(/\n\s*\n/).filter(s => s.trim());

    // If that doesn't help much, fall back to sentence splitting
    if (sections.length <= 2) {
      sections = content.split(/(?<=[.!?])\s+/).filter(s => s.trim());
    }

    let currentChunk = "";
    let currentTokens = 0;
    let chunkIndex = startIndex;

    for (const section of sections) {
      const sectionTokens = countTokens(section);

      if (currentTokens + sectionTokens <= INLINE_CITATIONS_CONFIG.MAX_TOKENS) {
        currentChunk += (currentChunk ? "\n" : "") + section;
        currentTokens += sectionTokens;
      } else {
        if (currentChunk.trim()) {
          this.addOptimizedChunk(chunks, currentChunk, documentId, chunkIndex);
          chunkIndex++;
        }
        currentChunk = section;
        currentTokens = sectionTokens;
      }
    }

    if (currentChunk.trim()) {
      this.addOptimizedChunk(chunks, currentChunk, documentId, chunkIndex);
    }

    return chunks;
  }

  /**
   * Adds an optimized chunk with better metadata
   */
  private static addOptimizedChunk(
    chunks: InlineCitationsChunk[],
    content: string,
    documentId: string,
    chunkIndex: number
  ): void {
    const trimmed = content.trim();
    if (!trimmed) return;

    const tokenCount = countTokens(trimmed);
    const chunkId = `${documentId}_semantic_chunk_${chunkIndex}`;

    chunks.push({
      id: chunkId,
      text: trimmed,
      metadata: {
        chunk_id: chunkId,
        page_number: this.estimatePageNumber(chunkIndex, chunks.length + 1),
        source: INLINE_CITATIONS_CONFIG.SOURCE,
        token_count: tokenCount,
        chunk_index: chunkIndex,
        document_id: documentId,
      },
    });
  }

  /**
   * Simple section-based chunking that preserves larger content blocks
   * Less aggressive existing chunking approach adapted for larger chunks
   */
  private static chunkBySections(
    markdown: string,
    documentId: string
  ): InlineCitationsChunk[] {
    Logger.debug("Using simple section-based chunking", { documentId });

    // Split by larger sections first (triple newlines), then paragraphs as fallback
    let sections = markdown.split(/\n\s*\n\s*\n/).filter(s => s.trim());
    if (sections.length <= 2) {
      sections = markdown.split(/\n\s*\n/).filter(s => s.trim());
    }

    const chunks: InlineCitationsChunk[] = [];
    let chunkIndex = 0;
    let currentChunk = "";

    for (const section of sections) {
      const sectionTokens = countTokens(section);
      const currentTokens = countTokens(currentChunk);

      // If adding this section would exceed max tokens, finalize current chunk
      if (currentTokens + sectionTokens > INLINE_CITATIONS_CONFIG.MAX_TOKENS && currentChunk.trim()) {
        this.addOptimizedChunk(chunks, currentChunk, documentId, chunkIndex);
        chunkIndex++;
        currentChunk = section;
      } else {
        // Add section to current chunk
        currentChunk = currentChunk ? `${currentChunk}\n\n${section}` : section;
      }
    }

    // Add final chunk if it has content
    if (currentChunk.trim()) {
      this.addOptimizedChunk(chunks, currentChunk, documentId, chunkIndex);
    }

    return chunks;
  }

  /**
   * Creates minimal chunks when other approaches fail
   * Ensures we always have some chunks to work with
   */
  private static createMinimalChunks(
    markdown: string,
    documentId: string
  ): InlineCitationsChunk[] {
    Logger.debug("Creating minimal chunks as fallback", { documentId });

    const chunks: InlineCitationsChunk[] = [];
    const words = markdown.split(/\s+/);
    let chunkIndex = 0;
    let currentChunk: string[] = [];

    for (const word of words) {
      currentChunk.push(word);

      // Check if we've reached a reasonable chunk size
      if (currentChunk.length >= INLINE_CITATIONS_CONFIG.MIN_CHUNK_SIZE) {
        const chunkText = currentChunk.join(' ');
        const tokenCount = countTokens(chunkText);

        if (tokenCount >= INLINE_CITATIONS_CONFIG.MIN_CHUNK_SIZE) {
          this.addOptimizedChunk(chunks, chunkText, documentId, chunkIndex);
          chunkIndex++;
          currentChunk = [];
        }
      }
    }

    // Add remaining words as final chunk if any
    if (currentChunk.length > 0) {
      const chunkText = currentChunk.join(' ');
      this.addOptimizedChunk(chunks, chunkText, documentId, chunkIndex);
    }

    return chunks;
  }

  /**
   * Checks if the generated chunks are usable based on size and quality criteria
   * Less strict validation for larger, less aggressive chunks
   */
  private static areChunksUsable(chunks: InlineCitationsChunk[]): boolean {
    if (chunks.length === 0) return false;

    // Check if most chunks are within reasonable size bounds
    const validChunks = chunks.filter(chunk =>
      chunk.metadata.token_count >= INLINE_CITATIONS_CONFIG.MIN_CHUNK_SIZE &&
      chunk.metadata.token_count <= INLINE_CITATIONS_CONFIG.MAX_CHUNK_SIZE
    );

    const validRatio = validChunks.length / chunks.length;
    // Reduced threshold from 70% to 50% to be less strict with larger chunks
    return validRatio >= 0.5 && chunks.length > 0;
  }





  /**
   * Estimates page number based on chunk index (rough approximation)
   */
  private static estimatePageNumber(chunkIndex: number, totalChunks: number): number {
    // Rough estimation: assume 3-5 chunks per page
    const chunksPerPage = 4;
    return Math.floor(chunkIndex / chunksPerPage) + 1;
  }

  /**
   * Filters chunks by size to remove overly small or large chunks
   */
  private static filterChunksBySize(chunks: InlineCitationsChunk[]): InlineCitationsChunk[] {
    const filtered = chunks.filter(chunk => {
      const tokenCount = chunk.metadata.token_count;
      return tokenCount >= INLINE_CITATIONS_CONFIG.MIN_CHUNK_SIZE &&
             tokenCount <= INLINE_CITATIONS_CONFIG.MAX_CHUNK_SIZE;
    });

    const removedCount = chunks.length - filtered.length;
    if (removedCount > 0) {
      Logger.info("Filtered out chunks by size", {
        originalCount: chunks.length,
        filteredCount: filtered.length,
        removedCount,
      });
    }

    return filtered;
  }

  /**
   * Calculates statistics about the chunking results
   */
  private static calculateChunkingStats(chunks: InlineCitationsChunk[]) {
    const totalTokens = chunks.reduce((sum, chunk) => sum + chunk.metadata.token_count, 0);
    const averageChunkSize = chunks.length > 0 ? totalTokens / chunks.length : 0;
    const chunkSizes = chunks.map(chunk => chunk.metadata.token_count);
    const minSize = Math.min(...chunkSizes);
    const maxSize = Math.max(...chunkSizes);

    return {
      totalTokens,
      averageChunkSize: Math.round(averageChunkSize),
      minSize: chunks.length > 0 ? minSize : 0,
      maxSize: chunks.length > 0 ? maxSize : 0,
    };
  }

  /**
   * Logs comprehensive results of the chunking process
   */
  private static logChunkingResults(
    documentId: string,
    chunks: InlineCitationsChunk[],
    stats: ReturnType<typeof InlineCitationsChunker.calculateChunkingStats>
  ): void {
    Logger.info("Inline citations chunking completed", {
      documentId,
      chunkCount: chunks.length,
      totalTokens: stats.totalTokens,
      averageChunkSize: stats.averageChunkSize,
      minChunkSize: stats.minSize,
      maxChunkSize: stats.maxSize,
      config: INLINE_CITATIONS_CONFIG,
    });

    // Log metadata summary
    const uniqueSources = new Set(chunks.map(c => c.metadata.source));
    const metadataSummary = {
      sources: Array.from(uniqueSources),
      pageRange: chunks.length > 0 ? {
        min: Math.min(...chunks.map(c => c.metadata.page_number)),
        max: Math.max(...chunks.map(c => c.metadata.page_number)),
      } : null,
      chunkIndexRange: chunks.length > 0 ? {
        min: Math.min(...chunks.map(c => c.metadata.chunk_index)),
        max: Math.max(...chunks.map(c => c.metadata.chunk_index)),
      } : null,
    };

    Logger.debug("Chunk metadata summary", {
      documentId,
      metadataSummary,
    });
  }
}
