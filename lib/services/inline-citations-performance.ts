import { Logger } from "../utils/Logger";

// Performance monitoring for inline citations pipeline
export class InlineCitationsPerformance {
  private static metrics: Map<string, PerformanceMetric> = new Map();
  private static readonly PERFORMANCE_THRESHOLD_MS = 1000; // 1 second threshold

  /**
   * Starts performance monitoring for a specific operation
   */
  static startTimer(operationId: string, operationType: string, metadata?: any): PerformanceTimer {
    const startTime = performance.now();
    const timer: PerformanceTimer = {
      operationId,
      operationType,
      startTime,
      metadata,
      end: () => this.endTimer(operationId, operationType, startTime, metadata),
    };

    Logger.debug("Performance timer started", {
      operationId,
      operationType,
      metadata,
    });

    return timer;
  }

  /**
   * Ends performance monitoring and logs results
   */
  private static endTimer(
    operationId: string,
    operationType: string,
    startTime: number,
    metadata?: any
  ): PerformanceResult {
    const endTime = performance.now();
    const duration = endTime - startTime;

    const result: PerformanceResult = {
      operationId,
      operationType,
      duration,
      metadata,
      timestamp: new Date(),
    };

    // Store metric for analysis
    this.storeMetric(operationType, duration);

    // Log performance result
    this.logPerformanceResult(result);

    return result;
  }

  /**
   * Stores performance metrics for analysis
   */
  private static storeMetric(operationType: string, duration: number): void {
    const existing = this.metrics.get(operationType);
    
    if (existing) {
      existing.count++;
      existing.totalDuration += duration;
      existing.averageDuration = existing.totalDuration / existing.count;
      existing.minDuration = Math.min(existing.minDuration, duration);
      existing.maxDuration = Math.max(existing.maxDuration, duration);
      existing.lastUpdated = new Date();
    } else {
      this.metrics.set(operationType, {
        operationType,
        count: 1,
        totalDuration: duration,
        averageDuration: duration,
        minDuration: duration,
        maxDuration: duration,
        lastUpdated: new Date(),
      });
    }
  }

  /**
   * Logs performance results with appropriate log level
   */
  private static logPerformanceResult(result: PerformanceResult): void {
    const isSlowOperation = result.duration > this.PERFORMANCE_THRESHOLD_MS;
    
    const logData = {
      operationId: result.operationId,
      operationType: result.operationType,
      duration: `${result.duration.toFixed(2)}ms`,
      metadata: result.metadata,
      isSlowOperation,
    };

    if (isSlowOperation) {
      Logger.warn("Slow operation detected", logData);
    } else {
      Logger.debug("Operation completed", logData);
    }
  }

  /**
   * Gets performance metrics for analysis
   */
  static getMetrics(): PerformanceMetric[] {
    return Array.from(this.metrics.values());
  }

  /**
   * Gets metrics for a specific operation type
   */
  static getMetricsForOperation(operationType: string): PerformanceMetric | undefined {
    return this.metrics.get(operationType);
  }

  /**
   * Resets all performance metrics
   */
  static resetMetrics(): void {
    this.metrics.clear();
    Logger.info("Performance metrics reset");
  }

  /**
   * Logs a performance summary
   */
  static logPerformanceSummary(): void {
    const metrics = this.getMetrics();
    
    if (metrics.length === 0) {
      Logger.info("No performance metrics available");
      return;
    }

    const summary = {
      totalOperations: metrics.reduce((sum, m) => sum + m.count, 0),
      operationTypes: metrics.length,
      slowOperations: metrics.filter(m => m.averageDuration > this.PERFORMANCE_THRESHOLD_MS),
      metrics: metrics.map(m => ({
        type: m.operationType,
        count: m.count,
        avgDuration: `${m.averageDuration.toFixed(2)}ms`,
        minDuration: `${m.minDuration.toFixed(2)}ms`,
        maxDuration: `${m.maxDuration.toFixed(2)}ms`,
      })),
    };

    Logger.info("Performance summary", summary);
  }

  /**
   * Monitors a function execution and returns the result with performance data
   */
  static async monitor<T>(
    operationType: string,
    operationId: string,
    fn: () => Promise<T>,
    metadata?: any
  ): Promise<{ result: T; performance: PerformanceResult }> {
    const timer = this.startTimer(operationId, operationType, metadata);
    
    try {
      const result = await fn();
      const performance = timer.end();
      return { result, performance };
    } catch (error) {
      const performance = timer.end();
      Logger.error("Monitored operation failed", {
        operationId,
        operationType,
        duration: performance.duration,
        error,
      });
      throw error;
    }
  }
}

// Types
export interface PerformanceTimer {
  operationId: string;
  operationType: string;
  startTime: number;
  metadata?: any;
  end: () => PerformanceResult;
}

export interface PerformanceResult {
  operationId: string;
  operationType: string;
  duration: number;
  metadata?: any;
  timestamp: Date;
}

export interface PerformanceMetric {
  operationType: string;
  count: number;
  totalDuration: number;
  averageDuration: number;
  minDuration: number;
  maxDuration: number;
  lastUpdated: Date;
}

// Operation types for consistent monitoring
export const OPERATION_TYPES = {
  SIMILARITY_SEARCH: "similarity_search",
  EMBEDDING_GENERATION: "embedding_generation",
  CHUNKING: "chunking",
  CITATION_GENERATION: "citation_generation",
  DOCUMENT_PROCESSING: "document_processing",
  DATABASE_QUERY: "database_query",
} as const;
