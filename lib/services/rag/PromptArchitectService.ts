import { Logger } from "../../utils/Logger";
import { GoogleGenerativeAI } from "@google/generative-ai";
import { getGooglePalmApiKey } from "../../utils/apiKeyRotation";

export class PromptArchitectService {
  private static readonly genAI = new GoogleGenerativeAI(
    getGooglePalmApiKey()
  );

  /**
   * Generates a refined prompt for the Prompt Architect workflow's Preview step
   * 
   * @param promptGoal The user's objective for the prompt
   * @param textReferences Manually added supporting text
   * @param conversationHistory Previous conversation history
   * @returns A refined prompt that incorporates the goal, references, and conversation context
   */
  static async generateRefinedPrompt(
    promptGoal: string,
    textReferences: string = "",
    conversationHistory: string = ""
  ): Promise<string> {
    try {
      const refinedPrompt = await this.refinePrompt(
        promptGoal,
        textReferences,
        conversationHistory
      );

      Logger.debug("Prompt Architect refined prompt result:", refinedPrompt);

      return refinedPrompt;
    } catch (error) {
      Logger.error("Error in prompt refinement process:", error);
      // Return a basic formatted prompt if the refinement fails
      return this.createBasicPrompt(promptGoal, textReferences);
    }
  }

  /**
   * Creates a basic formatted prompt when the AI refinement fails
   */
  private static createBasicPrompt(promptGoal: string, textReferences: string): string {
    let prompt = `Goal: ${promptGoal}\n\n`;
    
    if (textReferences && textReferences.trim()) {
      prompt += `References:\n${textReferences}\n\n`;
    }
    
    return prompt;
  }

  /**
   * Uses the Gemini model to refine the prompt based on the goal, references, and conversation history
   */
  private static async refinePrompt(
    promptGoal: string,
    textReferences: string,
    conversationHistory: string
  ): Promise<string> {
    const model = this.genAI.getGenerativeModel({
      model: "gemini-2.0-flash-lite-preview-02-05",
    });

    const parts: any[] = [];

    const promptText = `
    
Your task is to create a well-structured query based on the user's goal, additional context, and conversation history.

The refined query should:
1. Clearly articulate the user's objective
2. Incorporate relevant context from the additional context
3. Consider any relevant information from the conversation history


Here is the information to work with:

USER'S GOAL:
${promptGoal}

Additional context:
${textReferences ? textReferences : "No additional context provided."}

CONVERSATION HISTORY:
${conversationHistory ? conversationHistory : "No conversation history provided."}

Based on this information, create a refined query that will provided to an advanced legal analyst. The query should provide a strong starting point for
an extremely strong legal analysis.
Format your response as a complete self contained, ready-to-use query with any relevant explanantions or notes on provided context/ goals/ conversation history 
as well as guiding comments as required. Give the query and nothing else.
`;

    parts.push({ text: promptText });

    const result = await model.generateContent({
      contents: [{ role: "user", parts }],
      generationConfig: {
        temperature: 1,
        topP: 0.8,
        topK: 64,
        maxOutputTokens: 256,
        responseMimeType: "text/plain",
      },
    });

    Logger.debug("Gemini response for prompt refinement:", result);

    return result.response.text();
  }
}
