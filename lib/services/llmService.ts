// file: llmService.ts
// llmServices.ts
import Anthropic from "@anthropic-ai/sdk";
import { OpenAI } from "openai";
import { Logger } from "@/lib/utils/Logger";
import { CoreMessage, createDataStreamResponse, FilePart, TextPart } from "ai";
import { convertPdfBlockToDocument } from "./pdf";
import { GoogleGenerativeAI } from "@google/generative-ai";
import { Model } from "@/lib/ai/models";
import { logServerEvent } from "../analytics/events-server";
import { ServerErrorEvent, ServerSuccessEvent } from "../analytics/event-types";
import { VertexAIService } from "./VertexAIService";
import { extractErrorInfo, serializeError } from "../utils/errorUtils";
import { getGooglePalmApiKey } from "../utils/apiKeyRotation";

const openaiClient = new OpenAI({ apiKey: process.env.OPENAI_API_KEY });
const anthropicClient = new Anthropic({
  apiKey: process.env.ANTHROPIC_API_KEY,
});

/* ----------------------- Anthropic LLM Functions ----------------------------*/
/**
 * Sends a non-streaming request to Anthropic.
 */
export async function sendRequestToAnthropic(
  coreMessagesOrPrompt: { role: string; content: any }[] | string,
  systemPrompt?: string,
  model: string = "claude-3-5-sonnet-latest",
  includeFiles = false
): Promise<string | null> {
  let messages: { role: string; content: any }[] | undefined;
  let prompt: string | undefined;
  if (typeof coreMessagesOrPrompt === "string") {
    prompt = coreMessagesOrPrompt;
  } else {
    messages = coreMessagesOrPrompt
      .slice(-15)
      .map((m) => {
        if (!includeFiles && typeof m.content !== "string") {
          m.content = (m.content as Array<any>).map((c: any) =>
            c.type === "file"
              ? { type: "text", text: "(User shared a file)" }
              : c
          );
        }
        if (typeof m.content !== "string") {
          m.content = (m.content as Array<any>).filter(
            (c: any) => c.type !== "tool-call"
          );
        }
        return m.content && (typeof m.content === "string" ? m : null);
      })
      .filter((m) => m && m.role !== "tool") as CoreMessage[];
  }

  const response = await anthropicClient.messages.create({
    model,
    max_tokens: 8192,
    system: systemPrompt,
    // @ts-expect-error
    messages: messages || [{ role: "user", content: prompt }],
    stream: false,
  });
  // Assuming response.content is an array of text parts
  return response.content
    .map((c) => (c.type === "text" ? c.text : null))
    .filter(Boolean)
    .join("");
}

/**
 * Evaluates a prompt response comparison using Anthropic.
 */
export async function evaluatePromptComparison(
  prompt: string,
  responseText: string,
  docContext: string
): Promise<{ evalResp: string | null; modelBResp: string | null }> {
  // const claudeResp = await sendRequestToAnthropic(
  //   `${prompt}

  //   # Uploaded File Context:
  //   ${docContext}`,
  //   undefined,
  //   "claude-3-5-sonnet-latest",
  //   true
  // );
  const modelBResp =
    modelBResponseObj.find(
      (resp) => resp.prompt.toLowerCase().trim() === prompt.toLowerCase().trim()
    )?.response || null;

  const evaluationPrompt = `
TASK: Compare and critically evaluate the performance of Model A vs. Model B in analyzing the given legal issue. Assess their argument structure, use of case law, factual application, alternative strategies, clarity, and persuasiveness. Be strict, not lenient—no perfect scores. Highlight where each model falls short and how it can be improved.

FORMAT: Provide the comparison in a structured chart format with scored categories, followed by a detailed breakdown of strengths, weaknesses, and recommendations. Use a scoring scale from 1-5, where:
5/5 = Nearly flawless (rarely given)
4/5 = Strong but room for improvement
3/5 = Acceptable but needs major refinements
2/5 = Weak, requires significant restructuring
1/5 = Fundamentally flawed

EVALUATION CRITERIA:
Argument Structure: Does the response follow a logical flow, making it easy to track key legal principles?
Use of Case Law & Statutory Authority: Does it correctly cite and apply relevant case law, statutes, and legal principles? Is the precedent on point and persuasive?
Application to Facts: Does the model effectively connect legal standards to the facts at issue? Does it anticipate counterarguments?
Alternative Claim Strategies: Does it explore multiple legal approaches (e.g., different pleading theories, alternative claims)?
Clarity & Readability: Is the response concise, well-organized, and easy to follow? Or is it wordy, dense, or unclear?
Persuasiveness & Strategic Framing: Does it frame arguments in a compelling, litigation-ready manner? Does it advocate effectively for the side it's analyzing?
Overall Legal Strength: Considering all factors, how strong is the response from a legal and strategic standpoint?

DELIVERABLES:
Comparison Table: A side-by-side breakdown scoring each model's performance across all categories.
Strengths & Weaknesses Analysis: A detailed review of what each model did well and where it failed.
Improvement Recommendations: Concrete steps to make each model stronger for real-world legal analysis.

IMPORTANT:
Be brutally critical—point out flaws, missing case law, weak arguments, and inconsistencies.
Identify which model is better and why, but also how it can be further refined.
If neither model is ideal, suggest how a hybrid version could outperform both.

Prompt  x-
....
A1-
...
A2-
...

A1 is our response. A2 is Iqidis's response.
`;
  const evalResp = await sendRequestToGooglePalm(
    `
Prompt (x): x-${prompt}
A1 (our response): ${responseText}
A2 (Iqidis's response): ${modelBResp}
                `,
    evaluationPrompt
    // "claude-3-5-sonnet-latest",
    // true
  );

  return { evalResp, modelBResp };
}

/**
 * Converts core messages into Anthropic's required format.
 */
export async function convertMessagesForLLMs(
  coreMessages: CoreMessage[]
): Promise<{ role: string; content: any }[]> {
  const result: { role: string; content: any }[] = [];
  for (const msg of coreMessages) {
    let content: any;
    if (typeof msg.content === "string") {
      content = msg.content;
    } else if (Array.isArray(msg.content)) {
      const textBlocks = msg.content.filter(
        (block: any) => block.type === "text"
      ) as TextPart[];
      const fileBlocks = msg.content.filter(
        (block: any) => block.type === "file"
      ) as FilePart[];
      const textContent = textBlocks
        .map((block) => block.text)
        .join("\n")
        .trim();
      const documentBlocks: any[] = [];
      for (const block of fileBlocks) {
        if (block.mimeType === "application/pdf") {
          try {
            const pdfUrl = block.data.toString();
            const documentBlock = await convertPdfBlockToDocument(pdfUrl);
            documentBlocks.push(documentBlock);
          } catch (error) {
            console.error("Error processing PDF block:", block.data, error);
          }
        } else {
          console.warn(
            "Non-PDF file encountered; skipping conversion:",
            block.data
          );
        }
      }
      if (documentBlocks.length > 0) {
        content = [...documentBlocks];
        if (textContent) {
          content.push({ type: "text", text: textContent });
        }
      } else {
        content = textContent;
      }
    } else {
      content = String(msg.content);
    }
    result.push({ role: msg.role, content });
  }
  return result;
}

/**
 * Async generator that yields complete SSE event strings from a response stream.
 */
async function* sseEventGenerator(responseStream: NodeJS.ReadableStream) {
  let buffer = "";
  for await (const chunk of responseStream) {
    buffer += chunk.toString("utf8");
    const parts = buffer.split("\n\n");
    buffer = parts.pop() || "";
    for (const part of parts) {
      yield part;
    }
  }
  if (buffer) yield buffer;
}

/**
 * Calls Anthropic's API in streaming mode and transforms SSE events into our response format.
 * The saveMessageCallback parameter is called with (finalMessageId, chatId, aggregatedText)
 * when the stream completes.
 */
export async function callAnthropicAPIStream(
  messages: { role: string; content: string }[],
  system: string,
  userMessageId: string,
  finalMessageId: string,
  chatId: string,
  saveMessageCallback?: (
    finalMessageId: string,
    chatId: string,
    content: string,
    metadata?: {
      internetResults?: {
        mainContent: string;
        citations: string[];
        formattedContent: string;
      };
      relevantImages?: Array<{ mime_type: string; data: string }>;
    }
  ) => Promise<void>
): Promise<Response> {
  const anthropicClient = new Anthropic({
    apiKey: process.env.ANTHROPIC_API_KEY,
  });

  const params: Anthropic.Messages.MessageCreateParams = {
    model: "claude-3-5-sonnet-20241022",
    max_tokens: 8192,
    // @ts-expect-error
    messages,
    system,
    stream: true,
  };
  return createDataStreamResponse({
    async execute(dataStream) {
      try {
        dataStream.write(
          `2:${JSON.stringify([
            { type: "user-message-id", content: userMessageId },
          ])}\n`
        );
        dataStream.write(
          `f:${JSON.stringify({ messageId: finalMessageId })}\n`
        );
        const stream = await anthropicClient.messages.create(params);
        let aggregatedAssistantText = "";
        // @ts-expect-error
        for await (const streamEvent of stream) {
          const event: any = streamEvent;
          if (event.delta && typeof event.delta.text === "string") {
            const textFragment = event.delta.text;
            aggregatedAssistantText += textFragment;
            dataStream.write(`0:${JSON.stringify(textFragment)}\n`);
          } else if (event.delta?.citation?.cited_text) {
            const citedText = `Cited from the uploaded documents: ${event.delta.citation.cited_text}`;
            const formatted = ` {{CITATION:${citedText.replace(/\n/g, " ")}}} `;
            dataStream.write(`0:${JSON.stringify(formatted)}\n`);
            aggregatedAssistantText += formatted;
          } else {
            Logger.debug("Unhandled event from Anthropic SDK:", event);
          }
        }
        if (saveMessageCallback) {
          try {
            await saveMessageCallback(
              finalMessageId,
              chatId,
              aggregatedAssistantText
            );
            Logger.debug(
              "Successfully saved final assistant message via callback."
            );
          } catch (saveError) {
            Logger.error(
              "Failed to save final assistant message via callback:",
              saveError
            );
          }
        }
      } catch (error) {
        Logger.warn("Error calling Anthropic API via SDK:", error);
      }
    },
    headers: {
      "Content-Type": "text/event-stream",
      "Cache-Control": "no-cache",
      "x-vercel-ai-data-stream": "v1",
    },
  });
}

/* ----------------------- OpenAI LLM Functions ----------------------------*/
/**
 * Sends a non-streaming chat completion request to OpenAI.
 */
export async function sendRequestToOpenAI(
  coreMessagesOrPrompt: CoreMessage[] | string,
  systemPrompt?: string,
  model: string = "gpt-3.5-turbo"
): Promise<string | null> {
  let messages: { role: string; content: string }[] = [];
  if (typeof coreMessagesOrPrompt === "string") {
    messages.push({ role: "user", content: coreMessagesOrPrompt });
  } else {
    const filtered = coreMessagesOrPrompt
      .slice(-15)
      .filter((m) => m && m.role !== "tool");
    for (const msg of filtered) {
      if (typeof msg.content === "string") {
        messages.push({ role: msg.role, content: msg.content });
      } else if (Array.isArray(msg.content)) {
        const text = msg.content
          .map((c: any) => (c.type === "text" ? c.text : ""))
          .join("\n")
          .trim();
        messages.push({ role: msg.role, content: text });
      } else {
        messages.push({ role: msg.role, content: String(msg.content) });
      }
    }
  }
  if (systemPrompt) {
    messages.unshift({ role: "system", content: systemPrompt });
  }
  const response = await openaiClient.chat.completions.create({
    model,
    // @ts-expect-error
    messages,
    stream: false,
  });
  return response.choices[0].message?.content;
}

/**
 * Converts core messages into the format required for OpenAI.
 */
export async function convertMessagesForOpenAI(
  coreMessages: CoreMessage[]
): Promise<{ role: string; content: string }[]> {
  const result: { role: string; content: string }[] = [];
  for (const msg of coreMessages) {
    let content: string = "";
    if (typeof msg.content === "string") {
      content = msg.content;
    } else if (Array.isArray(msg.content)) {
      const textBlocks = msg.content.filter(
        (block: any) => block.type === "text"
      ) as TextPart[];
      const fileBlocks = msg.content.filter(
        (block: any) => block.type === "file"
      ) as FilePart[];
      const textContent = textBlocks
        .map((block) => block.text)
        .join("\n")
        .trim();
      let fileContent = "";
      for (const block of fileBlocks) {
        if (block.mimeType === "application/pdf") {
          try {
            const pdfUrl = block.data.toString();
            fileContent += "\n" + (await convertPdfBlockToDocument(pdfUrl));
          } catch (error) {
            console.error("Error processing PDF block:", block.data, error);
          }
        } else {
          console.warn(
            "Non-PDF file encountered; skipping conversion:",
            block.data
          );
        }
      }
      content = textContent + (fileContent ? "\n" + fileContent : "");
    } else {
      content = String(msg.content);
    }
    result.push({ role: msg.role, content });
  }
  return result;
}

/**
 * Calls OpenAI's Chat Completion API in streaming mode.
 * The saveMessageCallback parameter is invoked with (finalMessageId, chatId, aggregatedText)
 * when the stream is finished.
 */
export async function callOpenAIAPIStream(
  messages: { role: string; content: string }[],
  userQuery: string,
  systemPrompt: string,
  userMessageId: string,
  finalMessageId: string,
  chatId: string,
  perplexityAnswer?: {
    mainContent: string;
    citations: string[];
    formattedContent: string;
  },
  processedImages?: Array<{ mime_type: string; data: string; url: string }>,
  filesText?: string,
  rewrittenQuery?: string,
  chainOfThoughtsPromise?: string | Promise<string> | null,
  modelIdentifier: string = "gpt-4.1-2025-04-14",
  saveMessageCallback?: (
    finalMessageId: string,
    chatId: string,
    content: string,
    metadata?: {
      internetResults?: {
        mainContent: string;
        citations: string[];
        formattedContent: string;
      };
      relevantImages?: Array<{ mime_type: string; data: string; url: string }>;
      filesText?: string;
      rewrittenQuery?: string;
      chainOfThoughtsPromise?: string | Promise<string> | null;
    }
  ) => Promise<void>,
  userId?: string,
  userEmail?: string
): Promise<Response> {
  Logger.info("Model used inside callOpenAIAPIStream:", modelIdentifier);
  return createDataStreamResponse({
    async execute(dataStream) {
      try {
        // Creating metadata object
        const metadata = {
          messageId: finalMessageId,
          metadata: {
            internetResults:
              perplexityAnswer && perplexityAnswer.citations?.length > 0
                ? perplexityAnswer
                : undefined,
            relevantImages:
              processedImages && processedImages.length > 0
                ? processedImages
                : undefined,
            chainOfThoughts: chainOfThoughtsPromise || undefined,
          },
        };

        dataStream.write(
          `2:${JSON.stringify([
            { type: "user-message-id", content: userMessageId },
          ])}\n`
        );
        dataStream.write(`f:${JSON.stringify(metadata)}\n`);
        var modelInput = "";

        // Convert messages array to a single prompt string.
        const prompt = messages.map((msg) => msg.content).join("\n");
        modelInput += `\n Previous conversation:\n${prompt}\n`;
        modelInput += `\n Current question: ${userQuery}`;
        if (perplexityAnswer && perplexityAnswer.citations?.length > 0) {
          modelInput += `\n Internet results: ${perplexityAnswer.formattedContent}`;
        }
        if (filesText && filesText.trim()) {
          modelInput += `\n nAdditional context from files: ${filesText}`;
        }

        const parts = [];
        parts.push({
          type: "input_text",
          text: modelInput,
        });

        // Add processed images to parts if available
        if (processedImages && processedImages.length > 0) {
          console.log("Adding processed images to parts");
          // console.log("Processed Images:", processedImages);
          for (const img of processedImages) {
            parts.push({
              type: "input_image",
              image_url: "data:image/jpeg;base64," + img.data,
            });
          }
        }

        const stream = await openaiClient.responses.create({
          input: [
            {
              role: "user",
              content: parts as [],
            },
          ],
          instructions: systemPrompt,
          model: modelIdentifier,
          stream: true,
        });
        let aggregatedAssistantText = "";
        for await (const event of stream) {
          // Handle different event types from the Responses API
          if (event.type === "response.output_text.delta") {
            const delta = event.delta || "";
            if (delta) {
              aggregatedAssistantText += delta;
              dataStream.write(`0:${JSON.stringify(delta)}\n`);
            }
          } else if (event.type === "response.failed") {
            Logger.warn("OpenAI stream failed:", event.response?.error);
          } else if (event.type === "response.completed") {
            Logger.debug("OpenAI stream completed successfully");
          } else if (event.type === "response.incomplete") {
            Logger.warn(
              "OpenAI stream incomplete:",
              event.response?.incomplete_details
            );
          }
        }
        if (saveMessageCallback) {
          try {
            await saveMessageCallback(
              finalMessageId,
              chatId,
              aggregatedAssistantText,
              {
                internetResults: perplexityAnswer,
                relevantImages:
                  processedImages && processedImages.length > 0
                    ? processedImages
                    : undefined,
                filesText:
                  filesText && filesText.trim() ? filesText : undefined,
                chainOfThoughtsPromise: chainOfThoughtsPromise || undefined,
              }
            );
            Logger.debug(
              "Successfully saved final assistant message via callback."
            );
          } catch (saveError) {
            Logger.error(
              "Failed to save final assistant message via callback:",
              saveError
            );
          }
        }
        logServerEvent(ServerSuccessEvent.CHAT_REQUEST_SERVED, {
          chatId,
          userId,
          userEmail,
          modelId: "gpt-4-1-concise",
          // includeInternet,
          // ragEnabled,
          useLegacyModel:true,
          messageId:finalMessageId,
          responseType: "SUCCESS",
        });
      } catch (error) {
        Logger.error("Error calling OpenAI API via SDK:", error);
      }
    },
    onError(error) {
      Logger.error("Error in Fallback gpt4.1 stream: ", error);

      // Check if error is from LLM provider (status 400, 500, 503)
      const status =
        error instanceof Response ? error.status : (error as any).status;
      const isLLMProviderError = status && [400, 500, 503].includes(status);

      // Log server error event
      logServerEvent(ServerErrorEvent.CHAT_ROUTE_ERROR, {
        chatId,
        userId,
        userEmail,
        errorType: isLLMProviderError ? "LLM_PROVIDER_ERROR" : "GENERAL_ERROR",
        statusCode: status,
        errorMessage: error instanceof Error ? error.message : "",
        error: serializeError(error),
        modelIdentifier,
      });

      // Explicitly throw the error to ensure it's caught by the outer catch block
      return JSON.stringify({
        error: {
          type: "GENERAL_ERROR",
          message: error instanceof Error ? error.message : "Unknown error",
          uiMessage: isLLMProviderError
            ? "There was an issue with our AI systems."
            : "",
        },
      });
    },
    headers: {
      "Content-Type": "text/event-stream",
      "Cache-Control": "no-cache",
      "x-vercel-ai-data-stream": "v1",
    },
  });
}

/**
 * Fallback function that streams OpenAI response through an existing dataStream
 * Used when other model providers fail during streaming
 */
async function streamOpenAIFallback(
  dataStream: any,
  messages: { role: string; content: string }[],
  userQuery: string,
  systemPrompt: string,
  finalMessageId: string,
  perplexityAnswer?: {
    mainContent: string;
    citations: string[];
    formattedContent: string;
  },
  processedImages?: Array<{ mime_type: string; data: string; url: string }>,
  filesText?: string,
  modelIdentifier: string = "gpt-4.1-2025-04-14"
): Promise<string> {
  // Notify user about fallback
  
  try {
    // Creating metadata object
    const metadata = {
      messageId: finalMessageId,
      metadata: {
        internetResults:
          perplexityAnswer && perplexityAnswer.citations?.length > 0
            ? perplexityAnswer
            : undefined,
        relevantImages:
          processedImages && processedImages.length > 0
            ? processedImages
            : undefined,
      },
    };
    
    // Send metadata
    dataStream.write(`f:${JSON.stringify(metadata)}\n`);
    
    // Prepare input for OpenAI
    let modelInput = "";
    const prompt = messages.map((msg) => msg.content).join("\n");
    modelInput += `\n Previous conversation:\n${prompt}\n`;
    modelInput += `\n Current question: ${userQuery}`;
    
    if (perplexityAnswer && perplexityAnswer.citations?.length > 0) {
      modelInput += `\n Internet results: ${perplexityAnswer.formattedContent}`;
    }
    
    if (filesText && filesText.trim()) {
      modelInput += `\n Additional context from files: ${filesText}`;
    }

    const parts = [];
    parts.push({
      type: "input_text",
      text: modelInput,
    });

    // Add processed images to parts if available
    if (processedImages && processedImages.length > 0) {
      Logger.info("Adding processed images to fallback OpenAI request");
      for (const img of processedImages) {
        parts.push({
          type: "input_image",
          image_url: "data:image/jpeg;base64," + img.data,
        });
      }
    }

    // Create OpenAI stream
    const stream = await openaiClient.responses.create({
      input: [
        {
          role: "user",
          content: parts as [],
        },
      ],
      instructions: systemPrompt,
      model: modelIdentifier,
      stream: true,
    });
    
    // Process and forward the stream
    let aggregatedAssistantText = "";
    for await (const event of stream) {
      if (event.type === "response.output_text.delta") {
        const delta = event.delta || "";
        if (delta) {
          aggregatedAssistantText += delta;
          dataStream.write(`0:${JSON.stringify(delta)}\n`);
        }
      } else if (event.type === "response.failed") {
        Logger.warn("OpenAI fallback stream failed:", event.response?.error);
        throw new Error("OpenAI fallback stream failed");
      }
    }
    return aggregatedAssistantText;
  } catch (error) {
    Logger.warn("Error in OpenAI fallback:", error);
    throw error;
  }
}

async function streamGeminiResponse(
  dataStream: any,
  modelIdentifier: string,
  systemPrompt: string,
  chatId: string,
  userId: string | undefined,
  parts: any[],
  metadata: {
    messageId: string;
    metadata: {
      internetResults: {
        mainContent: string;
        citations: string[];
        formattedContent: string;
      } | undefined;
      relevantImages: {
        mime_type: string;
        url: string;
      }[] | undefined;
      rewrittenQuery: string;
      chainOfThoughts: string | undefined;
    };
  },
  finalMessageId: string,
  chainOfThoughtsPromise?: Promise<string> | null,
): Promise<string> {
  // Retry the entire response generation process
  const newModelResult = await getGenerativeModelWithFallback(
    modelIdentifier,
    systemPrompt,
    chatId,
    userId
  );

  const newModel = newModelResult.model;
  const newProvider = newModelResult.provider;

  // Retry stream generation
    const newStreamPromise = generateStreamWithRetryAndFallback(
    newModel,
    newProvider,
    parts,
    modelIdentifier,
    systemPrompt,
    chatId,
    userId
  );
  // Start resolving chain of thoughts in parallel if it exists
  let resolvedChainOfThoughts: string | undefined;
  if (chainOfThoughtsPromise) {
    chainOfThoughtsPromise.then(result => {
      resolvedChainOfThoughts = result;
      // Update metadata with resolved chain of thoughts
      metadata.metadata.chainOfThoughts = resolvedChainOfThoughts;
    }).catch(error => {
      Logger.warn("Error resolving chain of thoughts:", error);
    });
  }
  Logger.info("Resolved chain of thoughts response")
  // Now await the stream
  const newStream = await newStreamPromise;
  Logger.info("Resolved gemini stream object")

  // Send initial metadata with complete information
  dataStream.write(`f:${JSON.stringify(metadata)}\n`);

  // Stream the response in real-time with smoother delivery
  let buffer = "";
  let aggregatedAssistantText = ""; // Reset aggregated text for new stream
  let count = 0;
  for await (const event of newStream.stream) {
    if (event && event.text) {
      const textFragment = event.text();
      aggregatedAssistantText += textFragment;
      buffer += textFragment;

      // Send content more frequently with smaller chunks
      if (buffer.length > 60 || buffer.includes("\n")) {
        dataStream.write(`0:${JSON.stringify(buffer)}\n`);
        count++;
        if (count === 1){
          Logger.info("First chunk sent");
        }
        // // Only send metadata occasionally to reduce overhead
        // if (buffer.length > 100) {
        //   dataStream.write(`f:${JSON.stringify(metadata)}\n`);
        // }
        buffer = "";
        // Shorter delay for smoother appearance
        await new Promise((resolve) => setTimeout(resolve, 5));
      }
    }
  }

  // Send any remaining buffer content
  if (buffer) {
    dataStream.write(`0:${JSON.stringify(buffer)}\n`);
  }

  // Send final metadata with complete information
  dataStream.write(`f:${JSON.stringify(metadata)}\n`);
  Logger.info("Finished streaming gemini response");
  return aggregatedAssistantText;
}

/**
 * Gets a fresh Google Generative AI client with a rotated API key
 */
function getGoogleGenAIClient() {
  return new GoogleGenerativeAI(getGooglePalmApiKey());
}

// Clean existing message and notify user about fallback
function notifyUserFallback(dataStream: any, finalMessageId: string) {
  dataStream.write(
    `2:${JSON.stringify([
      { type: "clear-message", content: "", messageId: finalMessageId },
    ])}\n`
  );
}

/**
 * Helper function to log model errors consistently
 */
function logModelError(
  errorType: string,
  error: any,
  modelIdentifier: string,
  chatId?: string,
  userId?: string,
  context?: string
) {
  const errorDetails: Record<string, any> = {
    error: serializeError(error),
    modelIdentifier,
    context,
  };

  // Add chatId and userId if available
  if (chatId) errorDetails.chatId = chatId;
  if (userId) errorDetails.userId = userId;

  // Try to extract status code and message
  if (error instanceof Error) {
    errorDetails.errorMessage = error.message;
  }

  // Extract status code if available
  const status =
    error?.status ||
    error?.response?.status ||
    (typeof error === "object" && "code" in error ? error.code : null);

  if (status) {
    errorDetails.statusCode = status;
  }

  // Log the server error event
  logServerEvent(
    errorType === "google_palm"
      ? ServerErrorEvent.GEMINI_PALM_API
      : ServerErrorEvent.GEMINI_VERTEX_API,
    errorDetails,
    false
  );
}

/**
 * Attempts to get a generative model with fallback support.
 */
async function getGenerativeModelWithFallback(
  modelIdentifier: string,
  systemPrompt?: string,
  chatId?: string,
  userId?: string
) {
  // Helper function to create a model with consistent logging
  async function createModel(
    provider: "google_palm" | "google_vertexai",
    modelId?: string,
    context?: string
  ) {
    try {
      const actualModelId = modelId || modelIdentifier;
      Logger.info(`Attempting to initialize ${actualModelId} with ${provider}`);
      let model;
      if (provider === "google_palm") {
        const googleGenAI = getGoogleGenAIClient();
        model = googleGenAI.getGenerativeModel({
          model: actualModelId,
          systemInstruction: systemPrompt,
        });
      } else {
        model = VertexAIService.getGenerativeModel({
          model: actualModelId,
          systemInstruction: systemPrompt,
        });
      }

      Logger.info(`Successfully initialized ${actualModelId} with ${provider}`);
      return { model, provider };
    } catch (error) {
      Logger.warn(
        `Failed to initialize ${modelId || modelIdentifier} with ${provider}:`,
        error
      );
      logModelError(
        provider,
        error,
        modelId || modelIdentifier,
        chatId,
        userId,
        context
      );
      throw error;
    }
  }

  // Try primary model first
  try {
    return await createModel("google_palm", undefined, "Attempt 1");
  } catch (originalError) {
    // Retry primary after 4 sec delay
    try {
      await new Promise((resolve) => setTimeout(resolve, 4000));
      return await createModel("google_palm", undefined, "Attempt 2");
    } catch (retryError) {
      // Try with gemini-2.0-flash-thinking-exp as fallback
      await new Promise((resolve) => setTimeout(resolve, 1000));
      try {
        return await createModel(
          "google_palm",
          "gemini-2.0-flash-thinking-exp",
          "Attempt 3"
        );
      } catch (flashThinkingError) {
        // Try Vertex AI as final fallback
        try {
          return await createModel("google_vertexai", undefined, "Attempt 4");
        } catch (fallbackError) {
          // If all attempts fail, throw original error
          throw originalError;
        }
      }
    }
  }
}

// Add a helper function for stream generation with retry and fallback
async function generateStreamWithRetryAndFallback(
  model: any,
  provider: "google_palm" | "google_vertexai",
  contents: any,
  modelIdentifier: string,
  systemPrompt?: string,
  chatId?: string,
  userId?: string
) {
  // First attempt with current provider
  try {
    return await model.generateContentStream({
      contents: [{ role: "user", parts: contents }],
    });
  } catch (error) {
    Logger.warn(`Stream generation failed with ${provider}:`, error);
    logModelError(
      provider,
      error,
      modelIdentifier,
      chatId,
      userId,
      "Attempt 1"
    );
    // Single retry with same provider after delay
    try {
      await new Promise((resolve) => setTimeout(resolve, 3000));
      Logger.info(`Retrying stream generation with ${provider}`);
        const googleGenAI2 = getGoogleGenAIClient();
        const geminiPro25Model = googleGenAI2.getGenerativeModel({
          model: "gemini-2.5-pro-preview-03-25",
          systemInstruction: systemPrompt,
        });

        return await geminiPro25Model.generateContentStream({
          contents: [{ role: "user", parts: contents }],
        });
    } catch (retryError) {
      Logger.warn(`Retry also failed with ${provider}:`, retryError);
      logModelError(
        provider,
        retryError,
        modelIdentifier,
        chatId,
        userId,
        "Attempt 2"
      );

      // If using Palm API, try with gemini-2.0-flash-thinking-exp
      if (provider === "google_palm") {
        try {
          Logger.info(
            "Attempting fallback to gemini-2.0-flash-thinking-exp for stream generation"
          );
          const googleGenAI = getGoogleGenAIClient();
          const flashThinkingModel = googleGenAI.getGenerativeModel({
            model: "gemini-2.0-flash-thinking-exp",
            systemInstruction: systemPrompt,
          });

          return await flashThinkingModel.generateContentStream({
            contents: [{ role: "user", parts: contents }],
          });
        } catch (flashThinkingError) {
          Logger.warn(
            "gemini-2.0-flash-thinking-exp fallback failed:",
            flashThinkingError
          );
          logModelError(
            "google_palm",
            flashThinkingError,
            "gemini-2.0-flash-thinking-exp",
            chatId,
            userId,
            "Attempt 3"
          );
        }
      }

      // Try Vertex AI as final fallback regardless of provider
      try {
        Logger.info("Attempting fallback to Vertex AI for stream generation");
        const vertexResult = await VertexAIService.getGenerativeModel({
          model: modelIdentifier,
          systemInstruction: systemPrompt,
        });

        return await vertexResult.generateContentStream({
          contents: [{ role: "user", parts: contents }],
        });
      } catch (fallbackError) {
        Logger.warn("Vertex AI fallback also failed:", fallbackError);
        logModelError(
          "google_vertexai",
          fallbackError,
          modelIdentifier,
          chatId,
          userId,
          "Attempt 4"
        );
        if (
          fallbackError instanceof Error &&
          fallbackError?.message.includes(
            "exceeds the maximum number of tokens allowed"
          )
        ) {
          throw fallbackError;
        }
        throw error; // Throw the original error
      }
    }
  }
}
/**
 * Sends a non-streaming request to Google PaLM/Gemini (via @google/generative-ai).
 *
 * @param prompt               Prompt string.
 */
export async function sendRequestToGooglePalm(
  prompt: string,
  system?: string,
  modelIdentifier: string = "gemini-2.0-flash-thinking-exp"
): Promise<string | null> {
  console.log("Sending request to Google PaLM:", {
    system,
    modelIdentifier,
  });

  Logger.info("Model:", modelIdentifier);

  try {
    const googleGenAI = getGoogleGenAIClient();
    const model = googleGenAI.getGenerativeModel({
      model: modelIdentifier,
      systemInstruction: system,
    });

    const { response } = await model.generateContent(prompt);

    // The text content is typically in response.candidates[0].content
    const result = response?.candidates?.[0]?.content || null;
    return result?.parts.map((p) => p.text).join("\n") || null;
  } catch (err) {
    Logger.warn("Error calling Google PaLM:", err);
    return null;
  }
}

/**
 * Calls Google PaLM/Gemini's API in streaming mode and transforms SSE events into our response format.
 * The saveMessageCallback parameter is invoked with (finalMessageId, chatId, aggregatedText)
 * when the stream is finished.
 *
 * This function now accepts parameters similar to the other streaming functions.
 */
export async function callGooglePalmAPIStream(
  messages: { role: string; content: string }[],
  userQuery: string,
  systemPrompt: string,
  userMessageId: string,
  finalMessageId: string,
  chatId: string,
  rewrittenQuery: string,
  modelIdentifier: string,
  perplexityAnswer: {
    mainContent: string;
    citations: string[];
    formattedContent: string;
  },
  processedImages: Array<{ mime_type: string; data: string; url: string }>,
  filesText: string,
  chainOfThoughtsPromise?: Promise<string> | null,
  saveMessageCallback?: (
    finalMessageId: string,
    chatId: string,
    content: string,
    metadata?: {
      internetResults?: {
        mainContent: string;
        citations: string[];
        formattedContent: string;
      };
      relevantImages?: Array<{ mime_type: string; url: string }>;
      rewrittenQuery?: string;
      chainOfThoughts?: string;
    }
  ) => Promise<void>,
  userId?: string,
  userEmail?: string
): Promise<Response> {
  Logger.info("Model used inside callGooglePalmAPIStream:", modelIdentifier);
  Logger.info("Perplexity Answer:", perplexityAnswer);
  Logger.info("Processed Images before streaming:", processedImages.length);

  return createDataStreamResponse({
    async execute(dataStream) {
      // Write initial metadata events to the stream.
      dataStream.write(
        `2:${JSON.stringify([
          { type: "user-message-id", content: userMessageId },
        ])}\n`
      );
      // Create parts array similar to GeminiService
      const parts: any[] = [];

      // Convert messages array to a single prompt string.
      const prompt = messages.map((msg) => msg.content).join("\n");

      parts.push({ text: `\n Previous conversation:\n${prompt}\n` });

      parts.push({ text: `\nCurrent Question: ${userQuery}\n` });

      // Add processed images to parts if available
      if (processedImages && processedImages.length > 0) {
        for (const img of processedImages) {
          parts.push({
            inlineData: {
              mime_type: img.mime_type,
              data: img.data,
            },
          });
        }
      }

      if (perplexityAnswer && perplexityAnswer.formattedContent) {
        parts.push({
          text: `\nAdditional context from internet:\n${perplexityAnswer.formattedContent}`,
        });
      }

      // Add file text if available
      if (filesText && filesText.trim()) {
        parts.push({
          text: `\nAdditional context from files:\n${filesText}`,
        });
      }

      let aggregatedAssistantText = "";

      // Creating metadata object once - FIX: Ensure relevantImages is properly formatted
      const metadata = {
        messageId: finalMessageId,
        metadata: {
          internetResults:
            perplexityAnswer && perplexityAnswer.citations?.length > 0
              ? perplexityAnswer
              : undefined,
          relevantImages:
            processedImages && processedImages.length > 0
              ? processedImages.map((img) => ({
                  mime_type: img.mime_type,
                  url: img.url,
                }))
              : undefined,
          rewrittenQuery,
          chainOfThoughts: undefined,
        },
      };

      try {
        aggregatedAssistantText = await streamGeminiResponse(
          dataStream,
          modelIdentifier,
          systemPrompt,
          chatId,
          userId,
          parts,
          metadata,
          finalMessageId,
          chainOfThoughtsPromise
        );
      } catch (streamError) {
        Logger.warn("Error in Google PaLM stream processing:", streamError);
        const { errorType, uiMessage } = extractErrorInfo(streamError);
        Logger.warn("Detected error of type:", errorType);
        // Log the recitation error event
        logServerEvent(ServerErrorEvent.GEMINI_PALM_API, {
          chatId,
          userId,
          userEmail,
          errorNumber: 1,
          errorType: errorType,
          errorMessage: (streamError as Error).message,
          error: serializeError(streamError),
          modelIdentifier,
        },
          false
        );
        // delay for 3 seconds
        await new Promise((resolve) => setTimeout(resolve, 3000));
        try {
          notifyUserFallback(dataStream, finalMessageId);
          aggregatedAssistantText = await streamGeminiResponse(
            dataStream,
            modelIdentifier,
            systemPrompt,
            chatId,
            userId,
            parts,
            metadata,
            finalMessageId,
            chainOfThoughtsPromise
          );
        }
        catch (streamError2) {
          const { errorType, uiMessage } = extractErrorInfo(streamError2);
          Logger.warn("Retrying with GPT 4.1, detected 2nd error of type:", errorType);
          // Log the recitation error event
          logServerEvent(ServerErrorEvent.GEMINI_PALM_API, {
            chatId,
            userId,
            userEmail,
            errorNumber: 2,
            errorType: errorType,
            errorMessage: (streamError2 as Error).message,
            error: serializeError(streamError2),
            modelIdentifier,
          },
            false
          );
          notifyUserFallback(dataStream, finalMessageId);
          try {
            aggregatedAssistantText = await streamOpenAIFallback(
              dataStream,
              messages,
              userQuery,
              systemPrompt,
              finalMessageId,
              perplexityAnswer,
              processedImages,
              filesText
            );
          }
          catch (fallbackError) {
            const { errorType, uiMessage } = extractErrorInfo(fallbackError);
            Logger.warn("Error in OpenAI fallback:", errorType);
            logServerEvent(ServerErrorEvent.GEMINI_PALM_API, {
              chatId,
              userId,
              userEmail,
              errorNumber: 3,
              errorType: errorType,
              errorMessage: (fallbackError as Error).message,
              error: serializeError(streamError2),
              modelIdentifier: "gpt4.1-fallback",
            },
              false
            );
            // throw original error to be caught by onError handler
            throw streamError;
          }
        }
      }
        
      // Update metadata with the new information
      dataStream.write(`f:${JSON.stringify(metadata)}\n`);
      // Save the complete response with metadata
      if (saveMessageCallback) {
        try {
          await saveMessageCallback(
            finalMessageId,
            chatId,
            aggregatedAssistantText.trim(),
            {
              relevantImages:
                processedImages && processedImages.length > 0
                  ? processedImages.map((img) => ({
                      mime_type: img.mime_type,
                      url: img.url,
                    }))
                  : undefined,
              internetResults:
                perplexityAnswer && perplexityAnswer.citations?.length > 0
                  ? perplexityAnswer
                  : undefined,
              rewrittenQuery,
              chainOfThoughts: metadata.metadata.chainOfThoughts,
            }
          );
          Logger.debug(
            "Successfully saved final Google PaLM message via callback."
          );
        } catch (saveError) {
          Logger.error(
            "Failed to save final Google PaLM message via callback:",
            saveError
          );
        }
      }
      logServerEvent(ServerSuccessEvent.CHAT_REQUEST_SERVED, {
        chatId,
        userId,
        userEmail,
        modelId: modelIdentifier,
        messageId:userMessageId,
        responseType: "SUCCESS",
      });
    },
    onError(error) {
      Logger.error("Error in Google PaLM API stream:", error);

      // Check if error is from LLM provider (status 400, 500, 503)
      const status =
        error instanceof Response ? error.status : (error as any).status;
      const { errorType, uiMessage } = extractErrorInfo(error);

      // Log server error event
      logServerEvent(ServerErrorEvent.CHAT_ROUTE_ERROR, {
        chatId,
        userId,
        userEmail,
        errorType: errorType,
        statusCode: status,
        errorMessage: error instanceof Error ? error.message : "",
        error: serializeError(error),
        modelIdentifier,
      });
      // Explicitly throw the error to ensure it's caught by the outer catch block
      return JSON.stringify({
        error: {
          type: errorType,
          message: error instanceof Error ? error.message : "Unknown error",
          uiMessage: uiMessage,
        },
      });
    },
    headers: {
      "Content-Type": "text/event-stream",
      "Cache-Control": "no-cache",
      "x-vercel-ai-data-stream": "v1",
    },
  });
}

/**
 * Estimates the token count based on a heuristic that one token is ~4 characters.
 * This is a rough approximation and may not be accurate for all inputs.
 *
 * @param text - The input string.
 * @returns Estimated token count.
 */
export function countTokens(text: string): number {
  return Math.ceil(text.length / 4);
}

export const modelBResponseObj = [
  {
    prompt: `Draft a response to the jurisdictional arguments focusing on why the federal court should maintain supplemental jurisdiction over the state law claims even if some federal claims are dismissed`,
    response: `Let me draft a compelling response regarding why the federal court should maintain supplemental jurisdiction over the state law claims even if some federal claims are dismissed:

# MEMORANDUM OF LAW

## The Court Should Exercise Supplemental Jurisdiction Over the State Law Claims

The Court should maintain supplemental jurisdiction over the state law claims even if the federal EMTALA claims are dismissed, as doing so serves the fundamental principles of judicial economy, convenience, fairness, and comity that underlie 28 U.S.C. § 1367.

### I. Legal Standard

While district courts may decline supplemental jurisdiction when federal claims are dismissed before trial (Carnegie-Mellon Univ. v. Cohill, 484 U.S. 343, 350 n.7 (1988)), the Supreme Court has emphasized this is not a "mandatory rule." Rather, courts should consider:

1. Judicial economy
2. Convenience
3. Fairness to litigants
4. Comity with state courts

### II. Judicial Economy Strongly Favors Retaining Jurisdiction

Several factors demonstrate why judicial economy warrants maintaining jurisdiction:

A. **Substantial Investment of Judicial Resources**
- The Court has already devoted significant time to understanding the complex medical facts and interrelated legal theories
- The state law claims arise from the identical core of operative facts as the federal claims
- Starting anew in state court would wastefully duplicate judicial effort

B. **Advanced Stage of Litigation**
- Discovery has progressed substantially on all claims
- The Court is already deeply familiar with the factual record
- Dismissal would require duplicative discovery in state court

### III. Convenience and Fairness Support Federal Jurisdiction

A. **Convenience to Parties**
- All evidence and witnesses are already organized for federal court
- Parallel state proceedings would impose unnecessary costs
- The parties have invested substantial resources in federal litigation

B. **Fairness Considerations**
- Dismissal could create statute of limitations issues for some state claims
- The plaintiff would face additional delays in state court
- The integrated nature of the claims makes separate proceedings inefficient

### IV. Comity Does Not Require Dismissal

While state courts are competent to hear these claims, several factors suggest federal jurisdiction remains appropriate:

- The state law claims do not raise novel or complex issues of state law
- The claims are closely intertwined with federal policy on patient care and hospital obligations
- Federal courts regularly adjudicate similar medical malpractice claims

### V. Conclusion

The interests of justice strongly favor maintaining supplemental jurisdiction. The Court should exercise its discretion to retain the state law claims even if some federal claims are dismissed.

This approach:
1. Promotes efficient use of judicial resources
2. Serves the parties' legitimate interests
3. Avoids unnecessary duplication
4. Ensures comprehensive resolution of interrelated claims

The Court should therefore maintain supplemental jurisdiction over all state law claims.

Would you like me to expand on any of these points or address additional considerations`,
  },
  {
    prompt: `Create a brief explaining why the plaintiff should be allowed to proceed with both individual capacity claims and representative capacity claims, distinguishing the cases cited in the motion to dismiss`,
    response: `# MEMORANDUM OF LAW IN OPPOSITION TO DEFENDANT'S MOTION TO DISMISS INDIVIDUAL CAPACITY CLAIMS

## I. Introduction

Defendant's motion to dismiss plaintiff's individual capacity claims fundamentally misunderstands the distinct nature of individual and representative claims in wrongful death and survival actions. The plaintiff should be permitted to maintain both individual and representative claims because they arise from different legal theories and protect different interests.

## II. Legal Standard

Under New York law, while wrongful death claims must be brought by the estate representative, individual family members may maintain separate claims for their own direct injuries. The key is whether the injury claimed is direct to the individual or derivative of harm to the deceased.

## III. Individual Claims Are Legally Distinct from Representative Claims

### A. Direct Individual Injuries

The plaintiff's individual claims arise from direct injuries she personally suffered, including:

1. Loss of financial support
2. Medical and funeral expenses she personally incurred
3. Direct emotional distress from witnessing inadequate medical care
4. Personal injury from attempting to provide care when facilities failed to do so

These injuries are distinct from those suffered by the deceased and form the basis for valid individual claims.

### B. Distinguishing the Cases Cited by Defendant

The defendant relies primarily on cases that are readily distinguishable:

1. **Carrick v. Central General Hospital** (cited by defendant) addressed only the narrow issue of who may bring the wrongful death claim itself, not whether individual claims are barred.

2. **Liff v. Schildkrout** (cited by defendant) limited only the scope of consortium claims in wrongful death actions but did not address direct individual injuries.

3. The cases cited regarding representative capacity focus on the technical requirements for bringing estate claims but do not preclude parallel individual claims for direct injuries.

## IV. Policy Considerations Support Maintaining Both Claims

### A. Comprehensive Relief

Allowing both individual and representative claims ensures:
- Complete compensation for all injuries
- Efficient resolution of related claims
- Prevention of multiple lawsuits

### B. Consistent with Legislative Intent

New York's wrongful death and survival statutes were intended to expand, not limit, available remedies. Dismissing individual claims would frustrate this purpose.

## V. The Claims Are Properly Pled

The complaint clearly distinguishes between:
- Claims brought in representative capacity for the estate's injuries
- Claims brought individually for direct personal injuries
- Each claim has distinct elements and damages

## VI. Conclusion

The motion to dismiss the individual capacity claims should be denied because:
1. The individual claims arise from direct, personal injuries
2. They are legally distinct from representative claims
3. Maintaining both sets of claims serves judicial economy
4. The claims are properly pled with distinct elements and damages

The Court should allow both the individual and representative claims to proceed to ensure complete adjudication of all legitimate claims arising from this matter.

Would you like me to expand on any of these arguments or address additional aspects of the case law?`,
  },
  {
    prompt: `Draft a supplemental pleading that would strengthen the original complaint's EMTALA claims by more clearly connecting the emergency medical condition requirements to the alleged facts.`,
    response: `# SUPPLEMENTAL PLEADING REGARDING EMTALA CLAIMS

## I. Nature of Emergency Medical Conditions

1. At all relevant times during his care at defendant hospitals, Mr. Friedar suffered from multiple emergency medical conditions as defined by 42 U.S.C. § 1395dd(e)(1), including:

    a. Acute respiratory failure requiring mechanical ventilation, manifesting in:
        - Oxygen saturation levels dropping to 75%
        - Labored breathing requiring immediate ICU intervention
        - Need for emergency intubation

    b. Severe sepsis, evidenced by:
        - Documented infection
        - Systemic inflammatory response
        - Organ dysfunction
        - Deteriorating vital signs

    c. Acute renal failure requiring emergency dialysis, characterized by:
        - Dangerous elevation of blood chemistry values
        - Fluid overload threatening cardiac function
        - Metabolic derangement requiring immediate intervention

2. Each of these conditions constituted an "emergency medical condition" because:
    - They manifested through acute symptoms of sufficient severity
    - Absent immediate medical attention, they could reasonably be expected to result in:
        * Placing the patient's health in serious jeopardy
        * Serious impairment to bodily functions
        * Serious dysfunction of bodily organs

## II. Screening Requirements Violations

3. Despite Mr. Friedar's presentation with acute symptoms, defendants failed to provide appropriate medical screening by:

    a. Failing to recognize and respond to deteriorating respiratory status, specifically:
        - Ignoring documented episodes of aspiration
        - Failing to implement appropriate airway protection measures
        - Delaying necessary respiratory interventions

    b. Inadequately screening for sepsis despite clear risk factors:
        - Failing to perform complete sepsis screening protocols
        - Ignoring elevated inflammatory markers
        - Delayed recognition of infection sources

## III. Stabilization Requirement Violations

4. Defendants violated EMTALA's stabilization requirements by:

    a. Transferring Mr. Friedar between facilities without:
        - Adequate stabilization of his respiratory status
        - Proper evaluation of transfer risks
        - Appropriate transfer protocols for ventilator-dependent patients

    b. Failing to provide necessary stabilizing treatment, including:
        - Appropriate gastric suctioning to prevent aspiration
        - Adequate nutritional support
        - Proper wound care for developing pressure ulcers

5. The Mayo Clinic transfer incident specifically demonstrates EMTALA violations because:

    a. South Nassau:
        - Initially declared Mr. Friedar "too unstable" for transfer
        - Failed to provide specific medical reasons for the transfer denial
        - Did not perform required medical screening to evaluate transfer safety
        - Failed to consult with receiving physician at Mayo Clinic
        - Made unilateral decisions without proper medical evaluation

    b. The receiving facility (Mayo Clinic):
        - Had superior capabilities to treat Mr. Friedar's conditions
        - Had accepted the transfer
        - Had reviewed medical records and agreed to provide necessary care
        - Had appropriate specialists available

## IV. Causation and Damages

6. These EMTALA violations directly resulted in:

    a. Deterioration of Mr. Friedar's condition due to:
        - Delayed appropriate treatment
        - Inadequate stabilization measures
        - Improper transfers between facilities

    b. Development of additional complications including:
        - Progressive respiratory failure
        - Worsening sepsis
        - Development of pressure ulcers
        - Ultimate death

7. The failure to properly screen and stabilize Mr. Friedar's emergency medical conditions, as required by EMTALA, was a direct and proximate cause of his death.

## Prayer for Relief

WHEREFORE, Plaintiff respectfully requests:

1. Compensatory damages as provided by 42 U.S.C. § 1395dd(d)(2)(A)
2. Personal injury damages for pain and suffering
3. Wrongful death damages
4. Costs and attorney's fees
5. Such other relief as this Court deems just and proper

This supplemental pleading strengthens the EMTALA claims by:
- Clearly identifying qualifying emergency medical conditions
- Specifically detailing screening and stabilization violations
- Establishing clear causation between violations and harm
- Documenting the transfer incident within EMTALA's framework
- Connecting factual allegations to statutory requirements

Would you like me to expand on any particular aspect of these supplemental allegations?`,
  },
  {
    prompt: `Analyze the statute of limitations defense for the false imprisonment claim. Draft an argument for why the claim should survive based on continuing violation or other relevant theories`,
    response: `# MEMORANDUM OF LAW IN OPPOSITION TO MOTION TO DISMISS FALSE IMPRISONMENT CLAIM

## I. Introduction

Defendant South Nassau Communities Hospital's motion to dismiss the false imprisonment claim as time-barred should be denied because the alleged false imprisonment constituted a continuing violation that extended through Mr. Friedar's death on November 22, 2015, and the claim was therefore timely filed within the one-year statute of limitations under N.Y. C.P.L.R. § 215.

## II. Legal Standard for Continuing Violation Doctrine

### A. General Principles

The continuing violation doctrine provides that when a defendant's conduct constitutes a continuing wrong, the statute of limitations does not begin to run until the wrongful conduct ends. See Cornwell v. Robinson, 23 F.3d 694, 703 (2d Cir. 1994). This doctrine applies where:

1. The conduct forms part of a continuing pattern
2. The acts are sufficiently connected to constitute a single wrong
3. At least one act occurred within the limitations period

### B. Application to False Imprisonment

Courts have recognized that false imprisonment can constitute a continuing violation where:
- The confinement is ongoing
- The wrongful conduct persists
- The plaintiff remains subject to the defendant's control

## III. The Alleged False Imprisonment Was a Continuing Violation

### A. Pattern of Continuous Confinement

The complaint alleges a pattern of continuous wrongful confinement evidenced by:

1. Initial refusal to transfer on November 20, 2015
2. Continued detention without proper medical justification
3. Ongoing denial of transfer to Mayo Clinic through November 22, 2015
4. Persistent interference with patient autonomy until death

### B. Single Course of Conduct

The alleged false imprisonment constituted a single, unified course of conduct characterized by:

1. Consistent denial of transfer rights
2. Continuous exercise of wrongful control
3. Unbroken chain of improper confinement
4. Integrated pattern of misconduct

## IV. The Claim Was Timely Filed

### A. Accrual Analysis

The false imprisonment claim did not accrue until November 22, 2015, when Mr. Friedar died, because:

1. The wrongful confinement continued until death
2. The hospital maintained improper control throughout
3. The effects of the imprisonment were ongoing
4. The violation remained uninterrupted

### B. Computation of Limitations Period

The March 2017 filing was well within the one-year period because:

1. The claim accrued on November 22, 2015
2. The one-year period began running from that date
3. The filing occurred approximately 15 months after initial confinement but only 16 months after the continuing violation ended

## V. Alternative Theories Supporting Timeliness

### A. Equitable Tolling

Even if the continuing violation doctrine does not apply, equitable tolling is warranted because:

1. The hospital's conduct prevented earlier filing
2. Medical circumstances made earlier challenge impossible
3. The plaintiff pursued rights diligently
4. Extraordinary circumstances prevented timely filing

### B. Integration with Federal Claims

The false imprisonment claim should be viewed as integrated with the federal EMTALA claims because:

1. They arise from the same factual nucleus
2. The claims are inextricably intertwined
3. Federal policy supports unified consideration
4. Judicial economy favors concurrent review

## VI. Conclusion

The false imprisonment claim should survive the motion to dismiss because:

1. The continuing violation doctrine applies to the ongoing wrongful confinement
2. The claim did not accrue until November 22, 2015
3. The March 2017 filing was timely under proper analysis
4. Alternative theories support preservation of the claim
5. The interests of justice favor allowing the claim to proceed

For these reasons, the Court should deny the motion to dismiss the false imprisonment claim as time-barred.

Would you like me to expand on any of these arguments or address additional aspects of the statute of limitations analysis?`,
  },
  {
    prompt: `Based on the complaint's allegations about the GJ tube and intermittent suctioning issues, draft an additional medical negligence count that would survive the motion to dismiss`,
    response: `# SUPPLEMENTAL COUNT FOR MEDICAL NEGLIGENCE
## Re: Gastric-Jejunal Tube Management and Suctioning

Plaintiff hereby supplements the complaint to add the following count for medical negligence:

### COUNT V - MEDICAL NEGLIGENCE
(Against All Defendant Healthcare Facilities)

1. Plaintiff incorporates by reference all preceding paragraphs as if fully set forth herein.

2. The standard of care for patients with gastroparesis and aspiration risk while on mechanical ventilation requires:

   a. Proper evaluation for and placement of appropriate feeding tubes
   b. Implementation of intermittent gastric suctioning when medically indicated
   c. Regular monitoring of tube function and placement
   d. Prompt response to signs of aspiration or tube malfunction
   e. Appropriate consultation with gastroenterology specialists

3. Defendants breached this standard of care through multiple specific acts and omissions, including:

   a. Failing to timely place an appropriate GJ tube despite:
      - Known history of successful GJ tube use in 2003
      - Documented gastroparesis
      - Recurring aspiration events
      - Clear medical indication for jejunal feeding

   b. Improperly maintaining and managing feeding tubes by:
      - Using inappropriate feeding formulas known to clog tubes
      - Failing to properly clean and maintain tubes
      - Allowing tubes to deteriorate and malfunction
      - Placing feed in incorrect ports despite clear labeling

   c. Failing to provide proper intermittent gastric suctioning by:
      - Refusing to order appropriate equipment
      - Failing to train staff on proper suctioning techniques
      - Not implementing physician-ordered suctioning protocols
      - Ignoring clear medical necessity for this intervention

   d. Making improper modifications to medical devices:
      - Creating makeshift feeding tubes from cut pieces
      - Using non-FDA approved tube combinations
      - Failing to replace damaged equipment
      - Installing incorrect suctioning units

4. These breaches directly caused harm including:

   a. Multiple episodes of aspiration pneumonia
   b. Development of serious infections
   c. Respiratory deterioration
   d. Metabolic derangement
   e. Nutritional deficits
   f. Ultimate death

5. The defendants knew or should have known of these risks because:

   a. Prior medical records documented successful GJ tube use
   b. Multiple healthcare providers recommended proper tube placement
   c. Plaintiff repeatedly advised of aspiration events
   d. Standard medical protocols mandate these precautions
   e. Complications were clearly foreseeable

6. Each defendant facility failed to meet the standard of care by:

   a. Roosevelt Hospital:
      - Delayed GJ tube placement for months despite clear indication
      - Ignored documented aspiration events
      - Failed to consult appropriate specialists

   b. Mount Sinai:
      - Created improper makeshift feeding tube
      - Used inappropriate feeding formulas
      - Failed to properly evaluate tube function

   c. The Pavilion:
      - Refused to provide ordered suctioning equipment
      - Failed to train staff on proper tube management
      - Ignored physician orders for proper care

   d. South Nassau:
      - Failed to maintain proper tube function
      - Used inappropriate feeding formulas
      - Ignored deteriorating clinical condition

7. The breaches of the standard of care were substantial factors in causing:

   a. Unnecessary pain and suffering
   b. Additional medical complications
   c. Extended hospitalization
   d. Ultimate death

8. These actions constitute negligence under New York law because:

   a. A clear duty existed based on the physician-patient relationship
   b. The standard of care was well-established
   c. The breaches were clearly documented
   d. Causation is supported by medical records
   e. Damages are readily quantifiable

WHEREFORE, Plaintiff demands judgment against Defendants for:

1. Compensatory damages
2. Costs of suit
3. Pre and post-judgment interest
4. Such other relief as the Court deems just and proper

This count survives the motion to dismiss because it:
- Pleads specific facts showing breach of duty
- Details clear standard of care violations
- Establishes direct causation
- Documents foreseeable harm
- Provides facility-specific allegations

Would you like me to expand on any aspect of this supplemental count?`,
  },
  {
    prompt: `Draft a response to the defendant's argument that Ms. Neesemann cannot proceed pro se. Focus on whether there are any exceptions or alternative approaches available.`,
    response: `# MEMORANDUM OF LAW IN OPPOSITION TO MOTION TO DISMISS PRO SE REPRESENTATION

## I. Introduction

Defendant's motion to dismiss Ms. Neesemann's pro se representation should be denied because (1) she qualifies for recognized exceptions to the general rule against pro se estate representation, (2) federal courts have discretion to permit such representation in appropriate circumstances, and (3) dismissal would frustrate rather than serve the policies underlying the general rule.

## II. Legal Framework

### A. General Rule

While courts generally require estates to be represented by counsel, this rule is not absolute. The primary concerns underlying the rule are:
1. Protection of estate beneficiaries
2. Prevention of unauthorized practice of law
3. Ensuring competent representation

### B. Recognized Exceptions

Courts have recognized exceptions where:
1. The representative is the sole beneficiary
2. No creditors' interests are at stake
3. The representative demonstrates competence to handle the litigation

## III. Ms. Neesemann Qualifies for Exception to General Rule

### A. Sole Beneficiary Status

1. As Mr. Friedar's spouse, Ms. Neesemann is presumptively entitled to:
   - Spousal share under NY EPTL § 4-1.1
   - Family rights and privileges under NY EPTL § 5-3.1
   - Priority in estate administration

2. No evidence suggests existence of other beneficiaries:
   - Will names Ms. Neesemann as sole beneficiary
   - No children or other heirs identified
   - No competing claims to estate assets

### B. Absence of Creditor Interests

1. Estate's primary asset is this litigation
2. Medical expenses covered by insurance
3. No outstanding creditor claims identified
4. No estate insolvency issues present

### C. Demonstrated Competence

1. Ms. Neesemann has shown exceptional capability:
   - Detailed understanding of medical issues
   - Thorough documentation of care history
   - Effective advocacy throughout treatment
   - Well-pleaded complaint and filings

2. Her intimate knowledge of facts makes her uniquely qualified:
   - Present throughout medical care
   - Direct witness to key events
   - Managed all healthcare decisions
   - Maintained comprehensive records

## IV. Federal Court Discretion

### A. Authority to Permit Pro Se Representation

1. Federal courts have inherent authority to manage proceedings
2. 28 U.S.C. § 1654 provides broad pro se rights
3. Courts may fashion appropriate procedures

### B. Factors Supporting Exercise of Discretion

1. Judicial economy served by:
   - Ms. Neesemann's direct knowledge
   - Elimination of communication layers
   - Streamlined proceedings

2. Access to justice promoted through:
   - Reduced litigation costs
   - Direct party participation
   - Efficient case management

## V. Policy Considerations Support Pro Se Representation

### A. Traditional Concerns Not Present

1. No conflict between beneficiaries exists
2. No complex legal issues require specialized knowledge
3. No risk of prejudice to estate interests

### B. Benefits of Pro Se Representation

1. Cost savings benefit estate
2. Direct knowledge enhances presentation
3. Efficient case management promoted

## VI. Alternative Approaches Available

### A. Hybrid Representation

Court could permit:
1. Pro se representation on core issues
2. Appointed counsel for technical matters
3. Guardian ad litem if needed

### B. Conditional Permission

Court could allow pro se representation subject to:
1. Periodic competency review
2. Appointment of standby counsel
3. Enhanced judicial oversight

## VII. Conclusion

The motion to dismiss should be denied because:
1. Ms. Neesemann qualifies for recognized exceptions
2. Federal court discretion supports pro se representation
3. Policy goals are served by allowing direct participation
4. Alternative approaches can address any concerns

The Court should permit Ms. Neesemann to proceed pro se, with such safeguards as it deems appropriate.

Would you like me to expand on any of these arguments or address additional considerations?`,
  },
  {
    prompt: `Create a detailed timeline of the alleged transfers between facilities and explain how each transfer either supports or potentially weakens our EMTALA claims`,
    response: `# ANALYSIS OF FACILITY TRANSFERS AND EMTALA CLAIMS

## I. Chronological Timeline of Transfers

### December 5, 2014 - Initial Emergency Admission
- Location: Roosevelt Hospital
- Precipitating Event: Patient became unresponsive at home
- Condition: Required emergency intubation, ICU care
- EMTALA Implications: Supports claims as emergency condition present

### March 20, 2015 - Transfer to Select Specialty
- From: Roosevelt Hospital
- To: Special Selectivity Hospital (LTAC)
- Condition: Unstable with active secretions
- EMTALA Support: Documents inadequate stabilization before transfer

### April 30, 2015 - Transfer to Cliffside
- From: Select Specialty
- To: Cliffside sub-acute facility
- Condition: Inadequate suctioning equipment
- EMTALA Support: Transfer to facility unable to provide necessary care

### June 2, 2015 - Transfer to The Pavilion
- From: Mount Sinai
- To: The Pavilion
- Condition: Improper tube placement, inadequate equipment
- EMTALA Support: Transfer without ensuring receiving facility capability

### October 5, 2015 - Transfer to Woodmere
- From: Kindred Hospital
- To: Woodmere Rehabilitation
- Condition: Post-sepsis, CO2 overload
- EMTALA Support: Transfer without proper evaluation of receiving facility

### October 8, 2015 - Emergency Transfer
- From: Woodmere
- To: South Nassau
- Condition: Temperature >102°, pneumonia
- EMTALA Support: Strong evidence of emergency condition

### October 21, 2015 - Transfer to Pavilion
- From: South Nassau
- To: The Pavilion
- Condition: Allegedly stable but documentation suggests otherwise
- EMTALA Support: Questionable stability assessment

### November 14, 2015 - Return to South Nassau
- From: The Pavilion
- To: South Nassau
- Condition: Pneumonia and infections
- EMTALA Support: Emergency condition present

### November 20, 2015 - Attempted Mayo Transfer
- Attempted From: South Nassau
- Intended To: Mayo Clinic
- Result: Transfer denied
- EMTALA Support: Key evidence of violation

## II. EMTALA Strengthening Elements

### A. Emergency Medical Conditions
1. Documented throughout transfers:
   - Respiratory failure
   - Sepsis
   - Pneumonia
   - Metabolic derangement

### B. Stabilization Issues
1. Multiple transfers without adequate stabilization:
   - Respiratory status not optimized
   - Infection control incomplete
   - Equipment needs not secured

### C. Transfer Capability Problems
1. Pattern of transfers to inadequate facilities:
   - Lack of necessary equipment
   - Insufficient staff expertise
   - Missing critical care capabilities

## III. Key EMTALA Violations

### A. Improper Transfers
1. To facilities lacking necessary capabilities:
   - Woodmere without suctioning equipment
   - Pavilion without proper respiratory support
   - Facilities without proper isolation capabilities

### B. Transfer Denials
1. Mayo Clinic transfer refusal:
   - Receiving facility had accepted
   - Higher level of care available
   - No proper evaluation documented

### C. Screening Deficiencies
1. Throughout transfer sequence:
   - Inadequate evaluation of stability
   - Incomplete assessment of needs
   - Poor documentation of capability matching

## IV. Strongest EMTALA Arguments

1. Pattern of Destabilizing Transfers:
   - Multiple transfers to inadequate facilities
   - Lack of proper equipment arrangements
   - Insufficient coordination of care

2. Mayo Clinic Transfer Denial:
   - Clear emergency medical condition
   - Accepting facility with greater capabilities
   - No proper screening documented
   - No legitimate medical reason for denial

3. Capability Mismatches:
   - Consistent transfers to facilities lacking necessary equipment
   - Pattern of deterioration after transfers
   - Documentation of foreseeable problems

## V. Potential Weaknesses to Address

1. Time Gaps:
   - Some transfers outside emergency context
   - Chronic care aspects may weaken emergency nature

2. Documentation Issues:
   - Need clearer emergency condition documentation
   - Better stability assessment records
   - More detailed transfer criteria

3. Facility Selection:
   - Some transfers initiated by family
   - Need to distinguish patient preference from facility direction

`,
  },
  {
    prompt: `Draft a revised third cause of action that addresses the defendant's arguments about loss of consortium while maintaining the core claims about damages to the estate.`,
    response: `# THIRD CAUSE OF ACTION
## Estate Damages and Pecuniary Loss
(Against All Defendants)

1. Plaintiff incorporates by reference all preceding paragraphs as if fully set forth herein.

2. Plaintiff brings this cause of action solely in her capacity as Executor of the Estate of Samuel Eagle Friedar pursuant to New York Estates, Powers and Trusts Law § 5-4.1.

### A. Wrongful Death Damages

3. As a direct and proximate result of defendants' negligence and statutory violations, the Estate has suffered pecuniary losses including:

   a. Loss of Mr. Friedar's earnings and future earning capacity, specifically:
      - Documented income history
      - Professional earnings potential
      - Retirement and pension benefits
      - Investment returns

   b. Loss of household services Mr. Friedar would have provided:
      - Home maintenance and repairs
      - Financial management
      - Property administration
      - Business operations assistance

   c. Medical expenses incurred:
      - Hospital bills
      - Physician fees
      - Medication costs
      - Equipment expenses
      - Transportation costs

   d. Funeral and burial expenses:
      - Service costs
      - Burial plot
      - Monument expenses
      - Related arrangements

### B. Survival Action Damages

4. Prior to his death, Mr. Friedar experienced:

   a. Physical pain and suffering from:
      - Multiple episodes of aspiration
      - Respiratory distress
      - Infections and sepsis
      - Pressure ulcers
      - Invasive procedures

   b. Mental and emotional distress from:
      - Awareness of deteriorating condition
      - Physical restraint and confinement
      - Inability to communicate effectively
      - Knowledge of impending death

   c. Loss of enjoyment of life during:
      - Extended hospitalizations
      - Periods of consciousness
      - Family interactions
      - Final months of life

### C. Special Damages

5. The Estate has incurred special damages including:

   a. Lost business opportunities:
      - Documented pending transactions
      - Professional relationships
      - Investment opportunities
      - Business development plans

   b. Property-related losses:
      - Management costs
      - Maintenance expenses
      - Lost rental income
      - Property depreciation

   c. Administrative expenses:
      - Estate administration
      - Legal fees
      - Accounting costs
      - Asset management

### D. Calculation of Damages

6. The Estate's damages are calculated based on:

   a. Economic factors:
      - Life expectancy tables
      - Wage growth projections
      - Inflation adjustments
      - Present value calculations

   b. Documentary evidence:
      - Tax returns
      - Business records
      - Medical bills
      - Expert valuations

   c. Statistical data:
      - Industry standards
      - Actuarial tables
      - Economic indicators
      - Market analyses

### E. Causation

7. These damages were directly caused by defendants':

   a. Failure to provide appropriate medical care
   b. Violations of EMTALA requirements
   c. Negligent transfers between facilities
   d. Inadequate treatment protocols

8. The damages were foreseeable consequences of:

   a. Documented medical conditions
   b. Known risks of treatment decisions
   c. Predictable complications
   d. Standard mortality data

WHEREFORE, Plaintiff, as Executor of the Estate, demands judgment against defendants for:

1. Compensatory damages in an amount to be determined at trial
2. Special damages as proven
3. Pre-judgment interest
4. Costs and disbursements
5. Such other relief as the Court deems just and proper

This revised cause`,
  },
  {
    prompt: `Based on the complaint, draft a supplemental brief explaining why the hospital's refusal to transfer to Mayo Clinic constitutes both an EMTALA violation and false imprisonment. Focus specifically on the alleged instability determination.`,
    response: `Let me draft a supplemental brief analyzing why South Nassau's actions constitute both an EMTALA violation and false imprisonment, focusing on the stability determination.

# SUPPLEMENTAL BRIEF

## I. The Hospital's "Stability" Determination Violated EMTALA

South Nassau's refusal to transfer Mr. Friedar to Mayo Clinic constitutes a clear violation of EMTALA's stability provisions. The hospital's unilateral and unsupported determination that Mr. Friedar was "too unstable" to transfer, made without proper medical screening or consultation with his physicians, directly contravenes EMTALA's requirements.

### A. Improper Stability Assessment

1. The hospital failed to conduct the required medical screening examination to properly assess stability, as mandated by 42 U.S.C. § 1395dd(a).

2. The stability determination was made without consulting:
   - Dr. Pflueger, Mr. Friedar's treating physician who had arranged the Mayo transfer
   - The Mayo Clinic physician who had reviewed records and accepted the transfer
   - The patient's family or medical decision-maker

3. The hospital's actions demonstrate the type of arbitrary transfer denial that EMTALA was designed to prevent, as established in *Hardy v. New York City Health & Hosp. Corp.*, 164 F.3d 789 (2d Cir. 1999).

### B. Failure to Consider Higher Level of Care

The hospital's refusal to transfer ignored that:

1. Mayo Clinic offered specialized treatment unavailable at South Nassau
2. The patient's condition was deteriorating under current care
3. The receiving facility had accepted the transfer and was better equipped to treat the patient

## II. The Hospital's Actions Constituted False Imprisonment

The hospital's conduct meets all elements of false imprisonment under New York law:

1. **Intent to Confine**: The hospital deliberately prevented transfer despite:
   - Arranged medical transport
   - Accepting facility
   - Family's wishes
   - Treating physician's recommendation

2. **Conscious Confinement**: The hospital:
   - Turned away pre-arranged medical transport without notice
   - Failed to present transfer papers or liability waivers
   - Made unilateral decisions without consulting decision-makers

3. **Lack of Consent**: The hospital acted without:
   - Patient/family consent
   - Medical necessity
   - Legal authority

4. **No Reasonable Means of Escape**: The patient was:
   - Physically dependent on hospital care
   - Unable to arrange alternative transport
   - Denied legitimate transfer opportunities

## III. Conclusion

The hospital's actions represent precisely the type of arbitrary medical decision-making that both EMTALA and false imprisonment laws are designed to prevent. The stability determination was made without proper screening, consultation, or consideration of the higher level of care available, while the confinement was implemented through deliberate obstruction of a legitimate transfer attempt.

The hospital's conduct resulted in Mr. Friedar being denied potentially life-saving specialized care at Mayo Clinic, ultimately contributing to his death two days later. This Court should find both an EMTALA violation and false imprisonment.

Respectfully submitted,
[Attorney Name]
[Bar Number]
[Contact Information]
`,
  },
  {
    prompt: `Draft a point-by-point opposition to the defendant's argument that EMTALA doesn't apply in this case. Be specific in citing both the complaint's allegations and relevant case law`,
    response: `
Let me help draft a pointed opposition to the defendant's EMTALA arguments.

# PLAINTIFF'S OPPOSITION TO DEFENDANT'S MOTION TO DISMISS EMTALA CLAIMS

## I. Introduction

Defendant South Nassau's argument that EMTALA does not apply fundamentally mischaracterizes both the statute's scope and the nature of Plaintiff's claims. Their motion relies on an artificially narrow reading of EMTALA that courts have repeatedly rejected.

## II. EMTALA's Protections Extend Beyond Initial Emergency Room Presentations

### A. The Statute's Scope

Defendant's claim that EMTALA applies only to emergency room presentations is legally incorrect. While EMTALA was indeed enacted to prevent "patient dumping," courts have consistently held that its protections extend throughout a patient's hospital stay.

1. In *Moses v. Providence Hosp. & Med. Ctrs., Inc.*, 561 F.3d 573 (6th Cir. 2009), the court explicitly held that EMTALA's stabilization requirement continues until the patient is actually stabilized, not just when the hospital claims stabilization.

2. The defendant's reliance on *Hardy v. New York City Health & Hosp. Corp.* is misplaced, as that case dealt with misdiagnosis claims, not improper stability determinations for transfer purposes.

### B. Continuing Duty to Stabilize

The complaint specifically alleges that:
- Mr. Friedar's condition was actively deteriorating
- The hospital was aware of this deterioration
- The hospital nonetheless claimed "stability" to prevent transfer
- This determination was made without proper medical screening

These allegations directly implicate EMTALA's continuing duty to stabilize under 42 U.S.C. § 1395dd(b).

## III. The Complaint Properly Alleges an EMTALA Violation

### A. Improper Stability Determination

The complaint alleges specific facts showing the hospital's stability determination was pretextual:

1. The hospital declared Mr. Friedar "too unstable" for transfer while simultaneously:
   - Failing to provide necessary treatments
   - Ignoring deteriorating wound conditions
   - Refusing to consult with his treating physicians

2. This determination was made without:
   - Proper medical screening
   - Consultation with accepting facility
   - Review of transfer capabilities
   - Family notification

### B. Transfer to Higher Level of Care

The complaint specifically alleges that:

1. Mayo Clinic had:
   - Reviewed Mr. Friedar's records
   - Accepted the transfer
   - Prepared specialized treatment plans
   - Superior capabilities for treating his condition

2. South Nassau:
   - Lacked equivalent treatment capabilities
   - Failed to provide necessary care
   - Admitted inability to treat certain conditions
   - Had no treatment plan for deteriorating conditions

## IV. EMTALA Applies to Transfers to Higher Levels of Care

### A. Legal Framework

1. EMTALA specifically contemplates transfers to higher levels of care, as recognized in *Brooker v. Desert Hospital Corp.*, 947 F.2d 412 (9th Cir. 1991).

2. The statute requires hospitals to transfer when:
   - They lack capability to stabilize
   - A receiving hospital has superior capabilities
   - The medical benefits outweigh transfer risks

### B. Application to This Case

The complaint alleges:

1. South Nassau lacked capability to treat:
   - Deteriorating pressure ulcers
   - Complex wound care needs
   - Multiple organ system issues

2. Mayo Clinic offered:
   - Specialized wound care
   - Advanced treatment modalities
   - Comprehensive care coordination
   - Superior facilities and expertise

## V. The "Stability" Determination Was Pretextual

### A. Timing and Circumstances

1. The stability determination was made only after:
   - Transfer arrangements were complete
   - Mayo Clinic had accepted the patient
   - Transport was en route

2. The hospital:
   - Made no prior stability concerns known
   - Conducted no new assessments
   - Failed to document stability issues
   - Provided no medical basis for the determination

### B. Medical Evidence

The complaint alleges that:

1. Mr. Friedar's treating physician, Dr. Pflueger:
   - Deemed transfer appropriate
   - Arranged acceptance at Mayo
   - Was not consulted on stability
   - Disputed the hospital's determination

2. The medical records show:
   - Deteriorating condition under current care
   - Need for higher level of treatment
   - No acute changes contraindicating transfer
   - No documented medical basis for transfer denial

## VI. Conclusion

The defendant's argument that EMTALA does not apply represents a fundamental misunderstanding of both the statute and the nature of Plaintiff's claims. The complaint properly alleges an EMTALA violation based on an improper stability determination that prevented necessary transfer to a higher level of care. The motion to dismiss should be denied.

Respectfully submitted,
[Attorney Name]
[Bar Number]
[Contact Information]

This opposition systematically dismantles the defendant's arguments while grounding each point in specific allegations from the complaint and relevant case law. It demonstrates that EMTALA's protections extend beyond initial ER presentations and that the hospital's actions constitute precisely the type of arbitrary transfer denial that EMTALA was designed to prevent
`,
  },
];
