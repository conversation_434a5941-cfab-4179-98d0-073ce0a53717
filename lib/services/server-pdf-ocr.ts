import { Logger } from "../utils/Logger";
import { DocumentPage } from "./gemini-ocr";
import { S3Client, GetObjectCommand } from "@aws-sdk/client-s3";
import { Readable } from "stream";
import * as fs from 'fs';
import * as path from 'path';

// Server-side imports - use legacy build for Node.js
import { createCanvas } from "canvas";

// Import PDF.js using dynamic import to handle ES module issues
let pdfjs: any;

// Initialize S3 client
const s3Client = new S3Client({
  region: process.env.AWS_REGION || "us-east-1",
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY_ID || "",
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY || "",
  },
});

/**
 * Downloads a file from S3, Vercel Blob, or local file system and returns it as a Buffer
 * @param url The file URL
 * @returns Buffer containing the file data
 */
async function downloadFileToBuffer(url: string): Promise<Buffer> {
  // Handle local file:// URLs for testing
  if (url.startsWith("file://")) {
    const fs = await import("fs");
    const filePath = url.replace("file://", "").replace(/\//g, "\\");
    return fs.readFileSync(filePath);
  }

  // Check if this is a Vercel Blob URL
  if (
    url.includes("vercel-storage.com") ||
    url.includes("blob.vercel-storage.com")
  ) {
    // Handle Vercel Blob URL with a direct fetch
    const response = await fetch(url);
    if (!response.ok) {
      throw new Error(`Failed to fetch from Vercel Blob: ${response.status}`);
    }
    return Buffer.from(await response.arrayBuffer());
  }

  // Handle S3 URLs with proper authentication
  const urlObj = new URL(url);
  const bucketName = urlObj.hostname.split(".")[0];
  const key = urlObj.pathname.substring(1); // Remove leading slash

  // Fetching object directly from S3
  const command = new GetObjectCommand({
    Bucket: bucketName,
    Key: key,
  });

  const s3Response = await s3Client.send(command);

  // Convert stream to buffer
  const chunks = [];
  for await (const chunk of s3Response.Body as Readable) {
    chunks.push(chunk);
  }
  return Buffer.concat(chunks);
}

// PDF processing configuration - reverted to working config that produces actual content
const SERVER_PDF_CONFIG = {
  // Working settings that produce 183KB output (not blank)
  SCALE_FACTOR: 1.5, // Working scale factor
  JPEG_QUALITY: 0.9, // Working quality setting from successful config
  TARGET_DPI: 150, // Working DPI
  MAX_PAGES: 50, // Reasonable limit for processing

  // Working hybrid rendering settings
  USE_HYBRID_RENDERING: false, // Working - no hybrid complexity
  RENDER_VECTORS_SEPARATELY: false, // Working - no layer separation
  EXTRACT_TEXT_POSITIONS: true, // Working - enables manual text extraction

  // Working PDF.js settings that produce content
  FORCE_TEXT_RENDERING: true, // Working - forces text rendering
  PREVENT_TEXT_OVERLAP: true, // Working - prevents overlap issues
  USE_SYSTEM_FONTS: true, // Working - enables system fonts
  DISABLE_FONT_FACE: true, // Working - disables embedded fonts
  DISABLE_CLIPPING_MASKS: true, // Working - disables clipping masks
  ENABLE_FONT_HINTING: true, // Working - enables font hinting

  // Working font settings
  TEXT_RENDERING_FONT: 'Arial, sans-serif',
  FALLBACK_FONT: 'Arial, sans-serif',
  TEXT_COLOR: '#000000',

  // Working debug settings
  SAVE_DEBUG_IMAGES: true, // Working - saves debug images
  DEBUG_IMAGE_DIR: 'debug-images',
  SAVE_INTERMEDIATE_LAYERS: false, // Working - no intermediate layers
  SAVE_TRANSFORM_DEBUG: false, // Working - saves transform debug info

  // Working canvas settings
  ENABLE_SUBPIXEL_RENDERING: true, // Working - enables subpixel rendering
  CANVAS_ALPHA: false,
  USE_HIGH_DPI_CANVAS: true, // Working - enables high DPI canvas
} as const;

export interface ServerPdfPage {
  pageNumber: number;
  imageBase64: string;
  width: number;
  height: number;
}

export class ServerPdfOcrService {
  /**
   * Processes a PDF from URL into images using server-side pdfjs-dist + node-canvas
   * @param documentId The document ID for logging
   * @param url The PDF URL
   * @param options Optional processing options
   * @returns Array of document pages with base64 images
   */
  static async processPdfToImages(
    documentId: string,
    url: string,
    options?: {
      renderingApproach?: string;
      sourceService?: string;
      targetPipeline?: string;
    }
  ): Promise<DocumentPage[]> {
    try {
      const processingOptions = {
        renderingApproach: options?.renderingApproach || 'simple-pdfjs',
        sourceService: options?.sourceService || 'server-pdf-ocr',
        targetPipeline: options?.targetPipeline || 'default',
      };

      Logger.info("Starting server-side PDF to images processing", {
        documentId,
        url,
        config: SERVER_PDF_CONFIG,
        processingOptions,
      });

      // Import PDF.js using legacy build for Node.js server environment
      if (!pdfjs) {
        try {
          Logger.info("Importing PDF.js legacy build for Node.js server environment");

          // RUNTIME WORKER PREVENTION: Import and immediately wrap PDF.js
          // This prevents any worker initialization at the source
          const pdfjsModule = await import("pdfjs-dist/legacy/build/pdf.mjs");
          const originalPdfjs = (pdfjsModule as any).default || pdfjsModule;

          // Create a wrapper that prevents worker initialization
          pdfjs = {
            ...originalPdfjs,
            // Override getDocument to ensure no worker is ever used
            getDocument: function(src: any) {
              // Force worker-disabled parameters
              if (typeof src === 'object' && src !== null) {
                const cleanSrc = {
                  ...src,
                  useWorker: false,
                  worker: null,
                  // Remove any worker-related properties
                };
                delete cleanSrc.workerSrc;
                delete cleanSrc.workerPort;

                Logger.debug("PDF.js getDocument called with worker-disabled parameters", {
                  documentId,
                  hasWorkerSrc: 'workerSrc' in src,
                  useWorker: cleanSrc.useWorker
                });

                return originalPdfjs.getDocument(cleanSrc);
              }
              return originalPdfjs.getDocument(src);
            },
            // Expose GlobalWorkerOptions but make it safe
            GlobalWorkerOptions: originalPdfjs.GlobalWorkerOptions ? {
              ...originalPdfjs.GlobalWorkerOptions,
              // Override workerSrc getter to always return empty
              get workerSrc() {
                return '';
              },
              set workerSrc(value) {
                // Ignore any attempts to set workerSrc
                Logger.debug("Blocked attempt to set workerSrc", { documentId, value });
              }
            } : undefined
          };

          Logger.info("PDF.js wrapper created with worker prevention", {
            documentId,
            hasGetDocument: typeof pdfjs.getDocument === 'function',
            hasGlobalWorkerOptions: typeof pdfjs.GlobalWorkerOptions === 'object'
          });
        } catch (importError) {
          Logger.error("PDF.js legacy import failed", {
            error: importError,
            documentId
          });
          throw new Error(`Failed to import PDF.js legacy build: ${importError instanceof Error ? importError.message : String(importError)}`);
        }
      }

      // Configure PDF.js for server-side environment (v4.10.38+ compatible)
      try {
        // Setup Path2D polyfill for node-canvas compatibility
        if (typeof globalThis.Path2D === 'undefined') {
          (globalThis as any).Path2D = class Path2D {
            commands: any[] = [];
            constructor() {
              this.commands = [];
            }
            moveTo(x: number, y: number) { this.commands.push(['moveTo', x, y]); }
            lineTo(x: number, y: number) { this.commands.push(['lineTo', x, y]); }
            closePath() { this.commands.push(['closePath']); }
          };
        }

        // Setup ImageData polyfill for PDF.js image processing
        if (typeof globalThis.ImageData === 'undefined') {
          (globalThis as any).ImageData = class ImageData {
            data: Uint8ClampedArray;
            width: number;
            height: number;

            constructor(dataOrWidth: Uint8ClampedArray | number, widthOrHeight?: number, height?: number) {
              if (typeof dataOrWidth === 'number') {
                // ImageData(width, height)
                this.width = dataOrWidth;
                this.height = widthOrHeight!;
                this.data = new Uint8ClampedArray(this.width * this.height * 4);
              } else {
                // ImageData(data, width, height?)
                this.data = dataOrWidth;
                this.width = widthOrHeight!;
                this.height = height || (this.data.length / (this.width * 4));
              }
            }
          };
        }

        // Setup enhanced Image polyfill for PDF.js embedded image processing
        if (typeof globalThis.Image === 'undefined') {
          const { Image: CanvasImage, createImageData, Canvas } = require('canvas');

          // Use the Canvas Image directly but add error handling
          (globalThis as any).Image = CanvasImage;

          // Also provide Canvas constructor for PDF.js
          if (typeof (globalThis as any).Canvas === 'undefined') {
            (globalThis as any).Canvas = Canvas;
          }

          // Also provide createImageData if needed for advanced image processing
          if (typeof (globalThis as any).createImageData === 'undefined') {
            (globalThis as any).createImageData = createImageData;
          }

          Logger.info("Enhanced Image polyfill setup completed", { documentId });
        }

        // Completely disable worker functionality for server environment
        // Do not configure any worker options to avoid worker initialization
        Logger.info("PDF.js configured for server environment without worker", {
          documentId,
          hasGlobalWorkerOptions: typeof pdfjs.GlobalWorkerOptions === 'object'
        });

        // Clear any global worker references to prevent worker loading
        if (typeof globalThis !== 'undefined') {
          (globalThis as any).pdfjsWorker = undefined;
          (globalThis as any).pdfjsLib = undefined;
          (globalThis as any).pdfjsWorkerSrc = undefined;
        }

      } catch (workerConfigError) {
        Logger.warn("Worker configuration failed, continuing without worker setup", {
          error: workerConfigError,
          documentId
        });
      }

      // Download PDF buffer
      const buffer = await downloadFileToBuffer(url);
      const uint8Array = new Uint8Array(buffer);

      Logger.debug("PDF buffer downloaded", {
        documentId,
        bufferSize: buffer.length,
      });

      // Configure PDF.js document initialization for server environment
      // Enhanced configuration for reliable font rendering
      const documentInitParams = {
        data: uint8Array,
        // CRITICAL: Disable worker functionality completely
        useWorker: false,
        worker: null,
        // Disable all worker-related features
        useWorkerFetch: false,
        isEvalSupported: false,
        // Disable all external resource loading
        disableAutoFetch: true,
        disableStream: true,
        disableRange: true,
        // Enhanced font rendering settings for proper embedded font handling
        disableFontFace: SERVER_PDF_CONFIG.DISABLE_FONT_FACE, // false - enable embedded fonts for better fidelity
        useSystemFonts: SERVER_PDF_CONFIG.USE_SYSTEM_FONTS, // true - enable system font fallback
        standardFontDataUrl: SERVER_PDF_CONFIG.USE_SYSTEM_FONTS ?
          'https://cdn.jsdelivr.net/npm/pdfjs-dist@4.10.38/standard_fonts/' : null,
        // Enhanced font configuration for better rendering
        fontExtraProperties: SERVER_PDF_CONFIG.ENABLE_FONT_HINTING, // true - enable font hinting
        // Enable CMap loading for proper font handling (critical for embedded fonts)
        cMapUrl: SERVER_PDF_CONFIG.USE_SYSTEM_FONTS ?
          'https://cdn.jsdelivr.net/npm/pdfjs-dist@4.10.38/cmaps/' : null,
        cMapPacked: SERVER_PDF_CONFIG.USE_SYSTEM_FONTS, // Enable packed CMaps
        // Disable advanced features that might require workers
        enableXfa: false,
        // Canvas and image settings for server environment
        isOffscreenCanvasSupported: false,
        isImageDecoderSupported: false,
        // Enhanced verbosity for font debugging
        verbosity: 1, // Increased from 0 to help debug font issues
        // Disable image processing optimizations that might use workers
        maxImageSize: -1,
        // Additional font rendering options
        disableCreateObjectURL: true, // Prevent object URL creation for fonts
        // Additional worker prevention
        stopAtErrors: false,
      };

      // Load PDF document with enhanced error handling
      let pdf;
      try {
        Logger.info("Starting PDF document loading", {
          documentId,
          bufferSize: uint8Array.length,
          configKeys: Object.keys(documentInitParams)
        });

        pdf = await pdfjs.getDocument(documentInitParams).promise;
        const totalPages = pdf.numPages;

        Logger.info(`PDF loaded successfully. Processing ${totalPages} pages`, {
          documentId,
          totalPages,
        });
      } catch (getDocumentError) {
        Logger.error("PDF.js getDocument failed", {
          documentId,
          error: getDocumentError instanceof Error ? getDocumentError.message : String(getDocumentError),
          stack: getDocumentError instanceof Error ? getDocumentError.stack : undefined
        });

        // Check if it's a worker-related error
        const errorMessage = getDocumentError instanceof Error ? getDocumentError.message : String(getDocumentError);
        if (errorMessage.includes('worker') || errorMessage.includes('Worker')) {
          throw new Error(`PDF.js worker error (should be prevented by webpack config): ${errorMessage}`);
        }

        // Re-throw other errors
        throw getDocumentError;
      }

      const totalPages = pdf.numPages;

      // Limit pages for performance
      const pagesToProcess = Math.min(totalPages, SERVER_PDF_CONFIG.MAX_PAGES);
      if (totalPages > SERVER_PDF_CONFIG.MAX_PAGES) {
        Logger.warn(`PDF has ${totalPages} pages, limiting to ${SERVER_PDF_CONFIG.MAX_PAGES} for performance`, {
          documentId,
          totalPages,
          processedPages: pagesToProcess,
        });
      }

      // Process pages in parallel with controlled concurrency
      const pages: DocumentPage[] = [];
      const batchSize = 3; // Process 3 pages at a time to avoid memory issues

      for (let i = 0; i < pagesToProcess; i += batchSize) {
        const batchEnd = Math.min(i + batchSize, pagesToProcess);
        const batchPromises = [];

        for (let pageNum = i + 1; pageNum <= batchEnd; pageNum++) {
          batchPromises.push(this.processPageSafely(pdf, pageNum, documentId, processingOptions));
        }

        const batchResults = await Promise.allSettled(batchPromises);

        // Filter successful results and log failures
        for (let j = 0; j < batchResults.length; j++) {
          const result = batchResults[j];
          const pageNum = i + j + 1;

          if (result.status === 'fulfilled' && result.value) {
            pages.push(result.value);
          } else {
            Logger.warn(`Page ${pageNum} processing failed, skipping`, {
              documentId,
              pageNumber: pageNum,
              error: result.status === 'rejected' ? result.reason?.message || result.reason : 'Unknown error'
            });
          }
        }

        Logger.info(`Processed batch ${Math.floor(i / batchSize) + 1}`, {
          documentId,
          pagesProcessed: pages.length,
          totalPages: pagesToProcess,
          skippedPages: (batchEnd - i) - batchResults.filter(r => r.status === 'fulfilled' && r.value).length
        });
      }

      // Clean up PDF document
      pdf.destroy();

      Logger.info("Server-side PDF to images processing completed", {
        documentId,
        pagesProcessed: pages.length,
        totalImageSize: pages.reduce((sum, page) => sum + page.imageBase64.length, 0),
      });

      return pages;
    } catch (error) {
      Logger.error("Error in server-side PDF to images processing", {
        documentId,
        error: error instanceof Error ? error.message : "Unknown error",
        stack: error instanceof Error ? error.stack : undefined,
      });

      throw error;
    }
  }

  /**
   * Safely processes a single PDF page, handling errors gracefully
   * @param pdf The PDF document
   * @param pageNumber The page number (1-based)
   * @param documentId The document ID for logging
   * @param processingOptions Processing options for logging
   * @returns Document page with base64 image or null if failed
   */
  private static async processPageSafely(
    pdf: any, // PDFDocumentProxy type
    pageNumber: number,
    documentId: string,
    processingOptions: any
  ): Promise<DocumentPage | null> {
    try {
      return await this.processPage(pdf, pageNumber, documentId, processingOptions);
    } catch (error) {
      Logger.warn(`Page ${pageNumber} processing failed, will be skipped`, {
        documentId,
        pageNumber,
        error: error instanceof Error ? error.message : "Unknown error",
        processingOptions,
      });
      return null;
    }
  }

  /**
   * Processes a single PDF page to base64 image using node-canvas
   * @param pdf The PDF document
   * @param pageNumber The page number (1-based)
   * @param documentId The document ID for logging
   * @param processingOptions Processing options for logging
   * @returns Document page with base64 image
   */
  private static async processPage(
    pdf: any, // PDFDocumentProxy type
    pageNumber: number,
    documentId: string,
    processingOptions?: any
  ): Promise<DocumentPage> {
    try {
      Logger.debug(`Processing page ${pageNumber}`, { documentId, pageNumber });

      // Get the page
      const page = await pdf.getPage(pageNumber);

      // Get viewport with desired scale
      const viewport = page.getViewport({ scale: SERVER_PDF_CONFIG.SCALE_FACTOR });

      Logger.debug(`Page ${pageNumber} viewport created`, {
        documentId,
        pageNumber,
        width: viewport.width,
        height: viewport.height,
        scale: SERVER_PDF_CONFIG.SCALE_FACTOR
      });

      // Create fresh canvas for each page to prevent contamination
      const canvas = createCanvas(viewport.width, viewport.height);
      const context = canvas.getContext("2d");

      // CRITICAL: Reset canvas completely to prevent contamination from failed renders
      this.resetCanvas(canvas, context, viewport);

      Logger.debug(`Canvas reset completed for page ${pageNumber}`, {
        documentId,
        pageNumber,
        canvasSize: `${viewport.width}x${viewport.height}`,
        canvasState: 'clean'
      });

      // Simple render context optimized for clean PDF.js rendering
      const renderContext = {
        canvasContext: context as any, // Type assertion for pdfjs compatibility
        viewport: viewport,
        // Simple settings for reliable rendering
        background: 'white',
        enableWebGL: false,
        // Let PDF.js handle text rendering completely
        renderTextLayer: true, // Enable - PDF.js handles text with FORCE_TEXT_RENDERING
        renderAnnotationLayer: SERVER_PDF_CONFIG.DISABLE_CLIPPING_MASKS ? false : true,
        renderImageLayer: true, // Keep images and graphics
        renderInteractiveForms: false, // Disable forms to reduce complexity
        optionalContentConfigPromise: null,
        // Font settings for clean rendering
        fontExtraProperties: SERVER_PDF_CONFIG.ENABLE_FONT_HINTING,
        useSystemFonts: SERVER_PDF_CONFIG.USE_SYSTEM_FONTS,
        disableFontFace: SERVER_PDF_CONFIG.DISABLE_FONT_FACE,
        // Fallback font for glyph consistency
        fallbackFontFamily: SERVER_PDF_CONFIG.FALLBACK_FONT,
      };

      // DEBUG: Log page info before rendering
      Logger.debug(`Page ${pageNumber} info before rendering`, {
        documentId,
        pageNumber,
        hasContent: page.getAnnotations ? 'yes' : 'no',
        viewport: { width: viewport.width, height: viewport.height },
      });

      // Simplified rendering approach with proper error handling and canvas cleanup
      try {
        if (SERVER_PDF_CONFIG.USE_HYBRID_RENDERING) {
          // Hybrid approach: render vectors and text separately with precise alignment
          await this.hybridRenderWithAlignment(page, context, viewport, documentId, pageNumber);
        } else {
          // PDF.js rendering - approach determined by caller
          const renderingApproach = processingOptions?.renderingApproach || 'simple-pdfjs';
          const sourceService = processingOptions?.sourceService || 'server-pdf-ocr';
          const targetPipeline = processingOptions?.targetPipeline || 'default';

          Logger.info(`Using ${renderingApproach} rendering for page ${pageNumber}`, {
            documentId,
            pageNumber,
            renderingApproach,
            sourceService,
            targetPipeline,
            fontSettings: {
              forceTextRendering: SERVER_PDF_CONFIG.FORCE_TEXT_RENDERING,
              useSystemFonts: SERVER_PDF_CONFIG.USE_SYSTEM_FONTS,
              disableFontFace: SERVER_PDF_CONFIG.DISABLE_FONT_FACE,
              fallbackFont: SERVER_PDF_CONFIG.FALLBACK_FONT
            }
          });

          // Attempt simple PDF.js render
          try {
            const renderTask = page.render(renderContext);
            await renderTask.promise;

            Logger.info(`${renderingApproach} render successful for page ${pageNumber}`, {
              documentId,
              pageNumber,
              renderingApproach,
              sourceService,
              targetPipeline
            });

            // Save debug image after PDF.js rendering
            if (SERVER_PDF_CONFIG.SAVE_DEBUG_IMAGES) {
              await this.saveDebugImage(documentId, pageNumber, context, renderingApproach);
            }
          } catch (renderError) {
            const errorMessage = renderError instanceof Error ? renderError.message : String(renderError);
            Logger.warn(`${renderingApproach} render failed for page ${pageNumber}, attempting recovery`, {
              documentId,
              pageNumber,
              error: errorMessage,
              renderingApproach,
              sourceService,
              targetPipeline
            });

            // CRITICAL: Reset canvas after failed render to prevent contamination
            this.resetCanvas(canvas, context, viewport);

            // Skip this page gracefully instead of corrupting subsequent pages
            throw new Error(`Page ${pageNumber} render failed and canvas reset: ${errorMessage}`);
          }

          // Add manual text layer as specified in working config
          if (SERVER_PDF_CONFIG.EXTRACT_TEXT_POSITIONS) {
            Logger.debug(`Adding manual text layer for page ${pageNumber} (working config)`, {
              documentId,
              pageNumber,
              extractTextPositions: SERVER_PDF_CONFIG.EXTRACT_TEXT_POSITIONS
            });
            await this.renderManualTextLayer(page, context, viewport, documentId, pageNumber);
          }
        }

        // DEBUG: Check if canvas has any content after text rendering
        const imageData = context.getImageData(0, 0, viewport.width, viewport.height);
        const hasContent = this.checkCanvasHasContent(imageData);

        Logger.debug(`Page ${pageNumber} render completed`, {
          documentId,
          pageNumber,
          hasVisibleContent: hasContent,
          canvasSize: `${viewport.width}x${viewport.height}`,
        });

        if (!hasContent) {
          Logger.warn(`Page ${pageNumber} appears to be blank after rendering`, {
            documentId,
            pageNumber,
            suggestion: "PDF may be image-based or have rendering issues",
          });

          // Try alternative rendering approaches for blank pages
          await this.tryAlternativeRendering(page, context, viewport, pageNumber, documentId);
        }
      } catch (renderError) {
        const errorMessage = renderError instanceof Error ? renderError.message : String(renderError);
        Logger.warn(`Render failed for page ${pageNumber}, attempting graceful recovery`, {
          documentId,
          pageNumber,
          error: errorMessage
        });

        // CRITICAL: Reset canvas immediately after any render failure
        this.resetCanvas(canvas, context, viewport);

        Logger.debug(`Canvas reset after render failure for page ${pageNumber}`, {
          documentId,
          pageNumber,
          errorType: errorMessage.includes('Image') ? 'image-error' : 'general-error'
        });

        // Gracefully skip failed renders instead of attempting complex fallbacks
        // This prevents canvas contamination from affecting subsequent pages
        throw new Error(`Page ${pageNumber} render failed after canvas reset: ${errorMessage}`);
      }

      // Convert canvas to base64 JPEG
      const imageBase64 = canvas.toDataURL("image/jpeg", SERVER_PDF_CONFIG.JPEG_QUALITY);

      // DEBUG: Save image to file for inspection if debug mode is enabled
      if (SERVER_PDF_CONFIG.SAVE_DEBUG_IMAGES) {
        await this.saveDebugImage(documentId, pageNumber, imageBase64, viewport);
      }

      // Clean up page
      page.cleanup();

      Logger.debug(`Page ${pageNumber} processed successfully`, {
        documentId,
        pageNumber,
        width: viewport.width,
        height: viewport.height,
        imageSize: imageBase64.length,
        scaleFactor: SERVER_PDF_CONFIG.SCALE_FACTOR,
        jpegQuality: SERVER_PDF_CONFIG.JPEG_QUALITY,
      });

      return {
        pageNumber,
        imageBase64,
      };
    } catch (error) {
      Logger.error(`Error processing page ${pageNumber}`, {
        documentId,
        pageNumber,
        error: error instanceof Error ? error.message : "Unknown error",
      });

      throw error;
    }
  }

  /**
   * Enhanced hybrid rendering with precise text-table layer alignment
   * Renders vector shapes and text separately with consistent viewport and transform matrices
   * @param page PDF page object
   * @param context Canvas context
   * @param viewport Page viewport
   * @param documentId Document ID for logging
   * @param pageNumber Page number
   */
  private static async hybridRenderWithAlignment(
    page: any,
    context: any,
    viewport: any,
    documentId: string,
    pageNumber: number
  ): Promise<void> {
    try {
      Logger.info(`Starting enhanced hybrid rendering with text-table alignment for page ${pageNumber}`, {
        documentId,
        pageNumber,
        viewportSize: `${viewport.width}x${viewport.height}`,
        scaleFactorUsed: SERVER_PDF_CONFIG.SCALE_FACTOR,
        targetDPI: SERVER_PDF_CONFIG.TARGET_DPI
      });

      // CRITICAL: Use consistent viewport for both layers to ensure perfect alignment
      const consistentViewport = viewport;

      // Step 1: Render vector graphics and table lines (no text)
      const vectorRenderContext = {
        canvasContext: context,
        viewport: consistentViewport, // Use consistent viewport
        background: 'white',
        enableWebGL: false,
        renderTextLayer: false, // CRITICAL: Exclude text from vector rendering
        renderAnnotationLayer: false,
        renderImageLayer: true, // Include images and vector graphics (table lines)
        renderInteractiveForms: false,
        optionalContentConfigPromise: null,
        // Ensure consistent rendering settings
        useSystemFonts: false, // Disable fonts for vector layer
        disableFontFace: true, // Disable fonts for vector layer
      };

      Logger.info(`Rendering vector/table layer for page ${pageNumber}`, {
        documentId,
        pageNumber,
        layerType: 'vectors-tables'
      });

      await page.render(vectorRenderContext).promise;

      // Save intermediate vector layer for alignment verification
      if (SERVER_PDF_CONFIG.SAVE_INTERMEDIATE_LAYERS) {
        await this.saveIntermediateLayer(documentId, pageNumber, context, 'vectors-tables');
      }

      // Step 2: Extract text content and positions with transform matrix debugging
      Logger.info(`Extracting text content with transform debugging for page ${pageNumber}`, {
        documentId,
        pageNumber
      });

      const textContent = await page.getTextContent();

      if (textContent && textContent.items && textContent.items.length > 0) {
        Logger.info(`Found ${textContent.items.length} text items for precise alignment`, {
          documentId,
          pageNumber,
          textItemCount: textContent.items.length
        });

        // Step 3: Render text with precise alignment over vector/table layer
        await this.renderAlignedTextLayer(
          textContent,
          context,
          consistentViewport, // Use same viewport as vector layer
          documentId,
          pageNumber
        );

        // Save intermediate text layer for alignment verification
        if (SERVER_PDF_CONFIG.SAVE_INTERMEDIATE_LAYERS) {
          await this.saveIntermediateLayer(documentId, pageNumber, context, 'aligned-text');
        }
      } else {
        Logger.warn(`No text content found for page ${pageNumber}`, {
          documentId,
          pageNumber
        });
      }

      Logger.info(`Enhanced hybrid rendering with text-table alignment completed for page ${pageNumber}`, {
        documentId,
        pageNumber,
        textItems: textContent?.items?.length || 0,
        renderingApproach: 'hybrid-aligned',
        layersRendered: ['vectors-tables', 'aligned-text']
      });

    } catch (error) {
      Logger.error(`Enhanced hybrid rendering failed for page ${pageNumber}`, {
        documentId,
        pageNumber,
        error: error instanceof Error ? error.message : String(error),
        renderingApproach: 'hybrid-aligned'
      });
      throw error;
    }
  }

  /**
   * DEBUG: Check if canvas has any visible content (not just white background)
   * @param imageData Canvas image data
   * @returns True if canvas has visible content
   */
  private static checkCanvasHasContent(imageData: any): boolean {
    const data = imageData.data;
    const threshold = 250; // Consider pixels with RGB values below this as content
    let contentPixels = 0;

    // Check every 10th pixel for performance (sampling)
    for (let i = 0; i < data.length; i += 40) { // RGBA = 4 bytes, so every 10th pixel
      const r = data[i];
      const g = data[i + 1];
      const b = data[i + 2];

      // If any color channel is significantly below white, consider it content
      if (r < threshold || g < threshold || b < threshold) {
        contentPixels++;

        // If we find enough content pixels, we can return early
        if (contentPixels > 100) {
          return true;
        }
      }
    }

    // Consider it has content if more than 0.1% of sampled pixels are non-white
    const sampledPixels = data.length / 40;
    const contentRatio = contentPixels / sampledPixels;
    return contentRatio > 0.001;
  }

  /**
   * Render high-quality text at vector positions for optimal OCR
   * @param textContent Text content from PDF
   * @param context Canvas context
   * @param viewport Page viewport
   * @param documentId Document ID for logging
   * @param pageNumber Page number
   */
  private static async renderHighQualityText(
    textContent: any,
    context: any,
    viewport: any,
    documentId: string,
    pageNumber: number
  ): Promise<void> {
    try {
      Logger.debug(`Rendering high-quality text for page ${pageNumber}`, {
        documentId,
        pageNumber,
        textItems: textContent.items.length,
      });

      // Set up high-quality text rendering
      context.textBaseline = 'alphabetic';
      context.fillStyle = SERVER_PDF_CONFIG.TEXT_COLOR;

      // Enable high-quality text rendering
      if (context.textRenderingOptimization) {
        context.textRenderingOptimization = 'optimizeQuality';
      }
      if (context.imageSmoothingEnabled !== undefined) {
        context.imageSmoothingEnabled = true;
        context.imageSmoothingQuality = 'high';
      }

      let renderedCount = 0;

      // Render each text item at its vector position
      for (const item of textContent.items) {
        if (!item.str || item.str.trim().length === 0) continue;

        try {
          const transform = item.transform;
          if (!transform || transform.length < 6) continue;

          // Extract position from transform matrix
          const x = transform[4];
          const y = transform[5];
          const scaleY = Math.abs(transform[3]) || 12;

          // Use direct coordinates (no flipping for hybrid approach)
          const canvasX = x;
          const canvasY = y;

          // Validate coordinates
          if (canvasX < 0 || canvasX > viewport.width ||
              canvasY < 0 || canvasY > viewport.height) {
            continue;
          }

          // Set font size with reasonable limits for OCR
          const fontSize = Math.max(Math.min(scaleY, 72), 8);
          context.font = `${fontSize}px ${SERVER_PDF_CONFIG.TEXT_RENDERING_FONT}`;

          // Render text at vector position
          context.fillText(item.str, canvasX, canvasY);
          renderedCount++;

        } catch (itemError) {
          Logger.debug(`Failed to render text item: "${item.str}"`, {
            documentId,
            pageNumber,
            error: itemError instanceof Error ? itemError.message : String(itemError),
          });
        }
      }

      Logger.debug(`High-quality text rendering completed for page ${pageNumber}`, {
        documentId,
        pageNumber,
        totalItems: textContent.items.length,
        renderedItems: renderedCount,
      });

    } catch (error) {
      Logger.error(`High-quality text rendering failed for page ${pageNumber}`, {
        documentId,
        pageNumber,
        error: error instanceof Error ? error.message : String(error),
      });
    }
  }



  /**
   * CRITICAL FIX: Manually render text content to canvas
   * This fixes the issue where borders are visible but text content is missing
   * @param page PDF page object
   * @param context Canvas context
   * @param viewport Page viewport
   * @param documentId Document ID for logging
   * @param pageNumber Page number
   */
  private static async renderTextToCanvas(
    page: any,
    context: any,
    viewport: any,
    documentId: string,
    pageNumber: number
  ): Promise<void> {
    try {
      // Get text content from the page
      const textContent = await page.getTextContent();

      if (!textContent || !textContent.items || textContent.items.length === 0) {
        Logger.debug(`Page ${pageNumber} has no text content to render`, {
          documentId,
          pageNumber,
        });
        return;
      }

      Logger.debug(`Manually rendering ${textContent.items.length} text items to canvas`, {
        documentId,
        pageNumber,
        textItems: textContent.items.length,
      });

      // Set up text rendering properties with enhanced settings
      context.textBaseline = 'alphabetic';
      context.fillStyle = SERVER_PDF_CONFIG.TEXT_COLOR;
      context.strokeStyle = SERVER_PDF_CONFIG.TEXT_COLOR;
      context.lineWidth = 0.5;

      // Enhanced text rendering settings
      if (context.textRenderingOptimization) {
        context.textRenderingOptimization = 'optimizeQuality';
      }

      let renderedCount = 0;
      const renderedPositions = new Set(); // Track rendered positions to prevent overlaps

      // Render each text item with enhanced positioning and overlap prevention
      for (const item of textContent.items) {
        if (!item.str || item.str.trim().length === 0) continue;

        try {
          // Transform coordinates from PDF space to canvas space
          const transform = item.transform;
          if (!transform || transform.length < 6) continue;

          // Extract position and scaling from transform matrix
          const scaleX = transform[0];
          const scaleY = transform[3];
          const x = transform[4];
          const y = transform[5];

          // Convert PDF coordinates to canvas coordinates with proper handling
          const canvasX = x;
          const canvasY = y; // Don't flip Y coordinate - this was causing mirror text

          // Prevent text overlap by checking if position is already used
          if (SERVER_PDF_CONFIG.PREVENT_TEXT_OVERLAP) {
            const positionKey = `${Math.round(canvasX / 5)}_${Math.round(canvasY / 5)}`;
            if (renderedPositions.has(positionKey)) {
              Logger.debug(`Skipping overlapping text at position ${canvasX},${canvasY}: "${item.str}"`, {
                documentId,
                pageNumber,
              });
              continue;
            }
            renderedPositions.add(positionKey);
          }

          // Enhanced font size calculation with reasonable limits
          const rawFontSize = Math.abs(scaleY) || 12;
          const fontSize = Math.max(Math.min(rawFontSize, 72), 6); // Limit between 6-72px
          const fontFamily = SERVER_PDF_CONFIG.TEXT_RENDERING_FONT;
          context.font = `${fontSize}px ${fontFamily}`;

          // Validate coordinates are within canvas bounds
          if (canvasX < 0 || canvasX > viewport.width || canvasY < 0 || canvasY > viewport.height) {
            Logger.debug(`Skipping text outside canvas bounds: "${item.str}" at ${canvasX},${canvasY}`, {
              documentId,
              pageNumber,
            });
            continue;
          }

          // Save context for transform operations
          context.save();

          // Fixed rendering approach to prevent mirrored text
          // Always use simple positioning to avoid coordinate system issues

          // Validate coordinates are reasonable
          if (canvasX >= 0 && canvasX <= viewport.width &&
              canvasY >= 0 && canvasY <= viewport.height) {

            // Set font size based on transform but keep it reasonable
            const fontSize = Math.max(Math.min(Math.abs(scaleY) || 12, 48), 8);
            context.font = `${fontSize}px ${SERVER_PDF_CONFIG.TEXT_RENDERING_FONT}`;

            // Simple text rendering without complex transforms
            context.fillText(item.str, canvasX, canvasY);

            Logger.debug(`Rendered text: "${item.str}" at ${canvasX},${canvasY}`, {
              documentId,
              pageNumber,
              fontSize,
            });
          } else {
            Logger.debug(`Skipping text outside bounds: "${item.str}" at ${canvasX},${canvasY}`, {
              documentId,
              pageNumber,
              viewportSize: `${viewport.width}x${viewport.height}`,
            });
          }

          context.restore();
          renderedCount++;

        } catch (itemError) {
          Logger.debug(`Failed to render text item: "${item.str}"`, {
            documentId,
            pageNumber,
            error: itemError instanceof Error ? itemError.message : String(itemError),
            transform: item.transform,
          });
        }
      }

      Logger.debug(`Text rendering completed for page ${pageNumber}`, {
        documentId,
        pageNumber,
        totalTextItems: textContent.items.length,
        renderedItems: renderedCount,
        renderingSuccess: renderedCount > 0,
      });

      // Log warning if no text was rendered
      if (renderedCount === 0 && textContent.items.length > 0) {
        Logger.warn(`No text items were successfully rendered for page ${pageNumber}`, {
          documentId,
          pageNumber,
          totalItems: textContent.items.length,
          suggestion: "Check text transform matrices and positioning",
        });
      }

    } catch (error) {
      Logger.warn(`Failed to manually render text for page ${pageNumber}`, {
        documentId,
        pageNumber,
        error: error instanceof Error ? error.message : String(error),
      });
    }
  }

  /**
   * Try alternative rendering approaches for blank pages
   * @param page PDF page object
   * @param context Canvas context
   * @param viewport Page viewport
   * @param pageNumber Page number
   * @param documentId Document ID for logging
   */
  private static async tryAlternativeRendering(
    page: any,
    context: any,
    viewport: any,
    pageNumber: number,
    documentId: string
  ): Promise<void> {
    try {
      Logger.info(`Trying alternative rendering for blank page ${pageNumber}`, {
        documentId,
        pageNumber,
      });

      // Clear canvas and fill with white background again
      context.fillStyle = 'white';
      context.fillRect(0, 0, viewport.width, viewport.height);

      // Try rendering with different settings - focus on text rendering
      const alternativeRenderContext = {
        canvasContext: context,
        viewport: viewport,
        // Try with text layers enabled (opposite of main approach)
        renderTextLayer: true,
        renderAnnotationLayer: true,
        renderImageLayer: true,
        background: '#FFFFFF',
        renderInteractiveForms: true,
        enableWebGL: false,
        optionalContentConfigPromise: null,
        // Force font rendering
        fontExtraProperties: true,
      };

      await page.render(alternativeRenderContext).promise;

      // Also try manual text rendering with alternative approach
      await this.renderTextToCanvas(page, context, viewport, documentId, pageNumber);

      // Check if alternative rendering worked
      const imageData = context.getImageData(0, 0, viewport.width, viewport.height);
      const hasContent = this.checkCanvasHasContent(imageData);

      if (hasContent) {
        Logger.info(`Alternative rendering successful for page ${pageNumber}`, {
          documentId,
          pageNumber,
        });
      } else {
        Logger.warn(`Alternative rendering also failed for page ${pageNumber}`, {
          documentId,
          pageNumber,
          suggestion: "PDF may be image-based or corrupted",
        });

        // Last resort: try to extract any text content directly
        try {
          const textContent = await page.getTextContent();
          if (textContent && textContent.items && textContent.items.length > 0) {
            Logger.info(`Page ${pageNumber} has text content but rendering failed`, {
              documentId,
              pageNumber,
              textItems: textContent.items.length,
              suggestion: "Consider using text extraction instead of image rendering",
            });
          } else {
            Logger.warn(`Page ${pageNumber} has no text content`, {
              documentId,
              pageNumber,
              suggestion: "Page may be image-only or truly blank",
            });
          }
        } catch (textError) {
          Logger.warn(`Could not extract text from page ${pageNumber}`, {
            documentId,
            pageNumber,
            error: textError instanceof Error ? textError.message : String(textError),
          });
        }
      }
    } catch (error) {
      Logger.error(`Alternative rendering failed for page ${pageNumber}`, {
        documentId,
        pageNumber,
        error: error instanceof Error ? error.message : String(error),
      });
    }
  }

  /**
   * Saves debug image - handles both canvas context and base64 image data
   * @param documentId Document ID for logging
   * @param pageNumber Page number
   * @param contextOrImageBase64 Canvas context or base64 image data
   * @param layerNameOrViewport Layer name (string) or viewport (object)
   */
  private static async saveDebugImage(
    documentId: string,
    pageNumber: number,
    contextOrImageBase64: any,
    layerNameOrViewport: string | any
  ): Promise<void> {
    try {
      if (!SERVER_PDF_CONFIG.SAVE_DEBUG_IMAGES) return;

      let imageBase64: string;
      let layerName: string;
      let viewport: any;

      // Handle different function signatures
      if (typeof contextOrImageBase64 === 'string') {
        // Called with (documentId, pageNumber, imageBase64, viewport)
        imageBase64 = contextOrImageBase64;
        viewport = layerNameOrViewport;
        layerName = 'main';
      } else {
        // Called with (documentId, pageNumber, context, layerName)
        const canvas = contextOrImageBase64.canvas;
        imageBase64 = canvas.toDataURL('image/jpeg', SERVER_PDF_CONFIG.JPEG_QUALITY);
        layerName = layerNameOrViewport as string;
        viewport = { width: canvas.width, height: canvas.height };
      }

      const debugDir = path.join(process.cwd(), SERVER_PDF_CONFIG.DEBUG_IMAGE_DIR);
      if (!fs.existsSync(debugDir)) {
        fs.mkdirSync(debugDir, { recursive: true });
      }

      const base64Data = imageBase64.replace(/^data:image\/[^;]+;base64,/, '');
      const imageBuffer = Buffer.from(base64Data, 'base64');

      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const filename = `${documentId}_page${pageNumber}_${layerName}_${timestamp}.jpg`;
      const filepath = path.join(debugDir, filename);

      fs.writeFileSync(filepath, imageBuffer);

      // Save metadata file for main images
      if (layerName === 'main') {
        const metadataFilename = `${documentId}_page${pageNumber}_${timestamp}.json`;
        const metadataFilepath = path.join(debugDir, metadataFilename);
        const metadata = {
          documentId,
          pageNumber,
          timestamp: new Date().toISOString(),
          viewport: {
            width: viewport.width,
            height: viewport.height,
          },
          config: SERVER_PDF_CONFIG,
          imageSize: imageBase64.length,
          fileSizeKB: Math.round(imageBuffer.length / 1024),
        };
        fs.writeFileSync(metadataFilepath, JSON.stringify(metadata, null, 2));
      }

      Logger.debug(`DEBUG: ${layerName} layer saved`, {
        documentId,
        pageNumber,
        layerName,
        filepath,
        fileSizeKB: Math.round(imageBuffer.length / 1024),
        dimensions: `${viewport.width}x${viewport.height}`,
      });
    } catch (error) {
      Logger.error(`DEBUG: Failed to save debug image`, {
        documentId,
        pageNumber,
        error: error instanceof Error ? error.message : String(error),
      });
    }
  }

  /**
   * CRITICAL: Manual text layer rendering using page.getTextContent()
   * This replaces PDF.js text rendering to eliminate garbled overlays
   * @param page PDF page
   * @param context Canvas context
   * @param viewport Page viewport
   * @param documentId Document ID for logging
   * @param pageNumber Page number
   */
  private static async renderManualTextLayer(
    page: any,
    context: any,
    viewport: any,
    documentId: string,
    pageNumber: number
  ): Promise<void> {
    try {
      // Extract text content using PDF.js text extraction
      const textContent = await page.getTextContent();

      if (!textContent || !textContent.items || textContent.items.length === 0) {
        Logger.debug(`No text content found for page ${pageNumber}`, {
          documentId,
          pageNumber
        });
        return;
      }

      Logger.info(`Manual text rendering: ${textContent.items.length} text items for page ${pageNumber}`, {
        documentId,
        pageNumber,
        textItemCount: textContent.items.length
      });

      // Set up clean font rendering with system font
      context.fillStyle = SERVER_PDF_CONFIG.TEXT_COLOR;
      context.strokeStyle = 'transparent'; // No stroke to prevent artifacts
      context.font = `12px ${SERVER_PDF_CONFIG.FALLBACK_FONT}`;
      context.textBaseline = 'alphabetic';
      context.textAlign = 'left';

      let renderedTextItems = 0;

      // Render text items using extracted positions with overlap prevention
      const renderedPositions = new Set<string>();

      for (const item of textContent.items) {
        if (!item.str || item.str.trim().length === 0) continue;

        try {
          const transform = item.transform;
          if (!transform || transform.length < 6) continue;

          // Extract position from transform matrix
          const x = Math.round(transform[4]);
          const y = Math.round(transform[5]);
          const scaleY = Math.abs(transform[3]) || 12;

          // Validate coordinates are within canvas bounds with margin
          if (x < 10 || x > viewport.width - 10 || y < 10 || y > viewport.height - 10) {
            continue;
          }

          // Create position key to prevent overlaps
          const positionKey = `${Math.floor(x/5)}_${Math.floor(y/5)}`;
          if (renderedPositions.has(positionKey)) {
            continue; // Skip overlapping text
          }

          // Set conservative font size
          const fontSize = Math.max(Math.min(scaleY * 0.8, 24), 8); // Reduced max size
          context.font = `${fontSize}px ${SERVER_PDF_CONFIG.FALLBACK_FONT}`;

          // Render text at extracted position
          context.fillText(item.str, x, y);
          renderedPositions.add(positionKey);
          renderedTextItems++;

        } catch (itemError) {
          Logger.debug(`Failed to render text item: "${item.str}"`, {
            documentId,
            pageNumber,
            error: itemError instanceof Error ? itemError.message : String(itemError)
          });
        }
      }

      Logger.info(`Manual text layer rendering completed for page ${pageNumber}`, {
        documentId,
        pageNumber,
        totalTextItems: textContent.items.length,
        renderedTextItems,
        renderingFont: SERVER_PDF_CONFIG.FALLBACK_FONT,
        renderingApproach: 'manual-text-extraction'
      });

      // Save debug image after manual text rendering
      if (SERVER_PDF_CONFIG.SAVE_DEBUG_IMAGES) {
        await this.saveDebugImage(documentId, pageNumber, context, 'manual-text-layer');
      }

    } catch (error) {
      Logger.error(`Manual text layer rendering failed for page ${pageNumber}`, {
        documentId,
        pageNumber,
        error: error instanceof Error ? error.message : String(error)
      });

      // Don't throw error - continue with vector-only rendering
      Logger.warn(`Continuing with vector-only rendering for page ${pageNumber}`, {
        documentId,
        pageNumber
      });
    }
  }

  /**
   * CRITICAL: Reset canvas completely to prevent contamination between page renders
   * This is the key fix for garbled text caused by failed renders affecting subsequent pages
   * @param canvas Canvas element
   * @param context Canvas context
   * @param viewport Page viewport
   */
  private static resetCanvas(canvas: any, context: any, viewport: any): void {
    try {
      // Method 1: Reset canvas dimensions (clears all content and state)
      canvas.width = viewport.width;
      canvas.height = viewport.height;

      // Method 2: Clear entire canvas area
      context.clearRect(0, 0, canvas.width, canvas.height);

      // Method 3: Reset all canvas context properties to defaults
      context.globalAlpha = 1.0;
      context.globalCompositeOperation = 'source-over';
      context.strokeStyle = '#000000';
      context.fillStyle = '#000000';
      context.lineWidth = 1;
      context.lineCap = 'butt';
      context.lineJoin = 'miter';
      context.miterLimit = 10;
      context.shadowOffsetX = 0;
      context.shadowOffsetY = 0;
      context.shadowBlur = 0;
      context.shadowColor = 'rgba(0, 0, 0, 0)';

      // Method 4: Fill with clean white background
      context.fillStyle = 'white';
      context.fillRect(0, 0, canvas.width, canvas.height);

      // Method 5: Reset text rendering properties
      context.font = '10px sans-serif';
      context.textAlign = 'start';
      context.textBaseline = 'alphabetic';
      context.direction = 'inherit';

      // Method 6: Reset transform matrix to identity
      context.setTransform(1, 0, 0, 1, 0, 0);

      Logger.debug('Canvas reset completed', {
        canvasSize: `${canvas.width}x${canvas.height}`,
        fillStyle: context.fillStyle,
        globalAlpha: context.globalAlpha
      });

    } catch (error) {
      Logger.error('Canvas reset failed', {
        error: error instanceof Error ? error.message : String(error),
        canvasWidth: canvas?.width,
        canvasHeight: canvas?.height
      });

      // If reset fails, throw error to prevent using corrupted canvas
      throw new Error(`Canvas reset failed: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Save intermediate rendering layer for debugging
   * @param documentId Document ID for logging
   * @param pageNumber Page number
   * @param context Canvas context
   * @param layerName Name of the layer (e.g., 'vectors', 'text')
   */
  private static async saveIntermediateLayer(
    documentId: string,
    pageNumber: number,
    context: any,
    layerName: string
  ): Promise<void> {
    try {
      if (!SERVER_PDF_CONFIG.SAVE_INTERMEDIATE_LAYERS) return;

      await this.saveDebugImage(documentId, pageNumber, context, `layer-${layerName}`);

      Logger.debug(`Intermediate layer saved: ${layerName}`, {
        documentId,
        pageNumber,
        layerName
      });
    } catch (error) {
      Logger.warn(`Failed to save intermediate layer: ${layerName}`, {
        documentId,
        pageNumber,
        layerName,
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  /**
   * Render text layer with precise alignment over vector/table layer
   * Uses consistent viewport and transform matrix application for perfect alignment
   * @param textContent Text content from page.getTextContent()
   * @param context Canvas context
   * @param viewport Consistent viewport (same as vector layer)
   * @param documentId Document ID for logging
   * @param pageNumber Page number
   */
  private static async renderAlignedTextLayer(
    textContent: any,
    context: any,
    viewport: any,
    documentId: string,
    pageNumber: number
  ): Promise<void> {
    try {
      Logger.info(`Starting aligned text layer rendering for page ${pageNumber}`, {
        documentId,
        pageNumber,
        textItems: textContent.items.length,
        viewportSize: `${viewport.width}x${viewport.height}`
      });

      // Set up consistent text rendering properties
      context.textBaseline = 'alphabetic';
      context.fillStyle = SERVER_PDF_CONFIG.TEXT_COLOR;
      context.strokeStyle = 'transparent'; // No stroke to prevent artifacts
      context.font = `12px ${SERVER_PDF_CONFIG.FALLBACK_FONT}`;
      context.textAlign = 'left';

      let renderedTextItems = 0;
      let debugTransforms: any[] = [];

      // Render text items with precise transform matrix application
      for (const item of textContent.items) {
        if (!item.str || item.str.trim().length === 0) continue;

        try {
          const transform = item.transform;
          if (!transform || transform.length < 6) continue;

          // Extract transform matrix components
          const [scaleX, skewY, skewX, scaleY, translateX, translateY] = transform;

          // Calculate position with proper transform application
          const x = translateX;
          const y = translateY;
          const fontSize = Math.abs(scaleY) || 12;

          // Validate coordinates are within canvas bounds
          if (x < 0 || x > viewport.width || y < 0 || y > viewport.height) {
            continue;
          }

          // Save current context state
          context.save();

          // Apply transform matrix for precise alignment
          context.setTransform(scaleX, skewY, skewX, scaleY, translateX, translateY);

          // Set font size based on transform scale
          const adjustedFontSize = Math.max(Math.min(fontSize, 48), 8);
          context.font = `${adjustedFontSize}px ${SERVER_PDF_CONFIG.FALLBACK_FONT}`;

          // Render text at origin (0,0) since transform is applied
          context.fillText(item.str, 0, 0);

          // Restore context state
          context.restore();

          // Debug transform information
          if (SERVER_PDF_CONFIG.SAVE_TRANSFORM_DEBUG) {
            debugTransforms.push({
              text: item.str,
              transform: transform,
              position: { x, y },
              fontSize: adjustedFontSize,
              bounds: { width: viewport.width, height: viewport.height }
            });
          }

          renderedTextItems++;

        } catch (itemError) {
          Logger.debug(`Failed to render aligned text item: "${item.str}"`, {
            documentId,
            pageNumber,
            error: itemError instanceof Error ? itemError.message : String(itemError)
          });
        }
      }

      // Save transform debug information
      if (SERVER_PDF_CONFIG.SAVE_TRANSFORM_DEBUG && debugTransforms.length > 0) {
        await this.saveTransformDebugInfo(documentId, pageNumber, debugTransforms);
      }

      Logger.info(`Aligned text layer rendering completed for page ${pageNumber}`, {
        documentId,
        pageNumber,
        totalTextItems: textContent.items.length,
        renderedTextItems,
        renderingFont: SERVER_PDF_CONFIG.FALLBACK_FONT,
        transformsDebugged: debugTransforms.length
      });

    } catch (error) {
      Logger.error(`Aligned text layer rendering failed for page ${pageNumber}`, {
        documentId,
        pageNumber,
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * Save transform matrix debug information for alignment verification
   * @param documentId Document ID
   * @param pageNumber Page number
   * @param debugTransforms Array of transform debug info
   */
  private static async saveTransformDebugInfo(
    documentId: string,
    pageNumber: number,
    debugTransforms: any[]
  ): Promise<void> {
    try {
      const debugDir = path.join(process.cwd(), SERVER_PDF_CONFIG.DEBUG_IMAGE_DIR);
      if (!fs.existsSync(debugDir)) {
        fs.mkdirSync(debugDir, { recursive: true });
      }

      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const filename = `${documentId}_page${pageNumber}_transforms_${timestamp}.json`;
      const filepath = path.join(debugDir, filename);

      const debugData = {
        documentId,
        pageNumber,
        timestamp: new Date().toISOString(),
        config: {
          scaleFactorUsed: SERVER_PDF_CONFIG.SCALE_FACTOR,
          targetDPI: SERVER_PDF_CONFIG.TARGET_DPI,
          fallbackFont: SERVER_PDF_CONFIG.FALLBACK_FONT
        },
        transforms: debugTransforms,
        summary: {
          totalTransforms: debugTransforms.length,
          averageFontSize: debugTransforms.reduce((sum, t) => sum + t.fontSize, 0) / debugTransforms.length,
          textBounds: {
            minX: Math.min(...debugTransforms.map(t => t.position.x)),
            maxX: Math.max(...debugTransforms.map(t => t.position.x)),
            minY: Math.min(...debugTransforms.map(t => t.position.y)),
            maxY: Math.max(...debugTransforms.map(t => t.position.y))
          }
        }
      };

      fs.writeFileSync(filepath, JSON.stringify(debugData, null, 2));

      Logger.info(`Transform debug info saved for page ${pageNumber}`, {
        documentId,
        pageNumber,
        filepath,
        transformCount: debugTransforms.length
      });

    } catch (error) {
      Logger.warn(`Failed to save transform debug info for page ${pageNumber}`, {
        documentId,
        pageNumber,
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }
}
