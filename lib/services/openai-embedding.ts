import { <PERSON><PERSON><PERSON> } from "openai";
import { Logger } from "../utils/Logger";
import { db } from "../db";
import { resources, embeddings } from "../db/schema";
import { InlineCitationsChunk } from "./inline-citations-chunker";

// Configuration for OpenAI embedding
const OPENAI_EMBEDDING_CONFIG = {
  MODEL: "text-embedding-3-small",
  OUTPUT_DIMENSIONALITY: 1024,
  BATCH_SIZE: 10, // Process chunks in batches to avoid rate limits
  MAX_RETRIES: 3,
  RETRY_DELAY: 1000, // 1 second
} as const;

export interface EmbeddingResult {
  success: boolean;
  processedChunks: number;
  totalEmbeddings: number;
  averageVectorLength: number;
  error?: string;
}

/**
 * OpenAI Embedding Service for Inline Citations
 * Handles embedding generation and storage for the inline citations pipeline
 */
export class OpenAIEmbeddingService {
  private static openai = new OpenAI({
    apiKey: process.env.OPENAI_API_KEY,
  });

  /**
   * Main entry point for generating embeddings and storing chunks
   * @param chunks Array of inline citations chunks to process
   * @returns Embedding result with statistics
   */
  static async generateAndStoreEmbeddings(
    chunks: InlineCitationsChunk[]
  ): Promise<EmbeddingResult> {
    try {
      Logger.info("Starting OpenAI embedding generation for inline citations", {
        totalChunks: chunks.length,
        model: OPENAI_EMBEDDING_CONFIG.MODEL,
        outputDimensionality: OPENAI_EMBEDDING_CONFIG.OUTPUT_DIMENSIONALITY,
      });

      if (chunks.length === 0) {
        return {
          success: true,
          processedChunks: 0,
          totalEmbeddings: 0,
          averageVectorLength: 0,
        };
      }

      let totalProcessed = 0;
      let totalEmbeddings = 0;
      let totalVectorLength = 0;

      // Process chunks in batches to avoid rate limits
      for (let i = 0; i < chunks.length; i += OPENAI_EMBEDDING_CONFIG.BATCH_SIZE) {
        const batch = chunks.slice(i, i + OPENAI_EMBEDDING_CONFIG.BATCH_SIZE);
        
        Logger.debug("Processing embedding batch", {
          batchNumber: Math.floor(i / OPENAI_EMBEDDING_CONFIG.BATCH_SIZE) + 1,
          batchSize: batch.length,
          totalBatches: Math.ceil(chunks.length / OPENAI_EMBEDDING_CONFIG.BATCH_SIZE),
        });

        const batchResult = await this.processBatch(batch);
        
        if (batchResult.success) {
          totalProcessed += batchResult.processedChunks;
          totalEmbeddings += batchResult.totalEmbeddings;
          totalVectorLength += batchResult.totalVectorLength;
        } else {
          Logger.error("Batch processing failed", {
            batchNumber: Math.floor(i / OPENAI_EMBEDDING_CONFIG.BATCH_SIZE) + 1,
            error: batchResult.error,
          });
        }

        // Add delay between batches to respect rate limits
        if (i + OPENAI_EMBEDDING_CONFIG.BATCH_SIZE < chunks.length) {
          await new Promise(resolve => setTimeout(resolve, 100));
        }
      }

      const averageVectorLength = totalEmbeddings > 0 ? totalVectorLength / totalEmbeddings : 0;

      Logger.info("OpenAI embedding generation completed", {
        processedChunks: totalProcessed,
        totalEmbeddings,
        averageVectorLength,
        successRate: `${((totalProcessed / chunks.length) * 100).toFixed(1)}%`,
      });

      return {
        success: totalProcessed > 0,
        processedChunks: totalProcessed,
        totalEmbeddings,
        averageVectorLength,
      };
    } catch (error) {
      Logger.error("Error in OpenAI embedding generation", { error });
      return {
        success: false,
        processedChunks: 0,
        totalEmbeddings: 0,
        averageVectorLength: 0,
        error: error instanceof Error ? error.message : String(error),
      };
    }
  }

  /**
   * Process a batch of chunks for embedding generation
   */
  private static async processBatch(
    chunks: InlineCitationsChunk[]
  ): Promise<{
    success: boolean;
    processedChunks: number;
    totalEmbeddings: number;
    totalVectorLength: number;
    error?: string;
  }> {
    try {
      let processedChunks = 0;
      let totalEmbeddings = 0;
      let totalVectorLength = 0;

      for (const chunk of chunks) {
        const result = await this.processChunk(chunk);
        if (result.success) {
          processedChunks++;
          totalEmbeddings++;
          totalVectorLength += result.vectorLength;
        }
      }

      return {
        success: processedChunks > 0,
        processedChunks,
        totalEmbeddings,
        totalVectorLength,
      };
    } catch (error) {
      return {
        success: false,
        processedChunks: 0,
        totalEmbeddings: 0,
        totalVectorLength: 0,
        error: error instanceof Error ? error.message : String(error),
      };
    }
  }

  /**
   * Process a single chunk for embedding generation and storage
   */
  private static async processChunk(
    chunk: InlineCitationsChunk
  ): Promise<{ success: boolean; vectorLength: number }> {
    let retries = 0;
    
    while (retries < OPENAI_EMBEDDING_CONFIG.MAX_RETRIES) {
      try {
        // Generate embedding using OpenAI
        const embeddingResult = await this.generateEmbedding(chunk.text);
        const embedding = embeddingResult.data[0].embedding;

        if (!embedding || !Array.isArray(embedding)) {
          throw new Error("Invalid embedding response from OpenAI");
        }

        // Validate embedding dimensions
        if (embedding.length !== OPENAI_EMBEDDING_CONFIG.OUTPUT_DIMENSIONALITY) {
          Logger.warn("Unexpected embedding dimension", {
            expected: OPENAI_EMBEDDING_CONFIG.OUTPUT_DIMENSIONALITY,
            actual: embedding.length,
            chunkId: chunk.id,
          });
        }

        // Verify vector normalization (OpenAI embeddings should be L₂-normalized)
        const magnitude = Math.sqrt(embedding.reduce((sum, val) => sum + val * val, 0));
        const isNormalized = Math.abs(magnitude - 1.0) < 0.001; // Allow small floating point errors

        if (!isNormalized) {
          Logger.warn("Embedding vector is not L₂-normalized - normalizing manually", {
            chunkId: chunk.id,
            magnitude: magnitude.toFixed(6),
            expected: "~1.0",
            chunkPreview: chunk.text.substring(0, 100)
          });

          // Normalize the vector manually if needed
          const normalizedEmbedding = embedding.map(val => val / magnitude);

          // Verify the normalization worked
          const newMagnitude = Math.sqrt(normalizedEmbedding.reduce((sum, val) => sum + val * val, 0));

          Logger.info("Manually normalized embedding vector", {
            chunkId: chunk.id,
            originalMagnitude: magnitude.toFixed(6),
            newMagnitude: newMagnitude.toFixed(6)
          });

          // Use the normalized embedding
          embedding.splice(0, embedding.length, ...normalizedEmbedding);
        }

        // Store in database using the same pattern as Gemini service
        await this.storeChunkWithEmbedding(chunk, embedding);

        Logger.debug("Chunk embedding stored successfully", {
          chunkId: chunk.id,
          embeddingLength: embedding.length,
          pageNumber: chunk.metadata.page_number,
        });

        // Return the final magnitude (1.0 if normalized, original if already normalized)
        const finalMagnitude = isNormalized ? magnitude : 1.0;
        return { success: true, vectorLength: finalMagnitude };
      } catch (error) {
        retries++;
        Logger.warn("Chunk processing failed, retrying", {
          chunkId: chunk.id,
          attempt: retries,
          maxRetries: OPENAI_EMBEDDING_CONFIG.MAX_RETRIES,
          error: error instanceof Error ? error.message : String(error),
        });

        if (retries >= OPENAI_EMBEDDING_CONFIG.MAX_RETRIES) {
          Logger.error("Chunk processing failed after max retries", {
            chunkId: chunk.id,
            error,
          });
          return { success: false, vectorLength: 0 };
        }

        // Exponential backoff
        await new Promise(resolve => 
          setTimeout(resolve, OPENAI_EMBEDDING_CONFIG.RETRY_DELAY * Math.pow(2, retries - 1))
        );
      }
    }

    return { success: false, vectorLength: 0 };
  }

  /**
   * Stores a chunk with its embedding in the database
   */
  private static async storeChunkWithEmbedding(
    chunk: InlineCitationsChunk,
    embedding: number[]
  ): Promise<void> {
    try {
      Logger.debug("Storing chunk with embedding", {
        chunkId: chunk.id,
        documentId: chunk.metadata.document_id,
        contentLength: chunk.text.length,
        embeddingLength: embedding.length,
      });

      // Validate embedding before storing
      if (!Array.isArray(embedding) || embedding.length !== OPENAI_EMBEDDING_CONFIG.OUTPUT_DIMENSIONALITY) {
        throw new Error(`Invalid embedding: expected array of length ${OPENAI_EMBEDDING_CONFIG.OUTPUT_DIMENSIONALITY}, got ${embedding?.length || 'undefined'}`);
      }

      // Verify all values are finite numbers
      const invalidValues = embedding.filter(val => !Number.isFinite(val));
      if (invalidValues.length > 0) {
        throw new Error(`Embedding contains invalid values: ${invalidValues.slice(0, 5).join(', ')}${invalidValues.length > 5 ? '...' : ''}`);
      }

      await db.transaction(async (tx) => {
        // Insert the resource (chunk content) first
        const [resourceRow] = await tx
          .insert(resources)
          .values({
            content: chunk.text,
            sourceDocumentId: chunk.metadata.document_id,
          })
          .returning();

        if (!resourceRow?.id) {
          throw new Error("Failed to create resource record");
        }

        // Then insert the embedding
        await tx.insert(embeddings).values({
          resourceId: resourceRow.id,
          content: chunk.text,
          embedding: embedding,
        });

        Logger.debug("Chunk and embedding stored successfully", {
          chunkId: chunk.id,
          resourceId: resourceRow.id,
          embeddingLength: embedding.length,
          metadata: chunk.metadata,
        });
      });
    } catch (error) {
      Logger.error("Error storing chunk with embedding", {
        chunkId: chunk.id,
        documentId: chunk.metadata.document_id,
        error: error instanceof Error ? error.message : String(error),
        embeddingLength: embedding?.length || 'undefined',
        chunkTextLength: chunk.text?.length || 'undefined',
      });
      throw error;
    }
  }

  /**
   * Generates embedding using OpenAI API
   */
  private static async generateEmbedding(text: string) {
    try {
      const response = await this.openai.embeddings.create({
        model: OPENAI_EMBEDDING_CONFIG.MODEL,
        input: text,
        dimensions: OPENAI_EMBEDDING_CONFIG.OUTPUT_DIMENSIONALITY,
      });

      return response;
    } catch (error) {
      Logger.error("OpenAI embedding API error", { error });
      throw error;
    }
  }

  /**
   * Generates a single embedding for a query (for search purposes)
   */
  static async generateQueryEmbedding(query: string): Promise<number[]> {
    try {
      const response = await this.generateEmbedding(query);
      const embedding = response.data[0].embedding;

      if (!embedding || !Array.isArray(embedding)) {
        throw new Error("Invalid embedding response from OpenAI");
      }

      // Verify vector normalization (OpenAI embeddings should be L₂-normalized)
      const magnitude = Math.sqrt(embedding.reduce((sum, val) => sum + val * val, 0));
      const isNormalized = Math.abs(magnitude - 1.0) < 0.001; // Allow small floating point errors

      Logger.debug("Query embedding generated", {
        queryLength: query.length,
        embeddingLength: embedding.length,
        magnitude: magnitude.toFixed(6),
        isNormalized,
      });

      if (!isNormalized) {
        Logger.warn("Query embedding is not L₂-normalized - normalizing manually", {
          magnitude: magnitude.toFixed(6),
          expected: "~1.0",
          query: query.substring(0, 100),
        });

        // Manually normalize the vector to ensure L₂ normalization
        const normalizedEmbedding = embedding.map(val => val / magnitude);
        const newMagnitude = Math.sqrt(normalizedEmbedding.reduce((sum, val) => sum + val * val, 0));

        Logger.info("Query embedding manually normalized", {
          originalMagnitude: magnitude.toFixed(6),
          newMagnitude: newMagnitude.toFixed(6),
          queryPreview: query.substring(0, 100)
        });

        return normalizedEmbedding;
      }

      return embedding;
    } catch (error) {
      Logger.error("Error generating query embedding", { error, query: query.substring(0, 100) });
      throw error;
    }
  }
}
