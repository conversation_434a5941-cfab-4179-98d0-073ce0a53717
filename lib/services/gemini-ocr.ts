import { Logger } from "../utils/Logger";
import { GoogleGenerativeAI } from "@google/generative-ai";
import { getGooglePalmApiKey } from "../utils/apiKeyRotation";
import * as fs from 'fs';
import * as path from 'path';

export interface DocumentPage {
  pageNumber: number;
  imageBase64: string;
}

export interface OCRResult {
  success: boolean;
  markdown: string;
  pagesProcessed: number;
  error?: string;
}

export class GeminiOCRService {
  private static genAI: GoogleGenerativeAI | null = null;

  private static getGenAI(): GoogleGenerativeAI {
    if (!this.genAI) {
      this.genAI = new GoogleGenerativeAI(getGooglePalmApiKey());
    }
    return this.genAI;
  }

  /**
   * Processes multiple document pages with Gemini 2.0 Flash Lite using a single system prompt
   * @param pages Array of document pages with base64 images
   * @param documentId Document ID for logging
   * @returns OCR result with combined markdown
   */
  static async processDocumentPages(
    pages: DocumentPage[],
    documentId: string
  ): Promise<OCRResult> {
    try {
      Logger.info("Starting Gemini OCR processing", {
        documentId,
        pageCount: pages.length,
      });

      if (pages.length === 0) {
        return {
          success: false,
          markdown: "",
          pagesProcessed: 0,
          error: "No pages to process",
        };
      }

      const model = this.getGenAI().getGenerativeModel({
        model: "gemini-2.0-flash-lite-preview-02-05",
        systemInstruction: `You are an expert OCR system that converts PDF page images into high-quality markdown. Your goal is to extract ALL visible text and data with perfect accuracy.

CRITICAL REQUIREMENTS:
1. Extract EVERY piece of visible text - no matter how small, faint, or difficult to read
2. If you see ANY text, numbers, or symbols, include them in the output
3. NEVER output "This page is blank" unless the page is completely white with zero content
4. NEVER create empty tables - if you see a table structure, extract the actual data

TEXT EXTRACTION RULES:
- Read ALL text carefully, including headers, footers, page numbers, watermarks
- Include text that appears faint, small, or partially obscured
- If text is unclear, make your best guess and add [unclear] notation
- Preserve original formatting, spacing, and line breaks where meaningful

TABLE EXTRACTION RULES:
- Extract ALL data from tables, not just the structure
- If cells appear empty, write "empty" or "-" rather than leaving blank
- Include table headers, row labels, and all cell contents
- Maintain table alignment and structure in markdown format

IMAGE/CHART DESCRIPTIONS:
- Describe charts, graphs, diagrams in detail
- Include all visible text within images (labels, titles, legends)
- Extract data points, trends, and key information from visual elements

FORMATTING GUIDELINES:
- Use proper markdown headers (# ## ###) for document structure
- Preserve bullet points, numbered lists, and indentation
- Include page breaks as horizontal rules (---)
- Keep formatting clean but comprehensive

QUALITY CHECKS:
- If output seems too short, re-examine the image more carefully
- Look for text in margins, corners, headers, and footers
- Check for overlaid text, watermarks, or stamps
- Ensure no visible content is missed

Remember: The goal is comprehensive text extraction for search and analysis. Missing content is worse than including uncertain text with notation.`
      });

      // Prepare parts for the API call - include all images
      const parts: any[] = [];
      
      // Add instruction text
      parts.push({
        text: `Please convert these ${pages.length} document page(s) into structured markdown. Process them in order and combine into a single markdown document.`,
      });

      // Add all page images
      pages.forEach((page, index) => {
        // Remove data URL prefix if present
        const base64Data = page.imageBase64.replace(/^data:image\/[^;]+;base64,/, '');
        
        parts.push({
          inlineData: {
            mimeType: "image/jpeg",
            data: base64Data,
          },
        });
        
        // Add page separator text
        if (index < pages.length - 1) {
          parts.push({
            text: `--- End of Page ${page.pageNumber} ---`,
          });
        }
      });

      // DEBUG: Analyze image quality before sending to Gemini
      this.analyzeImageQuality(documentId, pages);

      Logger.debug("Sending request to Gemini with parts", {
        documentId,
        partsCount: parts.length,
        imageCount: pages.length,
      });

      // Make the API call
      const result = await model.generateContent({
        contents: [{ role: "user", parts }],
      });

      const response = result.response;
      const markdown = response.text();

      if (!markdown || markdown.trim().length === 0) {
        Logger.error("Empty response from Gemini OCR", { documentId });
        return {
          success: false,
          markdown: "",
          pagesProcessed: 0,
          error: "Empty response from Gemini",
        };
      }

      // DEBUG: Save markdown to file for inspection
      await this.saveMarkdownForDebugging(documentId, markdown);

      // DEBUG: Log detailed markdown analysis
      this.logMarkdownAnalysis(documentId, markdown);

      // DEBUG: Check for poor quality OCR output
      this.detectPoorOCRQuality(documentId, markdown, pages.length);

      Logger.info("Gemini OCR processing completed successfully", {
        documentId,
        pagesProcessed: pages.length,
        markdownLength: markdown.length,
      });

      return {
        success: true,
        markdown: markdown.trim(),
        pagesProcessed: pages.length,
      };
    } catch (error) {
      Logger.error("Error in Gemini OCR processing", {
        documentId,
        error: error,
        pageCount: pages.length,
      });

      return {
        success: false,
        markdown: "",
        pagesProcessed: 0,
        error: error instanceof Error ? error.message : "Unknown error",
      };
    }
  }

  /**
   * Validates the generated markdown content
   * @param markdown The markdown content to validate
   * @returns True if valid, false otherwise
   */
  static validateMarkdown(markdown: string): boolean {
    if (!markdown || markdown.trim().length === 0) {
      return false;
    }

    // Basic validation - check for some markdown elements
    const hasContent = markdown.length > 10;
    const hasValidStructure = markdown.includes('\n') || markdown.length < 1000;

    return hasContent && hasValidStructure;
  }

  /**
   * DEBUG: Save markdown to file for debugging purposes
   * @param documentId Document ID for filename
   * @param markdown The markdown content to save
   */
  private static async saveMarkdownForDebugging(
    documentId: string,
    markdown: string
  ): Promise<void> {
    try {
      // Create debug directory if it doesn't exist
      const debugDir = path.join(process.cwd(), 'debug-markdown');
      if (!fs.existsSync(debugDir)) {
        fs.mkdirSync(debugDir, { recursive: true });
      }

      // Save markdown with timestamp
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const filename = `${documentId}_${timestamp}.md`;
      const filepath = path.join(debugDir, filename);

      fs.writeFileSync(filepath, markdown, 'utf8');

      Logger.info("DEBUG: Markdown saved for inspection", {
        documentId,
        filepath,
        markdownLength: markdown.length,
      });
    } catch (error) {
      Logger.error("DEBUG: Failed to save markdown for debugging", {
        documentId,
        error,
      });
    }
  }

  /**
   * DEBUG: Log detailed analysis of the markdown content
   * @param documentId Document ID for logging
   * @param markdown The markdown content to analyze
   */
  private static logMarkdownAnalysis(
    documentId: string,
    markdown: string
  ): void {
    try {
      const lines = markdown.split('\n');
      const nonEmptyLines = lines.filter(line => line.trim().length > 0);
      const headings = lines.filter(line => line.trim().startsWith('#'));
      const tables = lines.filter(line => line.includes('|'));
      const codeBlocks = markdown.match(/```[\s\S]*?```/g) || [];

      // Sample first few lines for inspection
      const sampleLines = lines.slice(0, 10).map(line =>
        line.length > 100 ? line.substring(0, 100) + '...' : line
      );

      Logger.info("DEBUG: Markdown content analysis", {
        documentId,
        totalLines: lines.length,
        nonEmptyLines: nonEmptyLines.length,
        headingCount: headings.length,
        tableLineCount: tables.length,
        codeBlockCount: codeBlocks.length,
        characterCount: markdown.length,
        wordCount: markdown.split(/\s+/).length,
        sampleLines: sampleLines,
        firstHeadings: headings.slice(0, 5),
      });

      // Log potential issues
      if (markdown.length < 100) {
        Logger.warn("DEBUG: Markdown content seems very short", {
          documentId,
          length: markdown.length,
          content: markdown.substring(0, 200),
        });
      }

      if (nonEmptyLines.length < 5) {
        Logger.warn("DEBUG: Very few non-empty lines in markdown", {
          documentId,
          nonEmptyLines: nonEmptyLines.length,
          allLines: lines,
        });
      }
    } catch (error) {
      Logger.error("DEBUG: Failed to analyze markdown content", {
        documentId,
        error,
      });
    }
  }

  /**
   * DEBUG: Detect poor quality OCR output and suggest fixes
   * @param documentId Document ID for logging
   * @param markdown The markdown content to analyze
   * @param pageCount Number of pages processed
   */
  private static detectPoorOCRQuality(
    documentId: string,
    markdown: string,
    pageCount: number
  ): void {
    try {
      const issues: string[] = [];
      const suggestions: string[] = [];

      // Check for empty or minimal content
      const meaningfulContent = markdown.replace(/[#\-\|\s\n]/g, '').trim();
      if (meaningfulContent.length < 50) {
        issues.push("Very little meaningful content extracted");
        suggestions.push("Check PDF image quality and rendering");
        suggestions.push("Verify PDF contains actual text (not just images)");
      }

      // Check for empty tables
      const emptyTablePattern = /\|\s*\|\s*\|\s*\|/g;
      const emptyTableMatches = markdown.match(emptyTablePattern) || [];
      if (emptyTableMatches.length > 3) {
        issues.push(`Found ${emptyTableMatches.length} empty table rows`);
        suggestions.push("PDF may contain complex tables that need better OCR");
        suggestions.push("Consider adjusting image resolution or OCR prompt");
      }

      // Check for "blank page" indicators
      if (markdown.toLowerCase().includes("blank") || markdown.toLowerCase().includes("empty")) {
        issues.push("OCR detected blank or empty pages");
        suggestions.push("Verify PDF pages contain visible content");
        suggestions.push("Check PDF rendering quality");
      }

      // Check content-to-page ratio
      const avgContentPerPage = meaningfulContent.length / pageCount;
      if (avgContentPerPage < 20) {
        issues.push(`Very low content per page (${avgContentPerPage.toFixed(1)} chars/page)`);
        suggestions.push("PDF may be image-based or have poor quality");
        suggestions.push("Consider using higher resolution for PDF rendering");
      }

      // Check for repetitive patterns (indicating OCR confusion)
      const words = markdown.split(/\s+/).filter(w => w.length > 2);
      const uniqueWords = new Set(words);
      const uniqueRatio = uniqueWords.size / Math.max(words.length, 1);
      if (uniqueRatio < 0.3 && words.length > 10) {
        issues.push(`Low word diversity (${(uniqueRatio * 100).toFixed(1)}% unique)`);
        suggestions.push("OCR may be misreading repetitive patterns");
        suggestions.push("Check for image artifacts or poor scan quality");
      }

      if (issues.length > 0) {
        Logger.error("DEBUG: Poor OCR quality detected", {
          documentId,
          pageCount,
          issues,
          suggestions,
          stats: {
            totalLength: markdown.length,
            meaningfulLength: meaningfulContent.length,
            avgContentPerPage,
            uniqueWordRatio: uniqueRatio,
            emptyTableRows: emptyTableMatches.length,
          },
        });
      } else {
        Logger.info("DEBUG: OCR quality appears acceptable", {
          documentId,
          pageCount,
          meaningfulContentLength: meaningfulContent.length,
          avgContentPerPage,
        });
      }
    } catch (error) {
      Logger.error("DEBUG: Failed to analyze OCR quality", {
        documentId,
        error,
      });
    }
  }

  /**
   * DEBUG: Analyze image quality to help diagnose OCR issues
   * @param documentId Document ID for logging
   * @param pages Array of document pages with images
   */
  private static analyzeImageQuality(
    documentId: string,
    pages: DocumentPage[]
  ): void {
    try {
      const imageAnalysis = pages.map((page, index) => {
        const base64Data = page.imageBase64.replace(/^data:image\/[^;]+;base64,/, '');

        // Basic analysis of base64 data
        const dataSize = base64Data.length;
        const estimatedFileSize = (dataSize * 3) / 4; // Rough estimate of original file size

        // Check for very small images (likely poor quality)
        const isLikelyPoorQuality = estimatedFileSize < 50000; // Less than 50KB
        const isLikelyGoodQuality = estimatedFileSize > 200000; // More than 200KB

        return {
          pageNumber: page.pageNumber,
          base64Length: dataSize,
          estimatedFileSizeKB: Math.round(estimatedFileSize / 1024),
          qualityAssessment: isLikelyPoorQuality ? 'poor' : isLikelyGoodQuality ? 'good' : 'medium',
        };
      });

      const avgFileSize = imageAnalysis.reduce((sum, img) => sum + img.estimatedFileSizeKB, 0) / imageAnalysis.length;
      const poorQualityCount = imageAnalysis.filter(img => img.qualityAssessment === 'poor').length;

      Logger.info("DEBUG: Image quality analysis", {
        documentId,
        pageCount: pages.length,
        averageFileSizeKB: Math.round(avgFileSize),
        poorQualityPages: poorQualityCount,
        imageAnalysis: imageAnalysis,
      });

      // Log warnings for potential quality issues
      if (poorQualityCount > 0) {
        Logger.warn("DEBUG: Detected potentially poor quality images", {
          documentId,
          poorQualityPages: poorQualityCount,
          totalPages: pages.length,
          suggestion: "Consider increasing PDF rendering resolution or checking source PDF quality",
        });
      }

      if (avgFileSize < 100) {
        Logger.warn("DEBUG: Average image size is very small", {
          documentId,
          avgFileSizeKB: Math.round(avgFileSize),
          suggestion: "Images may be too small for effective OCR - consider higher resolution rendering",
        });
      }
    } catch (error) {
      Logger.error("DEBUG: Failed to analyze image quality", {
        documentId,
        error,
      });
    }
  }
}
