import { VertexAI, Part } from "@google-cloud/vertexai";
import { Logger } from "@/lib/utils/Logger";

// Map of Gemini model identifiers to the specific Vertex AI model identifier
const MODEL_MAPPING: Record<string, string> = {
  // Both identifiers now point to your desired Vertex model
  "gemini-2.0-flash-thinking-exp": "gemini-2.5-pro-preview-03-25",
  "gemini-2.5-pro-exp-03-25": "gemini-2.5-pro-preview-03-25",
};

/**
 * Normalizes the parts array for compatibility with the Vertex AI SDK.
 * Specifically, it converts `inlineData.mime_type` to `inlineData.mimeType`.
 */
function normalizePartsForVertex(parts: any[]): Part[] {
  return parts.map(part => {
    if (part.inlineData && part.inlineData.mime_type) {
      return {
        inlineData: {
          mimeType: part.inlineData.mime_type, // Corrected to camelCase
          data: part.inlineData.data,
        },
      };
    }
    return part;
  });
}

export class VertexAIService {
  private static getVertexAI() {
    let credentials;
    try {
      credentials = JSON.parse(process.env.VERTEXAI_CREDENTIALS_JSON || '{}');
    } catch (error) {
      Logger.error("Failed to parse Vertex AI credentials:", error);
      throw new Error("Invalid Vertex AI credentials format");
    }

    if (!credentials || !credentials.project_id) {
      throw new Error("Missing or invalid Vertex AI credentials");
    }
    return new VertexAI({
      project: credentials.project_id,
      location: process.env.VERTEX_LOCATION || 'us-central1',
      googleAuthOptions: {
        credentials,
        scopes: ['https://www.googleapis.com/auth/cloud-platform']
      }
    });
  }

  static getGenerativeModel(options: {
    model: string,
    systemInstruction?: string
  }) {
    const { model: modelId, systemInstruction } = options;
    
    // The default model is now updated to your specification
    const vertexModelId = MODEL_MAPPING[modelId] || "gemini-2.5-pro-preview-03-25";
    const vertexAI = this.getVertexAI();

    const generativeModel = vertexAI.getGenerativeModel({
      model: vertexModelId,
      systemInstruction: systemInstruction,
    });

    return {
      generateContent: async (request: { contents: any[] }) => {
        try {
          // Normalize parts before sending
          const normalizedContents = request.contents.map(content => ({
            ...content,
            parts: normalizePartsForVertex(content.parts),
          }));

          const response = await generativeModel.generateContent({ contents: normalizedContents });
          return { response };
        } catch (error) {
          Logger.error("Error in Vertex AI generateContent:", error);
          throw error;
        }
      },

      generateContentStream: async (request: { contents: any[] }) => {
        try {
           // Normalize parts before sending
          const normalizedContents = request.contents.map(content => ({
            ...content,
            parts: normalizePartsForVertex(content.parts),
          }));

          const streamResponse = await generativeModel.generateContentStream({ contents: normalizedContents });
          
          const stream = (async function* () {
            for await (const chunk of streamResponse.stream) {
              // Ensure candidates and parts exist before accessing text
              const text = chunk.candidates?.[0]?.content?.parts?.[0]?.text ?? "";
              yield {
                text: () => text
              };
            }
          })();

          return {
            stream,
            response: streamResponse.response,
          };
        } catch (error) {
          Logger.error("Error in Vertex AI generateContentStream:", error);
          throw error;
        }
      }
    };
  }
}
