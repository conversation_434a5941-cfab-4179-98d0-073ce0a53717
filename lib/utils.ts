import type {
  CoreAssistantMessage,
  CoreMessage,
  CoreToolMessage,
  Message,
  ToolInvocation,
} from "ai";
import { type ClassValue, clsx } from "clsx";
import { twMerge } from "tailwind-merge";

import type { Message as DBMessage, DocumentType } from "@/lib/db/schema";
import type { ExtendedMessage } from "./types";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

interface ApplicationError extends Error {
  info: string;
  status: number;
}

export const fetcher = async (url: string) => {
  const res = await fetch(url);

  if (!res.ok) {
    const error = new Error(
      "An error occurred while fetching the data."
    ) as ApplicationError;

    error.info = await res.json();
    error.status = res.status;

    throw error;
  }

  return res.json();
};

export function getLocalStorage(key: string) {
  if (typeof window !== "undefined") {
    return JSON.parse(localStorage.getItem(key) || "[]");
  }
  return [];
}

export function generateUUID(): string {
  return "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g, (c) => {
    const r = (Math.random() * 16) | 0;
    const v = c === "x" ? r : (r & 0x3) | 0x8;
    return v.toString(16);
  });
}

function addToolMessageToChat({
  toolMessage,
  messages,
}: {
  toolMessage: CoreToolMessage;
  messages: Array<Message>;
}): Array<Message> {
  return messages.map((message) => {
    if (message.toolInvocations) {
      return {
        ...message,
        toolInvocations: message.toolInvocations.map((toolInvocation) => {
          const toolResult = toolMessage.content.find(
            (tool) => tool.toolCallId === toolInvocation.toolCallId
          );

          if (toolResult) {
            return {
              ...toolInvocation,
              state: "result",
              result: toolResult.result,
            };
          }

          return toolInvocation;
        }),
      };
    }

    return message;
  });
}

export function convertToUIMessages(
  messages: Array<DBMessage>
): Array<ExtendedMessage> {
  return messages.reduce((chatMessages: Array<ExtendedMessage>, message) => {
    if (message.role === "tool") {
      return addToolMessageToChat({
        toolMessage: message as CoreToolMessage,
        messages: chatMessages,
      });
    }

    let textContent = "";
    const toolInvocations: Array<ToolInvocation> = [];

    if (typeof message.content === "string") {
      textContent = message.content;
    } else if (Array.isArray(message.content)) {
      for (const content of message.content) {
        if (content.type === "text") {
          textContent += content.text;
        } else if (content.type === "tool-call") {
          toolInvocations.push({
            state: "call",
            toolCallId: content.toolCallId,
            toolName: content.toolName,
            args: content.args,
          });
        }
      }
    }

    chatMessages.push({
      sources: [],
      id: message.id,
      role: message.role as Message["role"],
      content: textContent,
      createdAt: message.createdAt,
      metadata: message.metadata,
      toolInvocations,
      experimental_attachments: message.experimental_attachments,
    });

    return chatMessages;
  }, []);
}

export function sanitizeResponseMessages(
  messages: Array<CoreToolMessage | CoreAssistantMessage>
): Array<CoreToolMessage | CoreAssistantMessage> {
  const toolResultIds: Array<string> = [];

  for (const message of messages) {
    if (message.role === "tool") {
      for (const content of message.content) {
        if (content.type === "tool-result") {
          toolResultIds.push(content.toolCallId);
        }
      }
    }
  }

  const messagesBySanitizedContent = messages.map((message) => {
    if (message.role !== "assistant") return message;

    if (typeof message.content === "string") return message;

    return {
      ...message,
      content: message.content,
    };
  });

  return messagesBySanitizedContent.filter(
    (message) => message.content.length > 0
  );
}

export function sanitizeUIMessages(messages: Array<Message>): Array<Message> {
  const messagesBySanitizedToolInvocations = messages.map((message) => {
    if (message.role !== "assistant") return message;

    if (!message.toolInvocations) return message;

    const toolResultIds: Array<string> = [];

    for (const toolInvocation of message.toolInvocations) {
      if (toolInvocation.state === "result") {
        toolResultIds.push(toolInvocation.toolCallId);
      }
    }

    const sanitizedToolInvocations = message.toolInvocations.filter(
      (toolInvocation) =>
        toolInvocation.state === "result" ||
        toolResultIds.includes(toolInvocation.toolCallId)
    );

    return {
      ...message,
      toolInvocations: sanitizedToolInvocations,
    };
  });

  return messagesBySanitizedToolInvocations.filter(
    (message) =>
      message.content.length > 0 ||
      (message.toolInvocations && message.toolInvocations.length > 0)
  );
}

export function getMostRecentUserMessage(
  messages: Array<{
    role: string;
    content: any;
    experimental_attachments?: any[];
  }>
) {
  const userMessages = messages.filter((message) => message.role === "user");
  return userMessages.at(-1);
}

export function getDocumentIdsFromLastUserMessage(
  messages: Array<Message>
): string[] {
  // Extracts documentIds of the attachments in the lastest user message
  const userMessages = messages.filter((message) => message.role === "user");
  const lastUserMessage = userMessages.at(-1);
  return (
    lastUserMessage?.experimental_attachments
      ?.map((attachment) => attachment.document_id)
      .filter((id): id is string => id !== undefined) ?? []
  );
}

export function getDocumentTimestampByIndex(
  documents: Array<DocumentType>,
  index: number
) {
  if (!documents) return new Date();
  if (index > documents.length) return new Date();

  return documents[index].createdAt;
}

export function getMessageIdFromAnnotations(message: Message) {
  if (!message.annotations) return message.id;

  const [annotation] = message.annotations;
  if (!annotation) return message.id;

  // @ts-expect-error messageIdFromServer is not defined in MessageAnnotation
  return annotation.messageIdFromServer;
}

const inferContentType = (url?: string, name?: string): string => {
  const extractExtension = (str: string) =>
    str.split(".").pop()?.toLowerCase() || "";

  const contentTypes: Record<string, string> = {
    pdf: "application/pdf",
    jpg: "image/jpg",
    jpeg: "image/jpeg",
    png: "image/png",
  };

  return (
    contentTypes[extractExtension(url || "")] ||
    contentTypes[extractExtension(name || "")] ||
    ""
  );
};

/**
 * Extracts the document ID from a file URL
 * URL format: https://bucket.s3.region.amazonaws.com/uploads/{documentId}.{extension}
 * @param url The file URL
 * @returns The document ID or null if not found
 */
/**
 * Normalizes MIME types to fit within database constraints (50 characters)
 * while preserving functionality for document processing
 */
export function normalizeMimeType(mimeType: string): string {
  const mimeTypeMap: Record<string, string> = {
    "application/vnd.openxmlformats-officedocument.wordprocessingml.document": "application/docx",
    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet": "application/xlsx",
    "application/vnd.openxmlformats-officedocument.presentationml.presentation": "application/pptx",
    "application/msword": "application/doc",
    "application/vnd.ms-excel": "application/xls",
    "application/vnd.ms-powerpoint": "application/ppt"
  };

  return mimeTypeMap[mimeType] || mimeType;
}

/**
 * Expands normalized MIME types back to their full form for processing
 */
export function expandMimeType(normalizedType: string): string {
  const expansionMap: Record<string, string> = {
    "application/docx": "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    "application/xlsx": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    "application/pptx": "application/vnd.openxmlformats-officedocument.presentationml.presentation",
    "application/doc": "application/msword",
    "application/xls": "application/vnd.ms-excel",
    "application/ppt": "application/vnd.ms-powerpoint"
  };

  return expansionMap[normalizedType] || normalizedType;
}

/**
 * File type validation for prompt uploads
 */
export const ALLOWED_PROMPT_FILE_TYPES = {
  // Document types
  'application/pdf': { extension: '.pdf', category: 'document' },
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document': { extension: '.docx', category: 'document' },
  'application/msword': { extension: '.doc', category: 'document' },
  'text/plain': { extension: '.txt', category: 'document' },

  // Spreadsheet types
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': { extension: '.xlsx', category: 'spreadsheet' },
  'application/vnd.ms-excel': { extension: '.xls', category: 'spreadsheet' },

  // Presentation types
  'application/vnd.openxmlformats-officedocument.presentationml.presentation': { extension: '.pptx', category: 'presentation' },
  'application/vnd.ms-powerpoint': { extension: '.ppt', category: 'presentation' },

  // Image types
  'image/png': { extension: '.png', category: 'image' },
  'image/jpeg': { extension: '.jpg', category: 'image' },
  'image/jpg': { extension: '.jpg', category: 'image' }, // Some browsers use this
} as const;

export const ALLOWED_PROMPT_FILE_EXTENSIONS = [
  '.pdf', '.docx', '.doc', '.txt', '.xlsx', '.xls', '.ppt', '.pptx', '.png', '.jpg', '.jpeg'
] as const;

export const PROMPT_FILE_ACCEPT_STRING = '.pdf,.docx,.doc,.txt,.xlsx,.xls,.ppt,.pptx,.png,.jpg,.jpeg';

/**
 * Validates if a file type is allowed for prompt uploads
 */
export function validatePromptFileType(file: File): { isValid: boolean; error?: string } {
  // Check by MIME type first
  if (file.type && ALLOWED_PROMPT_FILE_TYPES[file.type as keyof typeof ALLOWED_PROMPT_FILE_TYPES]) {
    return { isValid: true };
  }

  // Check by file extension as fallback
  const extension = '.' + file.name.split('.').pop()?.toLowerCase();
  if (ALLOWED_PROMPT_FILE_EXTENSIONS.includes(extension as any)) {
    return { isValid: true };
  }

  return {
    isValid: false,
    error: 'Unsupported file type. Only PDF, DOCX, DOC, TXT, Excel, PowerPoint, PNG, and JPG files are allowed.'
  };
}

/**
 * Validates multiple files for prompt uploads
 */
export function validatePromptFiles(files: File[]): { validFiles: File[]; errors: string[] } {
  const validFiles: File[] = [];
  const errors: string[] = [];

  for (const file of files) {
    const validation = validatePromptFileType(file);
    if (validation.isValid) {
      validFiles.push(file);
    } else {
      errors.push(`${file.name}: ${validation.error}`);
    }
  }

  return { validFiles, errors };
}

export function extractDocumentIdFromUrl(url: string): string | null {
  try {
    const urlObj = new URL(url);
    const pathname = urlObj.pathname;

    // Extract the filename from the path (e.g., "/uploads/documentId.pdf" -> "documentId.pdf")
    const filename = pathname.split('/').pop();
    if (!filename) {
      return null;
    }

    // Remove the extension to get the document ID (e.g., "documentId.pdf" -> "documentId")
    const documentId = filename.split('.')[0];

    // Validate that it looks like a UUID (basic check)
    if (documentId && documentId.length >= 32) {
      return documentId;
    }

    return null;
  } catch (error) {
    console.error('Error extracting document ID from URL:', error);
    return null;
  }
}

export const fixMessageAttachments = (
  messages: Array<Message>
): Array<Message> => {
  // Process message attachments to infer and add content types if they are missing
  // Remove attachments for which content type could not be inferred
  return messages.map((msg) => ({
    ...msg,
    experimental_attachments: msg.experimental_attachments?.filter((att) => {
      if (!att.contentType) {
        att.contentType = inferContentType(att.url, att.name);
        return !!att.contentType;
      }
      return true;
    }),
  }));
};

// Helper function to capitalize text
export const capitalizeFirstLetter = (text: string): string => {
  // Capitalize the first letter of each sentence
  return text.replace(/(^\s*\w|[.!?]\s*\w)/g, (match) => match.toUpperCase());
};

export const formatTime = (dateString: Date | string) => {
  return new Date(dateString).toLocaleDateString("en-US", {
    month: "short",
    day: "numeric",
    year: "numeric",
    hour: "2-digit",
    minute: "2-digit",
    hour12: true,
  });
};
