import { PendoProvider } from "@/app/pendo-provider";
import { SentryProvider } from "@/app/sentry-provider";
import { UserProvider } from '@/contexts/UserContext';
import { auth } from "../../(auth)/auth";
// import Script from "next/script";

export const experimental_ppr = true;

export default async function Layout({
  children,
}: {
  children: React.ReactNode;
}) {
  const session = await auth();

  return (
    <UserProvider user={session?.user}>
      <PendoProvider user={session?.user}>
        <SentryProvider user={session?.user}>
          {children}
        </SentryProvider>
      </PendoProvider>
    </UserProvider >
  );
}
