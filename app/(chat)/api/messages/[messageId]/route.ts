import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/app/(auth)/auth";
import { db } from "@/lib/db";
import { message } from "@/lib/db/schema";
import { eq } from "drizzle-orm";
import { Logger } from "@/lib/utils/Logger";

/**
 * GET /api/messages/[messageId]
 * Retrieves a specific message by ID, used for polling citation updates
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ messageId: string }> }
) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { messageId } = await params;

    if (!messageId) {
      return NextResponse.json({ error: "Message ID is required" }, { status: 400 });
    }

    // Validate UUID format
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    if (!uuidRegex.test(messageId)) {
      Logger.warn("Invalid UUID format for messageId", {
        messageId,
        isValidUuid: false,
        receivedType: typeof messageId,
        receivedLength: messageId.length
      });
      return NextResponse.json({
        error: "Invalid message ID format. Expected UUID format."
      }, { status: 400 });
    }

    // Get the message from database
    const [messageRecord] = await db
      .select()
      .from(message)
      .where(eq(message.id, messageId))
      .limit(1);

    if (!messageRecord) {
      return NextResponse.json({ error: "Message not found" }, { status: 404 });
    }

    // Log the message data for debugging
    const metadata = messageRecord.metadata as any;
    Logger.info("Fetching message for citation polling", {
      messageId: messageRecord.id,
      hasMetadata: !!messageRecord.metadata,
      citationStatus: metadata?.inlineCitations?.asyncStatus?.status || "none",
      citationTriggered: metadata?.inlineCitations?.triggered || false,
      citationCount: metadata?.inlineCitations?.citationCount || 0
    });

    // Return the message with current content and metadata
    return NextResponse.json({
      id: messageRecord.id,
      content: messageRecord.content,
      metadata: messageRecord.metadata,
      updatedAt: messageRecord.createdAt,
    });

  } catch (error) {
    const { messageId } = await params;
    Logger.error("Error fetching message", { error, messageId });
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
