'use client';

import React from 'react';
import { MarkdownWithInteractiveCitations } from '@/components/markdown-with-interactive-citations';
import { CitationMatch } from '@/lib/utils/citation-formatter';
import { TooltipProvider } from '@/components/ui/tooltip';

// Mock citation data for testing
const mockCitations: CitationMatch[] = [
  {
    chunkId: "chunk-abc123",
    similarity: 0.85,
    chunkText: "GDPR compliance requires organizations to implement appropriate technical measures.",
    chunkPreview: "GDPR compliance requires organizations to implement appropriate technical and organizational measures to ensure data protection. This includes encryption, access controls, and regular security assessments.",
    pageNumber: 15,
    sourceDocumentId: "doc-456",
    filename: "GDPR_Guidelines_2023.pdf"
  },
  {
    chunkId: "chunk-def456",
    similarity: 0.78,
    chunkText: "Data subjects have rights under GDPR including access, rectification, and erasure.",
    chunkPreview: "Data subjects have rights under GDPR including access, rectification, and erasure. Organizations must provide clear mechanisms for exercising these rights.",
    pageNumber: 23,
    sourceDocumentId: "doc-456",
    filename: "GDPR_Guidelines_2023.pdf"
  },
  {
    chunkId: "chunk-ghi789",
    similarity: 0.72,
    chunkText: "Privacy by design is required for all new systems and processes.",
    chunkPreview: "Privacy by design is required for all new systems and processes. This means implementing data protection measures from the earliest design stages.",
    pageNumber: 8,
    sourceDocumentId: "doc-789",
    filename: "Privacy_Best_Practices.pdf"
  }
];

// Test content with citation markers
const testContent = `# GDPR Compliance Guide

Organizations must implement appropriate technical measures[^1] to ensure data protection. Data subjects have specific rights[^2] that must be respected.

Privacy by design[^3] should be implemented from the start of any project.

## Key Requirements

- Technical and organizational measures
- Data subject rights
- Privacy by design principles

## Additional Information

This guide covers the essential aspects of GDPR compliance[^1] and provides practical guidance for organizations.

The implementation of privacy by design[^3] is crucial for modern data protection strategies.`;

export default function TestCitationsPage() {
  return (
    <TooltipProvider>
      <div className="container mx-auto max-w-4xl p-8">
        <h1 className="text-3xl font-bold mb-8">Citation Rendering Test</h1>

        <div className="space-y-8">
          <div className="bg-card p-6 rounded-lg border">
            <h2 className="text-xl font-semibold mb-4">Test 1: With Citation Data</h2>
            <p className="text-sm text-muted-foreground mb-4">
              This should show superscript markers (¹, ², ³) with clickable tooltips.
            </p>
            <div className="prose dark:prose-invert max-w-none">
              <MarkdownWithInteractiveCitations
                role="assistant"
                citations={mockCitations}
              >
                {testContent}
              </MarkdownWithInteractiveCitations>
            </div>
          </div>

          <div className="bg-card p-6 rounded-lg border">
            <h2 className="text-xl font-semibold mb-4">Test 2: Without Citation Data</h2>
            <p className="text-sm text-muted-foreground mb-4">
              This should show fallback markers since no citation data is provided.
            </p>
            <div className="prose dark:prose-invert max-w-none">
              <MarkdownWithInteractiveCitations
                role="assistant"
                citations={[]}
              >
                {testContent}
              </MarkdownWithInteractiveCitations>
            </div>
          </div>

          <div className="bg-card p-6 rounded-lg border">
            <h2 className="text-xl font-semibold mb-4">Test 3: Partial Citation Data</h2>
            <p className="text-sm text-muted-foreground mb-4">
              This should show mixed rendering - some with tooltips, some without.
            </p>
            <div className="prose dark:prose-invert max-w-none">
              <MarkdownWithInteractiveCitations
                role="assistant"
                citations={[mockCitations[0]]} // Only first citation
              >
                {testContent}
              </MarkdownWithInteractiveCitations>
            </div>
          </div>

          <div className="bg-card p-6 rounded-lg border">
            <h2 className="text-xl font-semibold mb-4">Debug Information</h2>
            <div className="text-sm space-y-2">
              <p><strong>Citation Count:</strong> {mockCitations.length}</p>
              <p><strong>Citation Markers in Content:</strong> {(testContent.match(/\[\^(\d+)\]/g) || []).length}</p>
              <p><strong>Markers Found:</strong> {(testContent.match(/\[\^(\d+)\]/g) || []).join(', ')}</p>
              <p><strong>Citation Files:</strong> {mockCitations.map(c => c.filename).join(', ')}</p>
            </div>
          </div>
        </div>

        <div className="mt-8 p-4 bg-muted rounded-lg">
          <h3 className="font-semibold mb-2">Expected Behavior:</h3>
          <ul className="text-sm space-y-1">
            <li>• Citation markers [^1], [^2], [^3] should be replaced with [1], [2], [3]</li>
            <li>• Clicking on citation markers should show tooltips with citation data</li>
            <li>• Tooltips should contain filename, page number, and preview text</li>
            <li>• If citation data is missing, fallback markers should be shown</li>
          </ul>
        </div>
      </div>
    </TooltipProvider>
  );
}
