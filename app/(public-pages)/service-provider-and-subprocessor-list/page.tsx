import { <PERSON><PERSON> } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";
import Link from "next/link";
import React from "react";

export default function ServiceProviderAndSubprocessorListPage() {
  return (
    <div className="w-4/5 m-auto px-6 py-10 text-gray-800 whitespace-pre-line mb-4 dark:text-gray-300">
      <h1 className="text-3xl font-bold mb-2 text-[#270f57] dark:text-[#bdb1e3]">
        Iqidis Service Providers & Subprocessors
      </h1>
      <p className="text-sm mb-8 dark:text-gray-400">
        Last Updated: May 13, 2025
      </p>

      <section id="section-main" className="mb-8 pt-2">
        <p className="mb-4">
          Iqidis engages trusted third-party service providers and subprocessors
          to support the secure and effective delivery of its legal AI services.
          This policy outlines those entities and the standards that govern
          their engagement.
        </p>

        <h3 className="text-lg font-semibold mb-2 text-[#270f57] dark:text-[#bdb1e3]">
          Definitions
        </h3>
        <ul className="list-disc list-inside ml-4 mt-3 space-y-2 mb-6">
          <li>
            <strong>Service Providers</strong> support the general operation and
            infrastructure of the Iqidis platform.
          </li>
          <li>
            <strong>Subprocessors</strong> process Customer Data and Content as
            part of delivering specific features or functionalities of the
            Services.
          </li>
        </ul>
      </section>

      <section id="section-1" className="mb-8 pt-2">
        <h2 className="text-xl font-semibold mb-2 text-[#270f57] dark:text-[#bdb1e3]">
          Service Providers
        </h2>
        <div className="overflow-x-auto mt-4">
          <table className="w-full border-collapse border border-gray-300 dark:border-gray-600">
            <thead>
              <tr className="bg-gray-100 dark:bg-gray-700">
                <th className="border border-gray-300 dark:border-gray-600 px-4 py-2 text-left">
                  Provider
                </th>
                <th className="border border-gray-300 dark:border-gray-600 px-4 py-2 text-left">
                  Service Provided
                </th>
                <th className="border border-gray-300 dark:border-gray-600 px-4 py-2 text-left">
                  Location
                </th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td className="border border-gray-300 dark:border-gray-600 px-4 py-2">
                  Amazon Web Services
                </td>
                <td className="border border-gray-300 dark:border-gray-600 px-4 py-2">
                  Cloud infrastructure, hosting
                </td>
                <td className="border border-gray-300 dark:border-gray-600 px-4 py-2">
                  U.S.
                </td>
              </tr>
              <tr>
                <td className="border border-gray-300 dark:border-gray-600 px-4 py-2">
                  Keycloak
                </td>
                <td className="border border-gray-300 dark:border-gray-600 px-4 py-2">
                  Authentication, identity management
                </td>
                <td className="border border-gray-300 dark:border-gray-600 px-4 py-2">
                  U.S.
                </td>
              </tr>
              <tr>
                <td className="border border-gray-300 dark:border-gray-600 px-4 py-2">
                  Vercel
                </td>
                <td className="border border-gray-300 dark:border-gray-600 px-4 py-2">
                  Web hosting and edge network delivery
                </td>
                <td className="border border-gray-300 dark:border-gray-600 px-4 py-2">
                  U.S.
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </section>

      <section id="section-2" className="mb-8 pt-2">
        <h2 className="text-xl font-semibold mb-2 text-[#270f57] dark:text-[#bdb1e3]">
          Subprocessors
        </h2>
        <div className="overflow-x-auto mt-4">
          <table className="w-full border-collapse border border-gray-300 dark:border-gray-600">
            <thead>
              <tr className="bg-gray-100 dark:bg-gray-700">
                <th className="border border-gray-300 dark:border-gray-600 px-4 py-2 text-left">
                  Subprocessor
                </th>
                <th className="border border-gray-300 dark:border-gray-600 px-4 py-2 text-left">
                  Service Provided
                </th>
                <th className="border border-gray-300 dark:border-gray-600 px-4 py-2 text-left">
                  Location
                </th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td className="border border-gray-300 dark:border-gray-600 px-4 py-2">
                  Vercel
                </td>
                <td className="border border-gray-300 dark:border-gray-600 px-4 py-2">
                  Hosting of AI components and UI delivery
                </td>
                <td className="border border-gray-300 dark:border-gray-600 px-4 py-2">
                  U.S.
                </td>
              </tr>
              <tr>
                <td className="border border-gray-300 dark:border-gray-600 px-4 py-2">
                  OpenAI, L.L.C.
                </td>
                <td className="border border-gray-300 dark:border-gray-600 px-4 py-2">
                  AI processing and generation
                </td>
                <td className="border border-gray-300 dark:border-gray-600 px-4 py-2">
                  U.S. & E.U.
                </td>
              </tr>
              <tr>
                <td className="border border-gray-300 dark:border-gray-600 px-4 py-2">
                  Anthropic (Claude)
                </td>
                <td className="border border-gray-300 dark:border-gray-600 px-4 py-2">
                  AI processing and generation
                </td>
                <td className="border border-gray-300 dark:border-gray-600 px-4 py-2">
                  U.S. & E.U.
                </td>
              </tr>
              <tr>
                <td className="border border-gray-300 dark:border-gray-600 px-4 py-2">
                  Google (Gemini)
                </td>
                <td className="border border-gray-300 dark:border-gray-600 px-4 py-2">
                  AI processing and compute services
                </td>
                <td className="border border-gray-300 dark:border-gray-600 px-4 py-2">
                  U.S. & E.U.
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </section>

      <section id="section-3" className="mb-8 pt-2">
        <h2 className="text-xl font-semibold mb-2 text-[#270f57] dark:text-[#bdb1e3]">
          Subprocessor Standards & Commitments
        </h2>
        <p className="mb-4">
          Iqidis engages subprocessors solely to the extent necessary to deliver
          its Services. These subprocessors may process Customer Data, but only
          under Iqidis&apos;s direction and control.
        </p>
        <p className="mb-4">
          Subprocessors are required—via written agreements, standard
          contractual terms, or binding service-level terms—to:
        </p>
        <ul className="list-disc list-inside ml-4 mt-3 space-y-2">
          <li>Maintain the confidentiality of Customer Data.</li>
          <li>
            Comply with applicable data protection laws, including the GDPR
            where relevant.
          </li>
          <li>
            Implement appropriate technical and organizational safeguards such
            as encryption, access controls, and audit logging.
          </li>
          <li>
            Use Customer Data exclusively for providing services under Iqidis&apos;s
            direction and not for model training or unrelated purposes.
          </li>
        </ul>
        <p className="mt-4">
          Where possible, Iqidis enters into direct agreements with
          subprocessors. In other cases, Iqidis relies on industry-standard
          binding terms and privacy commitments.
        </p>
      </section>

      <section id="section-4" className="mb-8 pt-2">
        <h2 className="text-xl font-semibold mb-2 text-[#270f57] dark:text-[#bdb1e3]">
          Additional Commitments
        </h2>
        <ul className="list-disc list-inside ml-4 mt-3 space-y-3">
          <li>
            <strong>Data Control:</strong> Iqidis remains the controller of
            Customer Data. Subprocessors operate only on documented
            instructions.
          </li>
          <li>
            <strong>Geographic Routing:</strong> Data is processed primarily in
            the U.S. and EU. Regional routing may be adjusted based on client
            needs or regulatory obligations.
          </li>
          <li>
            <strong>AI Model Training:</strong> Customer Content is{" "}
            <strong>never</strong> used to train Iqidis models or third-party
            foundation models.
          </li>
          <li>
            <strong>System Architecture:</strong> Iqidis uses a proprietary
            multi-agent AI framework to manage AI tasks securely, intelligently,
            and efficiently.
          </li>
        </ul>
      </section>

      <section id="section-contact" className="mb-8 pt-2">
        <h2 className="text-xl font-semibold mb-2 text-[#270f57] dark:text-[#bdb1e3]">
          Questions or Requests
        </h2>
        <p>
          Clients may contact{" "}
          <Link
            href="mailto:<EMAIL>"
            target="_blank"
            className="text-blue-600 hover:underline"
          >
            <EMAIL>
          </Link>{" "}
          to request additional information about subprocessors or raise
          objections to specific engagements, where applicable by contract or
          law.
        </p>
      </section>
    </div>
  );
}
