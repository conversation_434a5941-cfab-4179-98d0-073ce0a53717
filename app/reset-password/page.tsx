'use client';

import { useState, useEffect, useRef } from 'react';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import { Label } from '@/components/ui/label';
import { ArrowLeft } from 'lucide-react';
import Link from 'next/link';
import { Loader2 } from 'lucide-react';
import { LogoSignup } from '@/components/icons';
import { useTheme } from 'next-themes';
import MeshBackground from '@/components/mesh-background';
import { motion } from 'framer-motion';
import { Eye, EyeOff } from "lucide-react";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { AlertCircle } from "lucide-react";

const FloatingParticles = () => {
  const { resolvedTheme } = useTheme();
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const animationRef = useRef<number>();
  
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    if (!ctx) return;
    
    // Set canvas dimensions
    const setCanvasDimensions = () => {
      canvas.width = window.innerWidth;
      canvas.height = window.innerHeight;
    };
    
    setCanvasDimensions();
    window.addEventListener('resize', setCanvasDimensions);
    
    // Create particles
    const particleCount = 50;
    const particles: {
      x: number;
      y: number;
      size: number;
      speedX: number;
      speedY: number;
      opacity: number;
      color: string;
    }[] = [];
    
    for (let i = 0; i < particleCount; i++) {
      const isDark = resolvedTheme === 'dark';
      const hue = isDark ? Math.random() * 60 + 220 : Math.random() * 60 + 240; // Blue/purple hues
      const saturation = isDark ? '70%' : '80%';
      const lightness = isDark ? '60%' : '70%';
      const alpha = Math.random() * 0.3 + 0.1;
      
      particles.push({
        x: Math.random() * canvas.width,
        y: Math.random() * canvas.height,
        size: Math.random() * 4 + 1,
        speedX: (Math.random() - 0.5) * 0.3,
        speedY: (Math.random() - 0.5) * 0.3,
        opacity: alpha,
        color: `hsla(${hue}, ${saturation}, ${lightness}, ${alpha})`
      });
    }
    
    // Animation loop
    const animate = () => {
      ctx.clearRect(0, 0, canvas.width, canvas.height);
      
      particles.forEach(particle => {
        // Update position
        particle.x += particle.speedX;
        particle.y += particle.speedY;
        
        // Wrap around edges
        if (particle.x < 0) particle.x = canvas.width;
        if (particle.x > canvas.width) particle.x = 0;
        if (particle.y < 0) particle.y = canvas.height;
        if (particle.y > canvas.height) particle.y = 0;
        
        // Draw particle
        ctx.beginPath();
        ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
        ctx.fillStyle = particle.color;
        ctx.fill();
      });
      
      animationRef.current = requestAnimationFrame(animate);
    };
    
    animate();
    
    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
      window.removeEventListener('resize', setCanvasDimensions);
    };
  }, [resolvedTheme]);
  
  return (
    <canvas 
      ref={canvasRef} 
      className="absolute inset-0 pointer-events-none z-0"
      aria-hidden="true"
    />
  );
};

export default function ResetPasswordPage() {
  const router = useRouter();
  const { resolvedTheme } = useTheme();
  const [oldPassword, setOldPassword] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [passwordError, setPasswordError] = useState('');
  const [mounted, setMounted] = useState(false);
  const [passwordCriteria, setPasswordCriteria] = useState({
    minLength: false,
    hasUppercase: false,
    hasLowercase: false,
    hasNumber: false,
    hasSpecial: false,
  });
  const [showOldPassword, setShowOldPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  // Password validation
  useEffect(() => {
    if (newPassword) {
      setPasswordCriteria({
        minLength: newPassword.length >= 8,
        hasUppercase: /[A-Z]/.test(newPassword),
        hasLowercase: /[a-z]/.test(newPassword),
        hasNumber: /[0-9]/.test(newPassword),
        hasSpecial: /[^A-Za-z0-9]/.test(newPassword),
      });
    }

    if (confirmPassword && newPassword !== confirmPassword) {
      setPasswordError('Passwords do not match');
    } else {
      setPasswordError('');
    }
  }, [newPassword, confirmPassword]);

  const handleResetPassword = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    // Validate passwords match
    if (newPassword !== confirmPassword) {
      toast.dismiss();
      toast.error('Passwords do not match');
      return;
    }

    // Validate password meets criteria
    const allCriteriaMet = Object.values(passwordCriteria).every(Boolean);
    if (!allCriteriaMet) {
      toast.dismiss();
      toast.error('Password does not meet all requirements');
      return;
    }

    try {
      setLoading(true);

      const response = await fetch('/api/password/reset', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
        
          oldPassword,
          newPassword 
        })
      });

      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.error || 'Failed to reset password');
      }
      toast.dismiss();
      toast.success('Password successfully updated');
      // Clear form fields after successful update
      setOldPassword('');
      setNewPassword('');
      setConfirmPassword('');
      
      setTimeout(() => {
        router.push('/login');
      }, 2000);
    } catch (error) {
      console.error('Password reset error:', error);
      if (error instanceof Error) {
        toast.dismiss();
        toast.error(error.message);
      } else {
        toast.dismiss();
        toast.error('An unexpected error occurred');
      }
    } finally {
      setLoading(false);
    }
  };

  if (!mounted) return null;

  return (
    <div className="flex lg:h-dvh h-full w-screen items-start py-12 md:py-0 md:items-center justify-center bg-background relative resetContainer">
      <FloatingParticles />
      
      <motion.div 
        className="w-full max-w-md overflow-hidden rounded-2xl flex flex-col gap-8 bg-white/70 dark:bg-slate-800/70 backdrop-blur-md border border-white/30 dark:border-gray-700 shadow-lg p-8 z-10 relative"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, ease: "easeOut" }}
      >
        <div className="flex flex-col items-center justify-center gap-2 text-center">
          <motion.div 
            className="mb-2"
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.1, duration: 0.5 }}
          >
            <LogoSignup
              size={140}
              mixBlendMode={resolvedTheme === "dark" ? "lighten" : "multiply"}
              isDark={resolvedTheme === "dark"}
            />
          </motion.div>
          
          <motion.h3 
            className="text-2xl font-semibold font-playfair dark:text-zinc-50"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.2, duration: 0.5 }}
          >
            Reset Your Password
          </motion.h3>
          <motion.p 
            className="text-sm text-muted-foreground"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.3, duration: 0.5 }}
          >
            Enter your current password and a new password below
          </motion.p>
        </div>
        
        <motion.form 
          onSubmit={handleResetPassword} 
          className="flex flex-col gap-4"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.4, duration: 0.5 }}
        >
          <div className="flex flex-col gap-1.5">
            <Label htmlFor="oldPassword">Current Password</Label>
            <div className="relative">
              <input
                id="oldPassword"
                name="oldPassword"
                type={showOldPassword ? "text" : "password"}
                value={oldPassword}
                onChange={(e) => setOldPassword(e.target.value)}
                placeholder="Enter your current password"
                required
                className="w-full p-3 border rounded bg-card/80 text-card-foreground dark:bg-gray-800/60 dark:border-gray-700 focus:ring-2 focus:ring-primary focus:outline-none transition-all duration-200 pr-10"
              />
              <button
                type="button"
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
                onClick={() => setShowOldPassword(!showOldPassword)}
                aria-label={showOldPassword ? "Hide password" : "Show password"}
              >
                {showOldPassword ? <EyeOff size={18} /> : <Eye size={18} />}
              </button>
            </div>
          </div>
          <div className="flex flex-col gap-1.5">
            <Label htmlFor="newPassword">New Password</Label>
            <div className="relative">
              <input
                id="newPassword"
                name="newPassword"
                type={showNewPassword ? "text" : "password"}
                value={newPassword}
                onChange={(e) => setNewPassword(e.target.value)}
                placeholder="Enter your new password"
                required
                className="w-full p-3 border rounded bg-card/80 text-card-foreground dark:bg-gray-800/60 dark:border-gray-700 focus:ring-2 focus:ring-primary focus:outline-none transition-all duration-200 pr-10"
              />
              <button
                type="button"
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
                onClick={() => setShowNewPassword(!showNewPassword)}
                aria-label={showNewPassword ? "Hide password" : "Show password"}
              >
                {showNewPassword ? <EyeOff size={18} /> : <Eye size={18} />}
              </button>
            </div>
          </div>
          <div className="flex flex-col gap-1.5">
            <Label htmlFor="confirmPassword">Confirm New Password</Label>
            <div className="relative">
              <input
                id="confirmPassword"
                name="confirmPassword"
                type={showConfirmPassword ? "text" : "password"}
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
                placeholder="Confirm your new password"
                required
                className="w-full p-3 border rounded bg-card/80 text-card-foreground dark:bg-gray-800/60 dark:border-gray-700 focus:ring-2 focus:ring-primary focus:outline-none transition-all duration-200 pr-10"
              />
              <button
                type="button"
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
                onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                aria-label={showConfirmPassword ? "Hide password" : "Show password"}
              >
                {showConfirmPassword ? <EyeOff size={18} /> : <Eye size={18} />}
              </button>
            </div>
            {passwordError && <p className="text-sm text-destructive">{passwordError}</p>}
          </div>

          {/* Password criteria section */}
          {newPassword.length > 0 && (
            <TooltipProvider delayDuration={0}>
              <Tooltip>
                <TooltipTrigger asChild>
                  <div className="flex items-center mt-1 text-xs">
                    <AlertCircle 
                      size={14} 
                      className={`mr-1 ${
                        Object.values(passwordCriteria).every(Boolean)
                          ? "text-green-500"
                          : "text-amber-500"
                      }`} 
                    />
                    <span>
                      {Object.values(passwordCriteria).every(Boolean)
                        ? "Password meets all requirements"
                        : "Password requirements"}
                    </span>
                  </div>
                </TooltipTrigger>
                <TooltipContent side="right" className="w-64 p-2">
                  <ul className="space-y-1 text-xs">
                    <li className={`flex items-center ${passwordCriteria.minLength ? "text-green-500" : "text-muted-foreground"}`}>
                      <span className="mr-1">{passwordCriteria.minLength ? "✓" : "○"}</span>
                      At least 8 characters
                    </li>
                    <li className={`flex items-center ${passwordCriteria.hasUppercase ? "text-green-500" : "text-muted-foreground"}`}>
                      <span className="mr-1">{passwordCriteria.hasUppercase ? "✓" : "○"}</span>
                      One uppercase letter
                    </li>
                    <li className={`flex items-center ${passwordCriteria.hasLowercase ? "text-green-500" : "text-muted-foreground"}`}>
                      <span className="mr-1">{passwordCriteria.hasLowercase ? "✓" : "○"}</span>
                      One lowercase letter
                    </li>
                    <li className={`flex items-center ${passwordCriteria.hasNumber ? "text-green-500" : "text-muted-foreground"}`}>
                      <span className="mr-1">{passwordCriteria.hasNumber ? "✓" : "○"}</span>
                      One number
                    </li>
                    <li className={`flex items-center ${passwordCriteria.hasSpecial ? "text-green-500" : "text-muted-foreground"}`}>
                      <span className="mr-1">{passwordCriteria.hasSpecial ? "✓" : "○"}</span>
                      One special character
                    </li>
                  </ul>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          )}

          <motion.button
            type="submit"
            disabled={loading || !oldPassword || !newPassword || !confirmPassword || Boolean(passwordError)}
            className="mt-4 w-full bg-primary text-primary-foreground py-3 px-4 rounded hover:bg-primary hover:shadow-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:bg-primary disabled:hover:shadow-none disabled:hover:text-primary-foreground font-medium"
            whileHover={{ scale: loading || !oldPassword || !newPassword || !confirmPassword || Boolean(passwordError) ? 1 : 1.02 }}
            whileTap={{ scale: loading || !oldPassword || !newPassword || !confirmPassword || Boolean(passwordError) ? 1 : 0.98 }}
          >
            {loading ? (
              <div className="flex items-center justify-center">
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Updating...
              </div>
            ) : (
              "Update Password"
            )}
          </motion.button>
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.5, duration: 0.5 }}
          >
            <Link href="/" className="flex items-center justify-center text-sm text-muted-foreground hover:text-foreground mt-2 transition-colors">
              <ArrowLeft className="h-4 w-4 mr-1" /> Back to Home
            </Link>
          </motion.div>
        </motion.form>
      </motion.div>
    </div>
  );
}
