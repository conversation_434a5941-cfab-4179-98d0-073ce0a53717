import type { Metada<PERSON> } from 'next';
import { Toaster } from 'sonner';
import { Analytics } from '@vercel/analytics/next';
import { PHProvider } from './providers';
import { ThemeProvider } from '@/components/theme-provider';
import { SpeedInsights } from '@vercel/speed-insights/next';
import { AntdRegistry } from '@ant-design/nextjs-registry';
import { TooltipProvider } from '@/components/ui/tooltip';
import MixpanelProvider from '@/components/MixpanelProvider';

import './globals.css';
import { ConfigProvider } from 'antd';

export const metadata: Metadata = {
  title: 'Iqidis Core - Your AI Associate',
  description: 'Your new legal AI assistant.',
};

export const viewport = {
  maximumScale: 1, // Disable auto-zoom on mobile Safari
};

const LIGHT_THEME_COLOR = 'hsl(0 0% 100%)';
const DARK_THEME_COLOR = 'hsl(240deg 10% 3.92%)';
const THEME_COLOR_SCRIPT = `\
(function() {
  var html = document.documentElement;
  var meta = document.querySelector('meta[name="theme-color"]');
  if (!meta) {
    meta = document.createElement('meta');
    meta.setAttribute('name', 'theme-color');
    document.head.appendChild(meta);
  }
  function updateThemeColor() {
    var isDark = html.classList.contains('dark');
    meta.setAttribute('content', isDark ? '${DARK_THEME_COLOR}' : '${LIGHT_THEME_COLOR}');
  }
  var observer = new MutationObserver(updateThemeColor);
  observer.observe(html, { attributes: true, attributeFilter: ['class'] });
  updateThemeColor();
})();`;

export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {

  return (
    <html
      lang="en"
      // `next-themes` injects an extra classname to the body element to avoid
      // visual flicker before hydration. Hence the `suppressHydrationWarning`
      // prop is necessary to avoid the React hydration mismatch warning.
      // https://github.com/pacocoursey/next-themes?tab=readme-ov-file#with-app
      suppressHydrationWarning
    >
      <meta name="viewport" content="width=device-width, initial-scale=1" />
      <head>

        <script
          dangerouslySetInnerHTML={{
            __html: THEME_COLOR_SCRIPT,
          }}
        />
      </head>
      <PHProvider>
        <body className="antialiased">
          <ThemeProvider
            attribute="class"
            defaultTheme="light"
            enableSystem
            disableTransitionOnChange
          >
            <TooltipProvider delayDuration={300}>
              <AntdRegistry>
                <Toaster
                  position="top-center"
                  visibleToasts={1}
                  expand={false}
                  toastOptions={{
                    style: {
                      minWidth: '320px',
                      padding: '12px',
                    },
                  }}
                />
                <ConfigProvider
                  theme={{
                    components: {
                      Button: {
                        colorPrimary: '#3f2a65',
                        colorPrimaryHover: '#523f7a',
                        colorPrimaryActive: '#322155',
                      },
                      Upload: {
                        colorPrimary: '#3f2a65',
                        colorPrimaryHover: '#523f7a',
                      },
                      Checkbox: {
                        colorPrimary: '#3f2a65',
                        colorPrimaryHover: '#523f7a',
                        colorPrimaryActive: '#322155',
                      },
                      Select: {
                        colorPrimary: '#3f2a65',
                        colorPrimaryHover: '#523f7a',
                        colorPrimaryActive: '#322155',
                      },
                      Radio: {
                        colorPrimary: '#3f2a65',
                        colorPrimaryHover: '#523f7a',
                        colorPrimaryActive: '#322155',
                      },
                      Input: {
                        colorPrimary: '#3f2a65',
                        colorPrimaryHover: '#523f7a',
                        colorPrimaryActive: '#322155',
                      },
                      Table: {
                        cellPaddingBlockMD: 11
                      }
                    }
                  }}
                  wave={{ disabled: true }}
                >
                  <MixpanelProvider />
                  {children}
                </ConfigProvider>
                <SpeedInsights />
                <Analytics />
              </AntdRegistry>
            </TooltipProvider>
          </ThemeProvider>
        </body>
      </PHProvider>
    </html>
  );
}
